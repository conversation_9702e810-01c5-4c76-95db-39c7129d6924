# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a dental clinic management system (BCare/Updental) built with Vue 3, TypeScript, and Vite. The application provides booking, patient management, and clinic operations features for dental practices.

## Essential Commands

```bash
# Development
bun run dev                  # Start development server (uses Vite)

# Building
bun run build               # Production build (default: 'up' mode)
bun run build:san          # Build for 'san' environment
bun run build:up           # Build for 'up' environment

# Code Quality
bun run typecheck          # TypeScript type checking (vue-tsc -noEmit)
bun run lint              # ESLint with auto-fix for .ts,.tsx,.vue,.js,.jsx files
bun run prettier          # Format code with Prettier

# Preview
bun run preview           # Preview production build locally
```

## Architecture Overview

### Tech Stack
- **Framework**: Vue 3 with Composition API
- **Language**: TypeScript (strict mode)
- **Build Tool**: Vite 5.4.6 with Bun package manager
- **State Management**: Pinia stores
- **Routing**: Vue Router 4
- **UI Framework**: PrimeVue 4.2.5 + Tailwind CSS
- **HTTP Client**: Axios
- **Real-time**: WebSocket integration
- **Rich Text**: TipTap editor
- **SIP Integration**: VoiceCenter Team VSIP for call features
- **Utilities**: VueUse for composables, Lodash for utilities, DayJS for date manipulation

### Project Structure

```
src/
├── api/              # API layer with TypeScript types and service definitions
├── base-components/  # Reusable components (Alert, Button, Form, Table, etc.)
├── components/       # Feature-specific components (Deal, Person, Appointment, etc.)
├── composables/      # Vue composables for shared logic
├── hooks/           # Custom hooks (similar to composables, data fetching patterns)
├── pages/           # Page components organized by feature
├── stores/          # Pinia stores for state management
├── router/          # Route definitions (main, mobile, print routes)
└── utils/           # Utility functions
```

### Key Architectural Patterns

1. **Component Organization**:
   - Base components are generic, reusable UI elements
   - Feature components are domain-specific (Deal, Person, Appointment, etc.)
   - Components follow Vue 3 Composition API patterns

2. **Data Fetching**:
   - Custom hooks in `hooks/` directory handle API calls and state
   - Uses composables pattern for data fetching (`useQuery`, `useRawQuery`)
   - Real-time updates via WebSocket (`realtime/websocket.ts`)

3. **State Management**:
   - Pinia stores organized by domain (person-store, deal-store, etc.)
   - Stores follow TypeScript patterns with proper typing

4. **Auto-imports**:
   - Components and composables are auto-imported
   - No need for explicit imports in most cases even vue, vueuse, primevue 's.

5. **Multi-environment Support**:
   - Different build modes for different environments (up, san)
   - Environment variables via `.env.local`

## Development Workflow

1. **Environment Setup**:
   Create `.env.local` with required variables (see README.md for template)

2. **Code Style**:
   - Prettier runs automatically on commit via Husky
   - ESLint fixes common issues on save
   - Follow Vue.js style guide: https://vuejs.org/style-guide/

3. **Type Safety**:
   - Always run `bun run typecheck` before committing
   - API types are defined in `api/bcare-v2.ts` and `api/bcare-types-v2.ts`

4. **Component Development**:
   - Check existing base components before creating new ones
   - Use PrimeVue components when available
   - Follow existing patterns in similar components

## Styling Guidelines

- **Tailwind CSS First**: Use Tailwind utility classes directly in templates. Avoid `<style scoped>` or custom CSS
- **PrimeVue Components**: Use PrimeVue for complex UI
- **PrimeVue Passthrough (PT)**: For customization, apply Tailwind classes via PT instead of global CSS overrides
- **Consistent Spacing**: Use Tailwind's spacing scale for consistent design
- **Color System**: Use semantic colors (`text-primary`, `bg-success`, `text-danger`) based on project theme
- **Typography**: Use with Tailwind font utilities
- **Mobile-First**: Design mobile-first with responsive prefixes (`md:`, `lg:`)
- **Accessibility**: Follow WCAG guidelines with semantic HTML and proper contrast

## Vue 3 + TypeScript Best Practices

### Component Development Patterns

**Script Setup Organization Order:**
1. `import` statements
2. `defineProps`, `defineEmits`, `defineModel`
3. State declaration (`ref`, `reactive`, Pinia stores)
4. `computed` properties
5. `watch` and `watchEffect`
6. Lifecycle hooks (`onMounted`, `onUnmounted`, etc.)
7. Handler functions/methods

**Reactivity Guidelines:**
- **`ref`**: Use for primitives and object references that might be replaced
- **`reactive`**: Use for complex nested objects that will be mutated (not replaced)
- **`computed`**: Use for derived state, prefer over watchers for data transformation
- **`defineModel`**: Use for v-model binding instead of manual props/emits

**Example Component Structure:**
```vue
<script setup lang="ts">
// 1. Imports
import { ref, computed, onMounted } from 'vue'
import { usePatientStore } from '@/stores/patient'

// 2. Props, Emits, Models
interface Props {
  patientId: string
  isArchived?: boolean
}
const props = withDefaults(defineProps<Props>(), { isArchived: false })
const emit = defineEmits<{ (e: 'save', data: Patient): void }>()
const modelValue = defineModel<string>({ required: true })

// 3. State
const patientStore = usePatientStore()
const isEditing = ref(false)

// 4. Computed
const patientFullName = computed(() => 
  patientStore.getPatientById(props.patientId)?.fullName ?? 'Loading...'
)

// 5. Lifecycle
onMounted(() => patientStore.fetchPatient(props.patientId))

// 6. Methods
function handleSave() {
  emit('save', patientStore.currentPatient)
}
</script>
```

### Pinia Store Best Practices

**Store Structure (Setup Syntax):**
```typescript
export const usePatientStore = defineStore('patient', () => {
  // State
  const patients = ref<Patient[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const activePatients = computed(() => 
    patients.value.filter(p => !p.isArchived)
  )

  // Actions
  async function fetchAllPatients() {
    isLoading.value = true
    error.value = null
    try {
      patients.value = await patientApi.getAll()
    } catch (e) {
      error.value = 'Failed to fetch patients'
    } finally {
      isLoading.value = false
    }
  }

  function getPatientById(id: string) {
    return patients.value.find(p => p.id === id)
  }

  return {
    // State
    patients, isLoading, error,
    // Getters  
    activePatients,
    // Actions
    fetchAllPatients, getPatientById
  }
})
```

**Store Organization:**
- One store per domain (patient, appointment, billing)
- Always handle loading/error states in async actions
- Use getters for derived state, actions for mutations
- Stores can compose other stores for cross-domain logic

### Anti-Patterns to Avoid

**Vue 3 Anti-Patterns:**
- Never mutate props directly - emit events instead
- Don't destructure props (breaks reactivity) - use `toRefs()` if needed
- Avoid overusing `watch` - prefer `computed` for derived state
- Don't forget cleanup in `onUnmounted` for manual subscriptions

**Pinia Anti-Patterns:**
- Never mutate store state from components - always use actions
- Avoid "God" stores - split by logical domains
- Don't put component-specific UI state in global stores
- Always handle async errors and loading states

**TypeScript Anti-Patterns:**
- Avoid `any` type - use `unknown` with type guards
- Don't ignore TypeScript errors - fix them properly
- Use proper typing for event payloads and component generics

## Utility Library Usage

- **VueUse**: Preferred for Vue-specific composables (DOM manipulation, lifecycle, reactivity utilities)
- **Lodash**: Use for data transformation, object/array utilities when native JS methods aren't sufficient
- **DayJS**: Preferred for all date manipulation, formatting, and calculations over native Date

## MCP Context7 Integration

**Stay Updated with Latest Documentation:**
- Use MCP Context7 (`mcp context7`) to access current documentation for libraries and frameworks
- Essential for avoiding outdated practices, especially with rapidly evolving libraries like PrimeVue
- Always verify latest API changes and migration guides before implementing new features

**Usage Examples:**
```
# Check latest PrimeVue documentation and best practices
"Get latest PrimeVue 4.x documentation for TypeScript Vue 3 - use context7"

# Verify current Vue 3 Composition API patterns  
"Show Vue 3 Composition API best practices 2025 - use context7"

# Check for breaking changes in dependencies
"Check Pinia store setup syntax latest changes - use context7"
```

**When to Use Context7:**
- Before major dependency updates
- When implementing new library features
- To verify if current patterns are still recommended
- For troubleshooting deprecated APIs or breaking changes

## Important Notes

- **No Test Framework**: The project currently lacks unit/e2e testing setup
- **Vietnamese Localization**: PrimeVue is configured with Vietnamese locale
- **Dental Domain**: Many components are specific to dental clinic operations (teeth selection, treatment plans, etc.)
- **Real-time Features**: WebSocket connection handles live updates for appointments, calls, etc.
- **Build Analysis**: Use `rollup-plugin-visualizer` output to analyze bundle size
