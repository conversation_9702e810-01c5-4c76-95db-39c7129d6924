
# Refactor Proposal: DataTable V2

This document proposes a new architecture (V2) for the `DataTable` component to address issues of complexity, maintainability, and scalability in the current version (V1).

## Summary (TL;DR)

- **Problem:** The current `DataTable` (V1) is a "God Component" tightly coupled with the `useFilterList` hook. It re-implements filter and state management logic that PrimeVue already provides natively. This makes maintenance, extension (e.g., adding sorting), and handling complex filters difficult.
- **Solution:** Separate logic from presentation.
    1.  **Create `useLazyDataTable.ts` (Composable):** A reusable composable responsible for managing all data logic: state (loading, data, total), handling the lazy-loading event (`@page`), and making API calls.
    2.  **Create `DataTableV2.vue` (Dumb Component):** A simple wrapper component focused solely on standardizing the UI (styling, PT, common slots) and passing props/events directly to the underlying PrimeVue DataTable. It will no longer have any awareness of filter logic.
- **Benefits:** Cleaner code, easier maintenance, high scalability, maximum logic reuse, and the ability to effectively leverage 100% of PrimeVue DataTable's power.

---

## 1. Analysis of the Current Architecture (V1)

The V1 architecture consists of two main parts:
- **`@/components/DataTable/DataTable.vue`**: A wrapper component for PrimeVue DataTable.
- **`@/hooks/useFilterList.ts`**: A custom hook to manage filter state and create the API payload.

### Key Issues

1.  **God Component & High Complexity:**
    - `DataTable.vue` accepts over 25 props, making it responsible for presentation, filtering, actions, pagination, and various UI states.
    - Logic for handling filters and pagination is fragmented across multiple places (`useFilterList`, `watch` handlers in the parent component, a separate `handlePageChange` function), making the data flow difficult to trace.

2.  **"Fighting the Framework":**
    - We are manually rebuilding a filter management system (`useFilterList`) while PrimeVue's `lazy` mode already provides a powerful mechanism to manage filters, sorting, and pagination via a single `@page` event.
    - This creates an unnecessary abstraction layer that increases complexity and maintenance overhead.

3.  **Difficult to Scale and Reuse:**
    - Adding a new feature like sorting would require cumbersome modifications in both `DataTable.vue` and its parent components.
    - `useFilterList` is not easily reusable for pages with different API payload structures without custom modifications.

## 2. Proposed Architecture (V2)

The V2 architecture clearly separates **Data Logic** from **Presentation**.

### a. `useLazyDataTable.ts` Composable (The Brain)

This is a reusable composable responsible for all background logic.

**Responsibilities:**
- Manages core state: `data`, `totalRecords`, `loading`, `error`.
- Provides a `ref` for `filters` to be used with `v-model` on the DataTable.
- Provides a single event handler, `onLazyLoad`, to be bound to the DataTable's `@page` event.
- Encapsulates the API call and state update logic.

**Sample Structure:**
```typescript
// src/composables/useLazyDataTable.ts
import { ref, onMounted } from 'vue';
import type { DataTablePageEvent } from 'primevue/datatable';

interface LazyDataTableResponse<T> {
  items: T[];
  total: number;
}

interface UseLazyDataTableOptions<T, TParams> {
  fetchDataFn: (params: TParams) => Promise<LazyDataTableResponse<T>>;
  initialFilters: Record<string, any>;
  mapEventToParams: (event: DataTablePageEvent) => TParams;
}

export function useLazyDataTable<T, TParams>(options: UseLazyDataTableOptions<T, TParams>) {
  const { fetchDataFn, initialFilters, mapEventToParams } = options;

  const data = ref<T[]>([]);
  const totalRecords = ref(0);
  const loading = ref(false);
  const filters = ref(initialFilters);

  const onLazyLoad = async (event: DataTablePageEvent) => {
    loading.value = true;
    const apiParams = mapEventToParams(event);
    const response = await fetchDataFn(apiParams);
    data.value = response.items;
    totalRecords.value = response.total;
    loading.value = false;
  };

  onMounted(() => { /* Initial data load */ });

  return { data, totalRecords, loading, filters, onLazyLoad };
}
```

### b. `DataTableV2.vue` Component (The View)

This is a "dumb component" focused solely on presentation.

**Responsibilities:**
- Acts as a thin wrapper around the PrimeVue DataTable.
- Standardizes the common look-and-feel via `pt` (Passthrough) and default classes.
- Provides common slots (e.g., `#actions`) for reuse.
- **Contains no business logic.** It only receives props and emits events.
- It will transparently pass through all of PrimeVue's native props and slots, allowing us to leverage 100% of the library's power.

## 3. Benefits of the V2 Architecture

1.  **Simplicity & Readability:** Logic is centralized in the composable, while the component focuses on presentation.
2.  **Maximum Reusability (DRY):** `useLazyDataTable` can be used for any data table, only requiring a new `fetchDataFn` and `mapEventToParams` function.
3.  **High Scalability:** Adding features like sorting merely involves handling `event.sortField` in the `mapEventToParams` function, with no architectural changes.
4.  **Adherence to Best Practices:** Correctly and efficiently utilizes PrimeVue's native `lazy` loading mechanism.
5.  **Testability:** The composable is a pure JS/TS function, making it very easy to unit test.

## 4. V2 Principles and Best Practices

To ensure `DataTableV2` and `useLazyDataTable` are built sustainably and modernly, we must adhere to the following principles:

### a. Leverage Vue 3 Best Practices
- **`defineModel`**: When building form components or more complex wrappers in the future, prioritize `defineModel` (from Vue 3.4+) to simplify two-way `v-model` binding, instead of manual `props` and `emits`.
- **Order in `<script setup>`**: Adhere to the order specified in `CLAUDE.md` (imports, props/emits/model, state, computed, watch, lifecycle, methods) to keep the source code clean and predictable.
- **Reactivity**: Use `ref` for primitives and `reactive` for complex objects appropriately. Prefer `computed` for derived state.

### b. Styling & Layout
- **Tailwind First**: Continue to use Tailwind CSS utility classes as the first priority for styling.
- **PrimeVue Passthrough (PT)**: For deep customization of PrimeVue components (like `DataTableV2`), use `pt` to apply Tailwind classes. Avoid writing global CSS or `<style scoped>` unless absolutely necessary. This encapsulates styling and prevents unwanted conflicts.

### c. Stay Updated with `context7`
The front-end world changes rapidly. To ensure the V2 architecture does not become obsolete, proactively updating our knowledge is crucial.

- **Mandatory `context7` Usage:** Before starting to implement or refactor a major feature involving PrimeVue, **always** use `context7` to check the latest documentation.
    ```bash
    # Example useful commands
    "Get latest PrimeVue 4.x DataTable lazy loading API and examples - use context7"
    "Show me best practices for PrimeVue Passthrough (PT) in Vue 3 - use context7"
    "What are the breaking changes for PrimeVue DataTable from v4.2 to latest? - use context7"
    ```
- **Why is this necessary?**: This helps us leverage new APIs more effectively, avoid using outdated patterns, and prepare for future upgrades with ease.

## 5. Implementation Plan

1.  Create the `useLazyDataTable.ts` file in the `src/composables/` directory.
2.  Create the `DataTableV2.vue` component in `src/components/DataTableV2/`, applying the aforementioned best practices.
3.  Refactor the `@/pages/product/Products.vue` page to use the V2 architecture as a pilot. See the `PRODUCTS_PAGE_MIGRATION_V2.md` document for details.
4.  After confirming its effectiveness, plan the upgrade for the remaining pages.
