
# Migration Plan: Upgrading Products Page to DataTable V2

This document provides a step-by-step guide for refactoring the `@/pages/product/Products.vue` page from the V1 DataTable architecture to the new V2 architecture, which is based on the `useLazyDataTable` composable.

**Objective:** Replace `useFilterList` and the fragmented state management logic with `useLazyDataTable` to achieve a cleaner, more maintainable, and more efficient component.

---

## Migration Steps

### Step 1: Preparation - Create Necessary Files

1.  **Create the Composable:**
    -   Create the file `src/composables/useLazyDataTable.ts` with the content defined in `DATATABLE_V2_PROPOSAL.md`.

2.  **Create the V2 Component:**
    -   Create the directory `src/components/DataTableV2`.
    -   Create the file `src/components/DataTableV2/DataTableV2.vue`. Initially, this can be a very thin wrapper around the PrimeVue `DataTable`, containing only the `pt` (styling) customizations we want to apply globally.
    -   Create `src/components/DataTableV2/index.ts` to export the component.

### Step 2: Refactor `Products.vue`

This is the process of changing the code within the `@/pages/product/Products.vue` file.

#### 1. Remove Old Logic

-   Completely remove the import and usage of `useFilterList`.
-   Delete now-unnecessary `ref`s and `computed` properties:
    -   `tableState` (will be replaced by `data` and `totalRecords` from the composable).
    -   `perPage` (will be managed by `rows` from the composable).
    -   `currentPage`.
    -   `columns` (can be kept or moved into the composable if desired).
-   Delete old logic handlers:
    -   `handlePageChange`.
    -   `handlePerPageChange`.
    -   `loadList`.
-   Remove unnecessary `watch` handlers (e.g., `watch(perPage, ...)` and `watch(filters, ...)`).

#### 2. Integrate `useLazyDataTable`

-   Import `useLazyDataTable` from `src/composables/useLazyDataTable.ts`.
-   Import `useProduct` to get the `listProducts` function.

-   **Define the necessary functions for the composable:**

    ```typescript
    // 1. Define the API fetcher function
    const fetchProductsApi = async (params: ProductListParams): Promise<ProductListResponse> => {
      const response = await listProducts(params);
      // Map the response structure to match the composable's expectation
      return {
        items: response.products,
        total: response.total,
      };
    };

    // 2. Define the function to map the PrimeVue Event to API Params
    const mapPrimeVueEventToApiParams = (event: DataTablePageEvent): ProductListParams => {
      const page = Math.floor(event.first / event.rows) + 1;
      const apiFilters: Record<string, any> = {};

      for (const key in event.filters) {
        const filterValue = event.filters[key].value;
        if (filterValue !== null && filterValue !== '') {
          const apiKey = key === 'name' ? 'search' : key;
          const apiValue = key === 'status' ? Number(filterValue) : filterValue;
          apiFilters[apiKey] = apiValue;
        }
      }

      return {
        page: page,
        page_size: event.rows,
        ...apiFilters,
      };
    };
    ```

-   **Call `useLazyDataTable`:**

    ```typescript
    const { 
      data: products, 
      totalRecords, 
      loading, 
      filters, 
      rows, 
      onLazyLoad 
    } = useLazyDataTable<Product, ProductListParams>({
      fetchDataFn: fetchProductsApi,
      initialFilters: {
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        type: { value: null, matchMode: FilterMatchMode.EQUALS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
      },
      initialRows: 10,
      mapEventToParams: mapPrimeVueEventToApiParams,
    });
    ```

#### 3. Update the Template

-   Change the `<DataTable>` component to `<DataTableV2>`.
-   Update the props of `DataTableV2` to bind to the values from `useLazyDataTable`:
    -   `:data="products"`
    -   `:loading="loading"`
    -   `:total-records="totalRecords"`
    -   `:rows="rows"`
    -   `v-model:filters="filters"`
    -   `@page="onLazyLoad"`
    -   Add the `lazy` prop.

-   Remove unnecessary props like the V1 `v-model:filters` and the old `@page` handler.

    ```html
    <DataTableV2
      title="Product List"
      :columns="columns"
      :data="products"
      :loading="loading"
      :total-records="totalRecords"
      paginator
      lazy
      :rows="rows"
      v-model:filters="filters"
      @page="onLazyLoad"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <!-- Slots remain unchanged -->
    </DataTableV2>
    ```

#### 4. Update Data Refresh Logic

-   In functions like `handleFormSuccess` or after a deletion (`onAccept` in `handleDelete`), instead of calling `loadList(currentFilterPayload.value)`, we need a way to trigger a data reload.
-   **Solution:** Create a `refreshData` function that calls `onLazyLoad` with the current state.

    ```typescript
    // You might need to manage currentPage or get it from the composable
    const currentPage = ref(1); // Example

    const refreshData = () => {
        onLazyLoad({
            first: (currentPage.value - 1) * rows.value,
            rows: rows.value,
            filters: filters.value
        });
    };

    // Use in handleFormSuccess
    const handleFormSuccess = () => {
      refreshData();
    };
    ```

### Step 3: Verification and Cleanup

1.  Run the application and thoroughly test the Products page:
    -   Does the initial data load correctly?
    -   Do the filters for name, type, and status work as expected?
    -   Does pagination work correctly?
    -   Does adding, editing, and deleting a product properly reload the list?
2.  After confirming everything works, delete the `src/hooks/useFilterList.ts` file (if no other pages are using it).
3.  Delete the V1 `DataTable` component if all pages have been migrated.
