# DataTable V2 URL Parameters Implementation

## Overview

This document describes the implementation of URL parameters functionality for the DataTable V2 system, enabling state persistence, shareability, and browser history management.

## Features Implemented

### 1. URL State Synchronization
- **Filters**: All column filters are persisted in URL
- **Pagination**: Current page and rows per page
- **Sorting**: Sort field and direction
- **Deep Linking**: Direct navigation to specific table states

### 2. Browser History Management
- **Back/Forward Navigation**: Browser buttons work correctly
- **History API**: Uses Vue Router's replace method to avoid cluttering history
- **State Restoration**: Automatically restores table state from URL on page load

### 3. Shareability
- **Compressed URLs**: State is base64-encoded and compressed to minimize URL length
- **Version Support**: Includes version field for backward compatibility
- **Clean URLs**: Empty or default states don't add URL parameters

## Implementation Details

### Core Files

#### 1. `src/utils/urlState.ts`
Central utility for URL state management:

```typescript
// Key interfaces
interface DataTableState {
  filters: Record<string, any>;
  rows: number;
  page: number;
  first: number;
  sortField?: string;
  sortOrder?: number;
}

// Main composable
export function useUrlState(options: UseUrlStateOptions)
```

**Key Functions:**
- `serializeStateToUrl()`: Converts table state to URL-safe string
- `deserializeStateFromUrl()`: Parses URL parameter back to table state
- `useUrlState()`: Vue composable for URL state management

#### 2. `src/composables/useLazyDataTable.ts`
Enhanced with URL persistence:

```typescript
// New options
interface UseLazyDataTableOptions<T, P> {
  // ... existing options
  enableUrlPersistence?: boolean;
  urlStateKey?: string;
  persistedFields?: string[];
  urlDebounceMs?: number;
}
```

**Key Features:**
- Automatic URL updates on state changes (debounced)
- Initial state loading from URL on mount
- URL state management functions in return object

#### 3. `src/pages/product/Products.vue`
Updated to use URL persistence:

```typescript
const { 
  data: products, 
  // ... other returns
  clearUrlState,
  getCurrentUrlState,
  hasUrlState
} = useLazyDataTable<Product, any>({
  ...createProductDataTableConfig(listProducts),
  
  // URL persistence configuration
  enableUrlPersistence: true,
  urlStateKey: 'products',
  persistedFields: ['filters', 'pagination', 'sorting'],
  urlDebounceMs: 300
});
```

### URL Format

URLs follow this pattern:
```
/product?products=<base64-encoded-state>
```

**Encoded State Structure:**
```json
{
  "v": "1.0",           // Version for compatibility
  "d": {                // Compressed data
    "f": {...},         // Filters
    "r": 10,            // Rows per page
    "p": 0,             // Current page
    "s": {              // Sorting (optional)
      "field": "name",
      "order": 1
    }
  }
}
```

### Configuration Options

#### URL Persistence Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enableUrlPersistence` | boolean | false | Enable URL state persistence |
| `urlStateKey` | string | 'table' | URL parameter key |
| `persistedFields` | string[] | ['filters', 'pagination', 'sorting'] | Which fields to persist |
| `urlDebounceMs` | number | 300 | Debounce delay for URL updates |

#### Persisted Fields

- **'filters'**: All column filters and their values
- **'pagination'**: Current page and rows per page
- **'sorting'**: Sort field and direction

## Usage Examples

### Basic Usage

```typescript
// Enable URL persistence
const { 
  data, 
  loading, 
  onLazyLoad,
  clearUrlState 
} = useLazyDataTable({
  fetchDataFn: myFetchFunction,
  enableUrlPersistence: true,
  urlStateKey: 'my-table'
});
```

### Custom Configuration

```typescript
// Custom persistence settings
const tableState = useLazyDataTable({
  fetchDataFn: myFetchFunction,
  enableUrlPersistence: true,
  urlStateKey: 'products',
  persistedFields: ['filters', 'pagination'], // Only persist filters and pagination
  urlDebounceMs: 500 // Longer debounce
});
```

### Manual State Management

```typescript
// Check if URL contains state
if (hasUrlState()) {
  const currentState = getCurrentUrlState();
  console.log('Current URL state:', currentState);
}

// Clear URL state
clearUrlState();
```

## Testing

### Test Page
A comprehensive test page is available at `/product-url-test` that demonstrates:

- URL state display and monitoring
- Filter testing
- Pagination testing
- Sorting testing
- State clearing

### Manual Testing Scenarios

1. **Filter Persistence**:
   - Apply filters → Check URL updates
   - Refresh page → Verify filters are restored
   - Share URL → Verify filters work for other users

2. **Pagination Persistence**:
   - Navigate to page 2 → Check URL updates
   - Refresh page → Verify page 2 is loaded
   - Use browser back button → Verify navigation works

3. **Sorting Persistence**:
   - Sort by column → Check URL updates
   - Refresh page → Verify sorting is maintained
   - Change sort direction → Verify URL updates

4. **Combined State**:
   - Apply filters + pagination + sorting
   - Verify all state is persisted in URL
   - Test browser navigation
   - Test URL sharing

## Browser Compatibility

- **Modern Browsers**: Full support (Chrome 60+, Firefox 55+, Safari 12+)
- **URL Length**: Automatically handles URL length limits
- **History API**: Uses standard History API for navigation
- **Base64 Encoding**: Standard base64 for cross-browser compatibility

## Performance Considerations

- **Debounced Updates**: URL updates are debounced (default 300ms)
- **Compressed State**: State is compressed to minimize URL length
- **Selective Persistence**: Only specified fields are persisted
- **Memory Efficient**: No additional state storage beyond Vue reactivity

## Error Handling

- **Invalid URLs**: Gracefully falls back to default state
- **Parsing Errors**: Logs warnings and uses default state
- **Version Mismatch**: Handles backward compatibility
- **URL Length**: Validates URL length limits

## Migration Guide

### Existing Tables

To add URL persistence to existing DataTable V2 implementations:

1. **Update useLazyDataTable call**:
```typescript
// Before
const tableState = useLazyDataTable({
  fetchDataFn: myFetchFunction
});

// After
const tableState = useLazyDataTable({
  fetchDataFn: myFetchFunction,
  enableUrlPersistence: true,
  urlStateKey: 'unique-table-key'
});
```

2. **Optional: Use URL state functions**:
```typescript
const { 
  // ... existing returns
  clearUrlState,
  getCurrentUrlState,
  hasUrlState
} = useLazyDataTable({...});
```

### No Breaking Changes
- URL persistence is opt-in (disabled by default)
- Existing implementations continue to work unchanged
- All existing APIs remain compatible

## Future Enhancements

- **Multiple Table Support**: Handle multiple tables on same page
- **Custom Serialization**: Allow custom state serialization
- **URL Shortening**: Integration with URL shortening services
- **State Validation**: Enhanced validation for URL state
- **Analytics Integration**: Track URL sharing and usage patterns
