<template>
  <div class="group inline-flex items-center justify-center" :class="sizeClasses.container">
    <!-- Always Edit Mode: Input with hover controls -->
    <div class="flex items-center rounded px-1 transition-colors">
      <!-- Decrease button (hidden by default, shown on hover) -->
      <i
        v-if="!readonly"
        class="pi pi-minus cursor-pointer rounded opacity-0 shadow transition-all duration-200 hover:bg-gray-100 group-hover:opacity-100"
        :class="[sizeClasses.button]"
        @click="decrease"
      ></i>

      <!-- Input field that always shows value -->
      <InputNumber
        :model-value="modelValue"
        :min="0"
        :max-fraction-digits="maxFractionDigits"
        :step="step"
        :use-grouping="false"
        :placeholder="placeholder"
        :readonly="readonly"
        :pt:pcInputText:root:class="inputClasses"
        @input="handleInputChange"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
        fluid
      />

      <!-- Increase button (hidden by default, shown on hover) -->
      <i
        v-if="!readonly"
        class="pi pi-plus cursor-pointer rounded opacity-0 shadow transition-all duration-200 hover:bg-gray-100 group-hover:opacity-100"
        :class="sizeClasses.button"
        @click="increase"
      ></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

interface Props {
  modelValue: number;
  step?: number;
  prefix?: string;
  suffix?: string;
  readonly?: boolean;
  placeholder?: string;
  size?: "xs" | "sm" | "md" | "lg";
  maxFractionDigits?: number;
}

const props = withDefaults(defineProps<Props>(), {
  step: 1,
  prefix: "",
  suffix: "",
  readonly: false,
  placeholder: "-",
  size: "sm",
  maxFractionDigits: 5,
});

const emit = defineEmits<{
  "update:modelValue": [value: number];
  "edit-start": [value: number];
  "edit-save": [value: number];
  "edit-cancel": [];
}>();

const isFocused = ref(false);

// Computed styles based on size
const sizeClasses = computed(() => {
  switch (props.size) {
    case "xs":
      return {
        container: "min-w-[50px]",
        input: "text-xs w-10",
        button: "text-xs p-0.5",
      };
    case "sm":
      return {
        container: "min-w-[60px]",
        input: "text-sm w-12",
        button: "text-sm p-1",
      };
    case "md":
      return {
        container: "min-w-[70px]",
        input: "text-base w-14",
        button: "text-base p-1.5",
      };
    case "lg":
      return {
        container: "min-w-[80px]",
        input: "text-lg w-16",
        button: "text-lg p-2",
      };
    default:
      return {
        container: "min-w-[60px]",
        input: "text-sm w-12",
        button: "text-sm p-1",
      };
  }
});

const inputClasses = computed(() => {
  return `${sizeClasses.value.input} font-mono tabular-nums font-medium border-0 p-1 shadow-none focus:ring-0 focus:border-none text-center bg-transparent`;
});

const handleFocus = () => {
  isFocused.value = true;
  emit("edit-start", props.modelValue);
};

const handleBlur = () => {
  isFocused.value = false;
  // Small delay to allow click events on buttons to register
  setTimeout(() => {
    emit("edit-save", props.modelValue);
  }, 150);
};

const increase = () => {
  if (props.readonly) return;
  const newValue = props.modelValue + props.step;
  emit("update:modelValue", newValue);
};

const decrease = () => {
  if (props.readonly) return;
  const newValue = Math.max(0, props.modelValue - props.step);
  emit("update:modelValue", newValue);
};

const handleInputChange = (event: any) => {
  const value = event.value ?? 0;
  const newValue = Math.max(0, value);
  emit("update:modelValue", newValue);
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    event.preventDefault();
    (event.target as HTMLInputElement).blur();
  } else if (event.key === "Escape") {
    event.preventDefault();
    emit("edit-cancel");
    (event.target as HTMLInputElement).blur();
  } else if (event.key === "ArrowUp") {
    event.preventDefault();
    increase();
  } else if (event.key === "ArrowDown") {
    event.preventDefault();
    decrease();
  }
};
</script>
