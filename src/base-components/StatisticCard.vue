<script setup lang="ts">
import { computed } from "vue";

import Lucide, { type Icon } from "@/base-components/Lucide";

interface StatisticCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: Icon; // Lucide icon name (e.g., "TrendingUp", "DollarSign")
  theme?: "success" | "warning" | "primary" | "danger" | "info";
  badge?: string;
  badgeTheme?: "success" | "warning" | "primary" | "danger" | "info";
}

const props = withDefaults(defineProps<StatisticCardProps>(), {
  theme: "primary",
  badgeTheme: "success",
});

// Theme color mappings
const themeColors = {
  success: {
    icon: "text-success",
    badge: "bg-success/20 text-success",
  },
  warning: {
    icon: "text-warning", 
    badge: "bg-warning/20 text-warning",
  },
  primary: {
    icon: "text-primary",
    badge: "bg-primary/20 text-primary",
  },
  danger: {
    icon: "text-danger",
    badge: "bg-danger/20 text-danger",
  },
  info: {
    icon: "text-info",
    badge: "bg-info/20 text-info",
  },
};

const iconColorClass = computed(() => themeColors[props.theme].icon);
const badgeColorClass = computed(() => themeColors[props.badgeTheme].badge);

// Format value if it's a number
const formattedValue = computed(() => {
  if (typeof props.value === "number") {
    return props.value.toLocaleString();
  }
  return props.value;
});
</script>

<template>
  <div
    :class="[
      'relative animate-[zoomIn_0.5s_ease-out]',
      'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
    ]"
  >
    <div class="box p-4">
      <div class="flex items-center">
        <Lucide :icon="icon" :class="['h-6 w-6', iconColorClass]" />
        <div class="ml-auto">
          <div
            v-if="badge"
            :class="[
              'rounded-full px-2 py-1 text-xs font-medium',
              badgeColorClass
            ]"
          >
            {{ badge }}
          </div>
        </div>
      </div>
      <div class="mt-4 text-2xl font-medium leading-8">{{ formattedValue }}</div>
      <div class="mt-1 text-sm text-slate-500">{{ subtitle }}</div>
    </div>
  </div>
</template>
