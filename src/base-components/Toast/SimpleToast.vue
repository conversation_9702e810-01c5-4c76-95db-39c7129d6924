<script setup lang="ts">
import { provide, ref, watch } from "vue";

import Lucide from "@/base-components/Lucide";
import { Icon } from "@/base-components/Lucide/Lucide.vue";
import Toast from "@/base-components/Toast";
import { ToastElement } from "@/base-components/Toast";
import { ToastType, SimpleToast, useToastStore } from "@/stores/toast-store";

const toastClassMap: Record<ToastType, string> = {
  warning: "warning",
  error: "danger",
  success: "success",
  info: "secondary",
};

const toastIconMap: Record<ToastType, Icon> = {
  warning: "AlertTriangle",
  error: "XCircle",
  success: "CheckSquare",
  info: "Activity",
};

const toastStore = useToastStore();
const toastElement = ref<ToastElement | null>(null); //Dùng để manipulate dom object
const simpleToastItem = ref<SimpleToast>();

const showToast = () => {
  const nextToast = toastStore.next();
  if (nextToast && toastElement.value) {
    simpleToastItem.value = nextToast;
    toastElement.value?.showToast();
  }
};

watch(
  () => toastStore.toasts.length,
  (newVal) => {
    if (newVal > 0) {
      showToast();
    }
  },
  { immediate: true },
);

provide("bind[simpleToast]", (el: ToastElement) => {
  toastElement.value = el;
});
</script>

<template>
  <Toast ref-key="simpleToast" class="flex" :options="{ duration: 2000 }">
    <Lucide
      :icon="simpleToastItem?.type ? toastIconMap[simpleToastItem.type] : 'Activity'"
      :class="simpleToastItem?.type ? 'text-' + toastClassMap[simpleToastItem.type] : ''"
    />
    <div class="ml-4 mr-4">
      <div class="font-medium">{{ simpleToastItem?.title }}</div>
      <div class="mt-1 text-slate-500">
        {{ simpleToastItem?.message }}
      </div>
    </div>
    <slot></slot>
  </Toast>
</template>

<style scoped></style>
