<script setup lang="ts">
import Toastify, { Options } from "toastify-js";
import { HTMLAttributes, inject, onMounted, ref } from "vue";

import { init, reInit } from "./toast";

export interface ToastElement extends HTMLDivElement {
  toastify: ReturnType<typeof Toastify>;
  showToast: () => void;
  hideToast: () => void;
}

export interface ToastProps extends /* @vue-ignore */ HTMLAttributes {
  options?: Options;
  refKey?: string;
}

export type ProvideToast = (el: ToastElement) => void;

const props = defineProps<ToastProps>();

const toastifyRef = ref<ToastElement>();

const bindInstance = (el: ToastElement) => {
  if (props.refKey) {
    const bind = inject<ProvideToast>(`bind[${props.refKey}]`);
    if (bind) {
      bind(el);
    }
  }
};

const vToastDirective = {
  mounted(el: ToastElement) {
    init(el, props);
  },
  updated(el: ToastElement) {
    reInit(el);
  },
};

onMounted(() => {
  if (toastifyRef.value) {
    bindInstance(toastifyRef.value);
  }
});
</script>

<template>
  <div
    ref="toastifyRef"
    v-toast-directive
    class="hidden rounded-lg border border-slate-200/60 bg-white py-5 pl-5 pr-14 shadow-xl dark:border-darkmode-600 dark:bg-darkmode-600 dark:text-slate-300"
  >
    <slot></slot>
  </div>
</template>
