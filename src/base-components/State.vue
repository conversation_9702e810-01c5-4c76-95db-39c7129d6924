<!-- components/State.vue -->
<script lang="ts" setup>
import { computed } from "vue";

export type StateType =
  | "draft"
  | "active"
  | "won"
  | "lost"
  | "cancelled"
  | "new"
  | "pending"
  | "old"
  | "paying"
  | "initial"
  | "subsequent";

interface Props {
  state: StateType | string;
  size?: "sm" | "md" | "lg" | number;
  shine?: boolean; // Add shine effect option
}

const props = withDefaults(defineProps<Props>(), {
  size: "sm",
  shine: true,
});

const stateColor = computed(() => {
  const colorMap: Record<StateType, string> = {
    draft: "bg-gray-100 text-gray-800 border-gray-300",
    active: "bg-sky-100 text-sky-800 border-sky-300",
    won: "bg-green-100 text-green-800 border-green-300",
    lost: "bg-red-100 text-red-800 border-red-300",
    cancelled: "bg-orange-100 text-orange-800 border-orange-300",
    pending: "bg-yellow-100 text-yellow-800 border-yellow-300",
    new: "bg-cyan-100 text-cyan-800 border-cyan-300",
    old: "bg-teal-100 text-teal-800 border-teal-300",
    paying: "bg-indigo-100 text-indigo-800 border-indigo-300",

    initial: "bg-emerald-100 text-emerald-800 border-emerald-300",
    subsequent: "bg-amber-100 text-amber-800 border-amber-300",
  };

  return colorMap[props.state as StateType] || colorMap.draft;
});

const stateLabel = computed(() => {
  const labelMap: Partial<Record<StateType, string>> = {
    // TODO: Add custom label here
    subsequent: "Thu bổ sung",
  };

  const label = labelMap[props.state as StateType] || props.state;
  return label.charAt(0).toUpperCase() + label.slice(1);
});

const sizeClasses = computed(() => {
  if (typeof props.size === "number") {
    return `h-[${props.size}px] px-2`;
  }

  const sizeMap = {
    sm: "px-1 text-xs",
    md: "px-2 py-1 text-sm",
    lg: "px-3 py-1.5 text-base",
  };

  return sizeMap[props.size];
});
</script>

<template>
  <span
    :class="[
      'relative inline-flex cursor-pointer items-center justify-center rounded border font-medium',
      stateColor,
      sizeClasses,
      { 'shine-effect': shine },
    ]"
  >
    <slot>
      {{ stateLabel }}
    </slot>
  </span>
</template>

<style scoped>
.shine-effect {
  overflow: hidden;
}

.shine-effect::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 60%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine 2.5s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Hover effect - optional */
.shine-effect:hover::after {
  animation: shine 1s forwards;
}
</style>
