import { ref, computed, onMounted } from 'vue';
import type { Ref, ComputedRef } from 'vue';
import type { DataTablePageEvent } from 'primevue/datatable';

/**
 * Base type for data items that can be used in the DataTable
 */
export type DataTableItem = Record<string, any>;

/**
 * Base type for API parameters
 */
export type ApiParams = Record<string, any>;

/**
 * Return type of the useLazyDataTable composable
 */
export interface UseLazyDataTableReturn<T extends DataTableItem = DataTableItem> {
  // Reactive state
  data: Ref<T[]>;
  totalRecords: Ref<number>;
  loading: Ref<boolean>;
  error: Ref<string | null>;
  currentPage: Ref<number>;
  first: Ref<number>;
  filters: Ref<Record<string, any>>;
  rows: Ref<number>;

  // Computed properties
  isEmpty: ComputedRef<boolean>;
  hasError: ComputedRef<boolean>;
  totalPages: ComputedRef<number>;
  hasData: ComputedRef<boolean>;
  canRetry: ComputedRef<boolean>;

  // Event handlers and methods
  onLazyLoad: (event: DataTablePageEvent) => Promise<void>;
  retry: () => Promise<void>;
  clearError: () => void;
}

/**
 * Response structure expected from the API for lazy data table
 */
export interface LazyDataTableResponse<T extends DataTableItem = DataTableItem> {
  items: T[];
  total: number;
}

/**
 * Options for configuring the useLazyDataTable composable
 */
export interface UseLazyDataTableOptions<T extends DataTableItem = DataTableItem, TParams extends ApiParams = ApiParams> {
  /**
   * Function to fetch data from the API
   * @param params - API parameters mapped from the DataTable page event
   * @returns Promise resolving to the data response
   */
  fetchDataFn: (params: TParams) => Promise<LazyDataTableResponse<T>>;
  
  /**
   * Initial filter configuration for the DataTable
   * Should match PrimeVue's filter structure with value and matchMode
   */
  initialFilters: Record<string, any>;
  
  /**
   * Function to map PrimeVue DataTablePageEvent to API parameters
   * @param event - The page event from PrimeVue DataTable
   * @returns API parameters object
   */
  mapEventToParams: (event: DataTablePageEvent) => TParams;
  
  /**
   * Initial number of rows per page
   * @default 10
   */
  initialRows?: number;

  /**
   * Whether to automatically load data on mount
   * @default true
   */
  autoLoad?: boolean;

  /**
   * Number of retry attempts on error
   * @default 0
   */
  retryAttempts?: number;
}

/**
 * Composable for managing lazy-loaded DataTable state and operations
 * 
 * This composable provides a clean separation between data logic and presentation,
 * leveraging PrimeVue's native lazy loading capabilities.
 * 
 * @template T - Type of the data items
 * @template TParams - Type of the API parameters
 * @param options - Configuration options for the composable
 * @returns Object containing reactive state and event handlers
 */
export function useLazyDataTable<T extends DataTableItem = DataTableItem, TParams extends ApiParams = ApiParams>(
  options: UseLazyDataTableOptions<T, TParams>
) {
  const {
    fetchDataFn,
    initialFilters,
    mapEventToParams,
    initialRows = 10,
    autoLoad = true,
    retryAttempts = 0
  } = options;

  // Core reactive state using refs for better TypeScript support
  const data = ref<T[]>([]);
  const totalRecords = ref(0);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const currentPage = ref(0);
  const first = ref(0);

  // Separate refs for user-controlled state
  const filters = ref(initialFilters);
  const rows = ref(initialRows);

  // Additional state for retry functionality
  const retryCount = ref(0);
  const lastFailedEvent = ref<DataTablePageEvent | null>(null);

  // Computed properties for derived state
  const isEmpty = computed(() => data.value.length === 0 && !loading.value);
  const hasError = computed(() => error.value !== null);
  const totalPages = computed(() => Math.ceil(totalRecords.value / rows.value));
  const hasData = computed(() => data.value.length > 0);
  const canRetry = computed(() => hasError.value && retryCount.value < retryAttempts && lastFailedEvent.value !== null);

  /**
   * Main event handler for DataTable lazy loading
   * This function is called by PrimeVue DataTable's @page event
   *
   * @param event - DataTablePageEvent containing pagination, filters, and sorting info
   */
  const onLazyLoad = async (event: DataTablePageEvent) => {
    try {
      loading.value = true;
      error.value = null;

      // Update pagination state
      currentPage.value = event.page || 0;
      first.value = event.first || 0;

      // Map PrimeVue event to API parameters
      const apiParams = mapEventToParams(event);

      // Fetch data from API
      const response = await fetchDataFn(apiParams);

      // Validate response structure
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format from API');
      }

      if (!Array.isArray(response.items)) {
        throw new Error('Response items must be an array');
      }

      if (typeof response.total !== 'number' || response.total < 0) {
        throw new Error('Response total must be a non-negative number');
      }

      // Update reactive state
      data.value = response.items;
      totalRecords.value = response.total;

    } catch (err) {
      // Store failed event for potential retry
      lastFailedEvent.value = event;
      retryCount.value++;

      // Clear data on error to prevent stale state
      data.value = [];
      totalRecords.value = 0;

      // Set error message
      if (err instanceof Error) {
        error.value = err.message;
      } else if (typeof err === 'string') {
        error.value = err;
      } else {
        error.value = 'An unexpected error occurred while loading data';
      }

      console.error('Error in useLazyDataTable:', err);
    } finally {
      loading.value = false;
    }
  };

  /**
   * Retry the last failed request
   */
  const retry = async () => {
    if (canRetry.value && lastFailedEvent.value) {
      retryCount.value = 0; // Reset retry count for new attempt
      await onLazyLoad(lastFailedEvent.value);
    }
  };

  /**
   * Clear error state and reset retry count
   */
  const clearError = () => {
    error.value = null;
    retryCount.value = 0;
    lastFailedEvent.value = null;
  };

  // Initial data load on component mount
  onMounted(() => {
    if (autoLoad) {
      // Trigger initial load with default parameters
      const initialEvent: Partial<DataTablePageEvent> = {
        first: 0,
        rows: rows.value,
        page: 0,
        filters: filters.value,
        sortField: undefined,
        sortOrder: undefined,
        multiSortMeta: undefined
      };

      onLazyLoad(initialEvent as DataTablePageEvent);
    }
  });

  return {
    // Reactive state
    data,
    totalRecords,
    loading,
    error,
    currentPage,
    first,
    filters,
    rows,

    // Computed properties
    isEmpty,
    hasError,
    totalPages,
    hasData,
    canRetry,

    // Event handlers and methods
    onLazyLoad,
    retry,
    clearError
  };
}
