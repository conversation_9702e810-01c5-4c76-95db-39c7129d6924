import { useNProgress } from "@vueuse/integrations/useNProgress";
import { useRouter } from "vue-router";

export type PrintEntity =
  | "payment"
  | "deal"
  | "bill"
  | "plan"
  | "medication"
  | "medical-exam"
  | "refund"
  | "outpatient-medical"
  | "cashflow"
  | "cashflow-summary";

interface PrintOptions {
  entity: PrintEntity;
  id?: string | number;
  personId?: string | number;
  callback?: () => void;
  options?: {
    [key: string]: any;
  };
}

export const usePrint = () => {
  const router = useRouter();
  const { isLoading } = useNProgress();

  const getFileName = (entity: PrintEntity, id?: string | number) => {
    switch (entity) {
      case "payment":
        return `Phieu-thu-${id}`;
      case "refund":
        return `Phieu-hoan-phi-${id}`;
      case "deal":
        return `Phieu-bao-gia-${id}`;
      case "bill":
        return `Hoa-don-${id}`;
      case "plan":
        return `Ke-hoach-dieu-tri`;
      case "medication":
        return `Don-thuoc-${id}`;
      case "medical-exam":
        return `Phieu-kham`;
      case "outpatient-medical":
        return `Benh-an-ngoai-tru`;
      case "cashflow":
        return `Phieu-thu-chi-${id}`;
      default:
        return `document-${id}`;
    }
  };

  const printX = async ({ entity, id, personId, callback, options }: PrintOptions) => {
    try {
      // Start loading
      isLoading.value = true;

      const routeName = `print-${entity}`;
      const url = router.resolve({
        name: routeName,
        params: { id, personId, ...(options ?? {}) },
      }).href;

      // Create iframe
      const iframe = document.createElement("iframe");
      iframe.style.position = "fixed";
      iframe.style.right = "-9999px";
      iframe.style.bottom = "-9999px";
      iframe.style.width = "1024px";
      iframe.style.height = "1440px";
      document.body.appendChild(iframe);

      // Load content into iframe
      iframe.src = url;

      // Wait for iframe to load and fonts to be ready
      iframe.onload = async () => {
        try {
          if (iframe.contentWindow?.document) {
            await new Promise<void>((resolve) => {
              if (document.fonts && document.fonts.ready) {
                document.fonts.ready.then(async () => {
                  if (iframe.contentWindow?.document.fonts) {
                    await iframe.contentWindow.document.fonts.ready;
                  }
                  setTimeout(resolve, 500);
                });
              } else {
                setTimeout(resolve, 1000);
              }
            });
          }

          // Execute print
          if (iframe.contentWindow) {
            // Stop loading before showing print dialog
            isLoading.value = false;
            iframe.contentWindow.print();
          }

          // Cleanup after printing
          const cleanup = () => {
            document.body.removeChild(iframe);
            callback?.();
          };

          if (iframe.contentWindow) {
            iframe.contentWindow.onafterprint = cleanup;
          }

          // Fallback cleanup after 60s
          setTimeout(cleanup, 60000);
        } catch (err) {
          console.error("Print execution error:", err);
          document.body.removeChild(iframe);
          isLoading.value = false;
        }
      };
    } catch (error) {
      console.error("Print error:", error);
      isLoading.value = false;
    }
  };

  const printPayment = (id: string | number, callback?: () => void) => {
    printX({ entity: "payment", id, callback });
  };

  const printDeal = (id: string | number, personId: string | number, callback?: () => void) => {
    printX({ entity: "deal", id, personId, callback });
  };

  const printBill = (id: string | number, callback?: () => void) => {
    printX({ entity: "bill", id, callback });
  };

  const printPlan = (personId: string | number, options?: { type: string }) => {
    printX({
      entity: "plan",
      personId,
      options,
    });
  };

  const printMedication = (
    id: string | number,
    personId: string | number,
    options?: {
      [key: string]: any;
    },
  ) => {
    printX({ entity: "medication", id, personId, callback: () => {}, options });
  };

  const printMedicalExam = (
    personId: string | number,
    options?: {
      tabIndex: number;
    },
  ) => {
    printX({
      entity: "medical-exam",
      personId,
      options: {
        tabIndex: options?.tabIndex,
      },
    });
  };

  const printRefund = (id: string | number, callback?: () => void) => {
    printX({ entity: "refund", id, callback });
  };

  const printOutpatientMedical = (personId: string | number, callback?: () => void) => {
    printX({ entity: "outpatient-medical", id: personId, callback });
  };

  const printCashFlow = (id: string | number, callback?: () => void) => {
    printX({ entity: "cashflow", id, callback });
  };

  const printCashFlowSummary = (options?: {
    dateRange?: any;
  }, callback?: () => void) => {
    printX({
      entity: "cashflow-summary",
      options: {
        dateRange: options?.dateRange ? JSON.stringify(options.dateRange) : null,
      },
      callback
    });
  };

  return {
    printX,
    printPayment,
    printDeal,
    printBill,
    printPlan,
    printMedication,
    printMedicalExam,
    printRefund,
    printOutpatientMedical,
    printCashFlow,
    printCashFlowSummary,
  };
};
