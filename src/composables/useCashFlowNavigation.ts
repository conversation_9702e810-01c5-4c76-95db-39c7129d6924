import { ref, watch, type Ref } from "vue";
import { useRouteQuery } from "@vueuse/router";
import type { CashFlowWithRelations } from "@/api/bcare-types-v2";

export function useCashFlowNavigation(cashflows: Ref<CashFlowWithRelations[]>) {
  const cashflowId = useRouteQuery("cashflowId");
  const selectedCashFlow = ref<CashFlowWithRelations | null>(null);
  const showCashFlowForm = ref(false);
  const showDetailPanel = ref(false);
  const editingCashFlow = ref<CashFlowWithRelations | null>(null);
  const isLoadingCashFlowDetails = ref(false);

  const selectCashFlowFromUrl = async (cashflowIdStr: string | string[]) => {
    const idStr = Array.isArray(cashflowIdStr) ? cashflowIdStr[0] : cashflowIdStr;
    isLoadingCashFlowDetails.value = true;

    try {
      const found = cashflows.value.find((cf) => cf.id.toString() === idStr);

      if (found) {
        selectedCashFlow.value = found;
        showCashFlowForm.value = false;
        showDetailPanel.value = true;
      } else {
        console.warn(`CashFlow with ID '${idStr}' not found in current cashflows.`);
        cashflowId.value = null;
        selectedCashFlow.value = null;
        showDetailPanel.value = false;
      }
    } catch (error) {
      console.error("Failed to select cashflow from URL:", error);
      cashflowId.value = null;
    } finally {
      isLoadingCashFlowDetails.value = false;
    }
  };

  const handleCashFlowSelect = (cashflow: CashFlowWithRelations) => {
    selectedCashFlow.value = cashflow;
    showCashFlowForm.value = false;
    showDetailPanel.value = true;
    cashflowId.value = cashflow.id.toString();
  };

  const handleAddCashFlow = () => {
    editingCashFlow.value = null;
    selectedCashFlow.value = null;
    showDetailPanel.value = false;
    showCashFlowForm.value = true;
    cashflowId.value = null;
  };

  const handleEditCashFlow = (cashflow: CashFlowWithRelations) => {
    editingCashFlow.value = cashflow;
    showDetailPanel.value = false;
    showCashFlowForm.value = true;
  };

  const handleFormCancel = () => {
    showCashFlowForm.value = false;
    if (selectedCashFlow.value) {
      showDetailPanel.value = true;
    } else {
      cashflowId.value = null;
    }
  };

  const handleCashFlowDeleted = () => {
    selectedCashFlow.value = null;
    showDetailPanel.value = false;
    cashflowId.value = null;
  };

  // Watch for changes in URL's cashflowId parameter
  watch(
    cashflowId,
    (newCashflowId) => {
      if (newCashflowId) {
        if (cashflows.value.length > 0) {
          selectCashFlowFromUrl(newCashflowId);
        }
      } else {
        selectedCashFlow.value = null;
        showDetailPanel.value = false;
        showCashFlowForm.value = false;
      }
    },
    { immediate: true }
  );

  // Watch for cashflows data changes to handle URL-based selection after data loads
  watch(cashflows, (newCashflows) => {
    if (cashflowId.value && newCashflows.length > 0) {
      if (!selectedCashFlow.value) {
        selectCashFlowFromUrl(cashflowId.value);
      }
    }
  });

  return {
    // State
    selectedCashFlow,
    showCashFlowForm,
    showDetailPanel,
    editingCashFlow,
    isLoadingCashFlowDetails,

    // Methods
    handleCashFlowSelect,
    handleAddCashFlow,
    handleEditCashFlow,
    handleFormCancel,
    handleCashFlowDeleted,
    selectCashFlowFromUrl,
  };
}