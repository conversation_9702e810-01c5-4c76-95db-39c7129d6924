import { ref } from "vue";
import { downloadBlob } from "@/utils/file";
import { usePrint } from "@/composables/usePrint";
import service from "@/api/http";
import type { CashFlowFilters } from "./useCashFlowFilters";

/**
 * Generate export filename based on filters and current date
 * @param filters - Applied filters
 * @param fileExtension - File extension (default: .xlsx)
 * @returns Generated filename
 */
function generateExportFilename(filters: CashFlowFilters, fileExtension: string = ".xlsx"): string {
  const now = new Date();
  const dateStr = now.toISOString().split("T")[0];
  let filename = `cashflow-export-${dateStr}`;

  if (filters.type) {
    filename += `-${filters.type}`;
  }

  if (filters.state) {
    filename += `-${filters.state}`;
  }

  filename += fileExtension;
  return filename;
}

/**
 * Export cash flows to Excel file
 * @param filters - Applied filters
 * @throws Error if export fails
 */
async function exportCashFlows(filters: CashFlowFilters): Promise<void> {
  try {
    const filename = generateExportFilename(filters);
    const blob = await service.downloadBlob("/v1/cashflow/export", { filter: filters });
    downloadBlob(blob, filename);
  } catch (error) {
    console.error("Failed to export cash flows:", error);
    throw new Error("Không thể xuất báo cáo. Vui lòng thử lại.");
  }
}

export function useCashFlowExport() {
  const isExporting = ref(false);
  const { printCashFlowSummary } = usePrint();

  const handleExportReport = async (filters: CashFlowFilters) => {
    if (isExporting.value) return;
    
    isExporting.value = true;
    
    try {
      await exportCashFlows(filters);
    } catch (error) {
      console.error("Export failed:", error);
      throw error;
    } finally {
      isExporting.value = false;
    }
  };

  const handlePrintReport = (dateRange: Date[] | Date | (Date | null)[] | null | undefined) => {
    printCashFlowSummary({
      dateRange,
    });
  };

  return {
    isExporting,
    handleExportReport,
    handlePrintReport,
  };
}