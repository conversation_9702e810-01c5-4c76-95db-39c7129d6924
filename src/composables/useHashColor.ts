// src/composables/useHashColor.ts

import Color from "color";

export type ColorCollection = {
  [key: string]: string[];
};

export const COLOR_COLLECTIONS: ColorCollection = {
  UserAvatar: [
    "#C5E1A5",
    "#FFCC80",
    "#81D4FA",
    "#F48FB1",
    "#B39DDB",
    "#9FA8DA",
    "#90CAF9",
    "#80DEEA",
    "#80CBC4",
    "#A5D6A7",
    "#E6EE9C",
    "#FFF59D",
    "#FFAB91",
    "#BCAAA4",
    "#B0BEC5",
    "#FFE082",
    "#FFAB91",
    "#CE93D8",
    "#9FA8DA",
    "#90CAF9",
    "#80DEEA",
    "#80CBC4",
    "#A5D6A7",
    "#C5E1A5",
    "#E6EE9C",
    "#FFF59D",
    "#FFCC80",
    "#FFAB91",
    "#BCAAA4",
    "#B0BEC5",
    "#FFB74D",
    "#4DB6AC",
    "#7986CB",
    "#F06292",
    "#AED581",
    "#4DD0E1",
    "#BA68C8",
    "#FFD54F",
    "#7E57C2",
    "#64B5F6",
    "#81C784",
    "#DCE775",
    "#FFB74D",
    "#F48FB1",
    "#9575CD",
    "#4FC3F7",
    "#A1887F",
    "#90A4AE",
    "#FFD740",
    "#69F0AE",
  ],
  PersonAvatar: [
    "#E6F3FF",
    "#FFE6E6",
    "#E6FFE6",
    "#FFE6F3",
    "#F3E6FF",
    "#E6FFF3",
    "#FFF3E6",
    "#E6FFFF",
    "#FFE6FF",
    "#F3FFE6",
    "#FFE6EC",
    "#E6ECFF",
    "#FFECE6",
    "#E6FFEC",
    "#FFE6F9",
    "#F9E6FF",
    "#E6F9FF",
    "#FFF9E6",
    "#E6FFF9",
    "#F6E6FF",
    "#FFE6F6",
    "#E6FFF6",
    "#F6FFE6",
    "#FFE6FA",
    "#E6FAFF",
    "#FAE6FF",
    "#FFE6FB",
    "#E6FBFF",
    "#FBE6FF",
    "#FFEDE6",
    "#EDE6FF",
    "#E6FFED",
    "#FFE6ED",
    "#E6EDFF",
    "#EDFFE6",
    "#FFE6E9",
    "#E9E6FF",
    "#E6FFE9",
    "#FFE9E6",
    "#E6E9FF",
    "#E9FFE6",
    "#FFE6F0",
    "#F0E6FF",
    "#E6FFF0",
    "#FFF0E6",
    "#E6F0FF",
    "#F0FFE6",
    "#FFE6FC",
    "#FCE6FF",
    "#E6FFFC",
    "#FFFCE6",
    "#E6FCFF",
    "#FCFFE6",
    "#FFE6E7",
    "#E7E6FF",
    "#E6FFE7",
    "#FFE7E6",
    "#E6E7FF",
    "#E7FFE6",
    "#FFE6EE",
    "#EEE6FF",
    "#E6FFEE",
    "#FFEEE6",
    "#E6EEFF",
    "#EEFFE6",
    "#FFE6F5",
    "#F5E6FF",
    "#E6FFF5",
    "#FFF5E6",
    "#E6F5FF",
    "#F5FFE6",
    "#FFE6EB",
    "#EBE6FF",
    "#E6FFEB",
    "#FFEBE6",
    "#E6EBFF",
    "#EBFFE6",
    "#FFE6F2",
    "#F2E6FF",
    "#E6FFF2",
    "#FFF2E6",
    "#E6F2FF",
    "#F2FFE6",
    "#FFE6FE",
    "#FEE6FF",
    "#E6FFFE",
    "#FFFEE6",
    "#E6FEFF",
    "#FEFFE6",
    "#FFE6E8",
    "#E8E6FF",
    "#E6FFE8",
    "#FFE8E6",
    "#E6E8FF",
    "#E8FFE6",
  ],
  Tag: [
    "#E91E63",
    "#9C27B0",
    "#673AB7",
    "#3F51B5",
    "#2196F3",
    "#03A9F4",
    "#00BCD4",
    "#009688",
    "#4CAF50",
    "#8BC34A",
    "#CDDC39",
    "#FFEB3B",
    "#FFC107",
    "#FF9800",
    "#FF5722",
  ],
  Term: [
    "#1ABC9C",
    "#2ECC71",
    "#3498DB",
    "#9B59B6",
    "#34495E",
    "#16A085",
    "#27AE60",
    "#2980B9",
    "#8E44AD",
    "#2C3E50",
  ],
  Bright: [
    "#FFD700",
    "#FFB6C1",
    "#9370DB",
    "#7FFFD4",
    "#87CEFA",
    "#FFA07A",
    "#BA55D3",
    "#98FB98",
    "#FF8C00",
    "#4682B4",
    "#FF6347",
    "#40E0D0",
    "#EE82EE",
    "#32CD32",
    "#FF4500",
    "#00CED1",
    "#DA70D6",
    "#3CB371",
    "#1E90FF",
    "#FF1493",
    "#B0E0E6",
    "#FF7F50",
    "#8A2BE2",
    "#00FA9A",
    "#6495ED",
    "#FF00FF",
    "#20B2AA",
    "#7B68EE",
    "#FFA500",
    "#00FF7F",
  ],
  PastelHarmony: [
    "#FF9999",
    "#88D4C0",
    "#85D4E3",
    "#FDD999",
    "#BBA3E3",
    "#FFB07C",
    "#85D3B5",
    "#85BBE3",
    "#B885D1",
    "#F9C483",
    "#E8A188",
    "#99DBA6",
    "#85A3C8",
    "#85C3E6",
    "#B68ED1",
    "#85E6C6",
    "#F9D68A",
    "#E8B38A",
    "#88A8C8",
    "#B4B9C8",
    "#C8E8A1",
    "#9BD48A",
    "#FFB3B3",
    "#9BE8E6",
    "#C48AD1",
    "#A3A8E3",
    "#A3A3C8",
    "#B8C3D9",
    "#FF8C8C",
    "#9BE8B1",
    "#A08BED",
    "#FF99A1",
    "#85C3FF",
    "#FFAD85",
    "#85D8C4",
    "#B98BEE",
    "#FF9C85",
    "#A8FF99",
    "#A18BE3",
    "#FF85A1",
    "#8C9BFA",
    "#FFB3D9",
    "#85D1E0",
    "#FFB38A",
    "#88E8DB",
    "#FFD18A",
    "#FF9966",
    "#88FFAA",
  ],
  Monday: [
    "#00C875",
    "#FDAB3D",
    "#FF5AC4",
    "#A25DDC",
    "#FFCB00",
    "#68A4FF",
    "#FF158A",
    "#FF642E",
    "#FFAD1F",
    "#FF5AC4",
    "#A25DDC",
    "#FFCB00",
    "#68A4FF",
    "#FF158A",
    "#FF642E",
    "#FFAD1F",
    "#00C875",
    "#FDAB3D",
    "#FF5AC4",
    "#A25DDC",
    "#FFCB00",
    "#68A4FF",
    "#FF158A",
    "#FF642E",
    "#FFAD1F",
    "#FF5AC4",
    "#A25DDC",
    "#FFCB00",
    "#68A4FF",
    "#FF158A",
  ],
  Yellowish: [
    "#3BE8B0",
    "#FFB900",
    "#FF6F61",
    "#A1C4FD",
    "#C4E17F",
    "#F7D794",
    "#F5A623",
    "#F8E71C",
    "#7B92FF",
    "#FF6F61",
    "#A1C4FD",
    "#C4E17F",
    "#F7D794",
    "#F5A623",
    "#F8E71C",
    "#7B92FF",
    "#3BE8B0",
    "#FFB900",
    "#FF6F61",
    "#A1C4FD",
    "#C4E17F",
    "#F7D794",
    "#F5A623",
    "#F8E71C",
    "#7B92FF",
    "#FF6F61",
    "#A1C4FD",
    "#C4E17F",
    "#F7D794",
    "#F5A623",
  ],
  Tailwind500: [
    "#F97316",
    "#EF4444",
    "#FBBF24",
    "#FBBF24",
    "#F97316",
    "#FCA5A1",
    "#FDE68A",
    "#34D399",
    "#4ADE80",
    "#3B82F6",
    "#1D4ED8",
    "#4C51BF",
    "#A855F7",
    "#D946EF",
    "#F97316",
    "#EF4444",
    "#FBBF24",
    "#FBBF24",
    "#F97316",
    "#FCA5A1",
  ],
  Tailwind400: [
    "#FB923C",
    "#F87171",
    "#FBBF24",
    "#FBBF24",
    "#FCA5A1",
    "#FDE68A",
    "#34D399",
    "#4ADE80",
    "#60A5FA",
    "#3B82F6",
    "#9333EA",
    "#D946EF",
    "#FBBF24",
    "#F87171",
    "#FBBF24",
    "#FCA5A1",
    "#FDE68A",
    "#34D399",
    "#4ADE80",
    "#60A5FA",
  ],
  Tailwind300: [
    "#FBBF24",
    "#FCA5A1",
    "#FDE68A",
    "#A5F3FC",
    "#86EFAC",
    "#6EE7B7",
    "#34D399",
    "#A5F3FC",
    "#60A5FA",
    "#93C5FD",
    "#A78BFA",
    "#D8B4FE",
    "#FBBF24",
    "#FCA5A1",
    "#FDE68A",
    "#A5F3FC",
    "#86EFAC",
    "#6EE7B7",
    "#34D399",
    "#A5F3FC",
  ],
  Colorful: [
    "#FF6B6B", // Coral Red
    "#4ECDC4", // Medium Turquoise
    "#45B7D1", // Sky Blue
    "#96CEB4", // Sage Green
    "#FF8C42", // Mango Orange
    "#D4A5A5", // Rosy Brown
    "#9B5DE5", // Medium Purple
    "#4CB944", // Fresh Green
    "#FF6B8B", // Salmon Pink
    "#45B7AF", // Teal
    "#FFB347", // Pastel Orange
    "#20B2AA", // Light Sea Green
    "#FF69B4", // Hot Pink
    "#5BA8FF", // Cornflower Blue
    "#98D8D8", // Light Cyan
    "#FF9999", // Light Coral
    "#77DD77", // Pastel Green
    "#B19CD9", // Light Purple
    "#FFB366", // Light Orange
    "#99C5FF", // Light Blue
    "#FF99E6", // Light Pink
    "#C1E1C1", // Pale Green
    "#FFA07A", // Light Salmon
    "#87CEEB", // Sky Blue
    "#DDA0DD", // Plum
    "#98FB98", // Pale Green
    "#F08080", // Light Coral
    "#E0FFFF", // Light Cyan
    "#FFB6C1", // Light Pink
    "#E6E6FA", // Lavender
  ],
  StatisticCard: [
    "#60A5FA", // Blue 400
    "#F87171", // Red 400
    "#34D399", // Emerald 400
    "#A78BFA", // Violet 400
    "#F472B6", // Pink 400
    "#38BDF8", // Sky 400
    "#C084FC", // Purple 400
    "#22D3EE", // Cyan 400
    "#FB923C", // Orange 400
    "#4ADE80", // Green 400
    "#818CF8", // Indigo 400
    "#FB7185", // Rose 400
    "#2DD4BF", // Teal 400
    "#FACC15", // Yellow 400
    "#FBB344", // Amber 400
  ],
};

export function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

export function useHashColor(text: string, collectionKey: keyof ColorCollection): string {
  const colors = COLOR_COLLECTIONS[collectionKey];
  if (!colors) {
    throw new Error(`Color collection "${collectionKey}" not found`);
  }

  const hash = hashString(text);
  const colorIndex = hash % colors.length;
  return colors[colorIndex];
}

export function getContrastTextColor(
  backgroundColor: string,
  options: {
    mode?: "darken" | "lighten";
    amount?: number;
  } = {
    mode: "darken",
    amount: 0.6,
  },
): string {
  const baseColor = Color(backgroundColor);
  const { mode = "darken", amount = 0.7 } = options;

  return mode === "darken" ? baseColor.darken(amount).hex() : baseColor.lighten(amount).hex();
}

export function getTagColorPair(category: string) {
  const baseColorHex = useHashColor(category || "default", "Tailwind500");

  const baseColor = Color(baseColorHex);

  const backgroundColor = baseColor.lighten(0.5).hex();

  const backgroundColorObj = Color(backgroundColor);
  let textColor: string;

  if (backgroundColorObj.isLight()) {
    textColor = baseColor.darken(0.4).hex();
  } else {
    textColor = baseColor.lighten(0.8).hex();
  }

  return {
    backgroundColor,
    textColor,
  };
}

/**
 * Category color utilities for consistent tag styling
 * Used by ModernTagChip and tag management pages
 */
export function getCategoryColorByName(category: string): string {
  const hash = hashString(category);
  // Use soft hover colors (200 variants) - prettier and harmonious
  const colors = [
    "bg-blue-200",
    "bg-green-200",
    "bg-purple-200",
    "bg-pink-200",
    "bg-indigo-200",
    "bg-yellow-200",
    "bg-red-200",
    "bg-cyan-200",
    "bg-orange-200",
    "bg-teal-200",
    "bg-emerald-200",
    "bg-violet-200",
    "bg-fuchsia-200",
    "bg-rose-200",
    "bg-sky-200",
    "bg-lime-200",
  ];
  return colors[hash % colors.length];
}

export function getCategoryColorClasses(category: string) {
  const hash = hashString(category);
  const colorSets = [
    {
      bg: "bg-blue-100 text-blue-800 hover:bg-blue-200",
      border: "border-blue-800",
      dot: "bg-blue-200",
    },
    {
      bg: "bg-green-100 text-green-800 hover:bg-green-200",
      border: "border-green-800",
      dot: "bg-green-200",
    },
    {
      bg: "bg-purple-100 text-purple-800 hover:bg-purple-200",
      border: "border-purple-800",
      dot: "bg-purple-200",
    },
    {
      bg: "bg-pink-100 text-pink-800 hover:bg-pink-200",
      border: "border-pink-800",
      dot: "bg-pink-200",
    },
    {
      bg: "bg-indigo-100 text-indigo-800 hover:bg-indigo-200",
      border: "border-indigo-800",
      dot: "bg-indigo-200",
    },
    {
      bg: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
      border: "border-yellow-800",
      dot: "bg-yellow-200",
    },
    {
      bg: "bg-red-100 text-red-800 hover:bg-red-200",
      border: "border-red-800",
      dot: "bg-red-200",
    },
    {
      bg: "bg-cyan-100 text-cyan-800 hover:bg-cyan-200",
      border: "border-cyan-800",
      dot: "bg-cyan-200",
    },
    {
      bg: "bg-orange-100 text-orange-800 hover:bg-orange-200",
      border: "border-orange-800",
      dot: "bg-orange-200",
    },
    {
      bg: "bg-teal-100 text-teal-800 hover:bg-teal-200",
      border: "border-teal-800",
      dot: "bg-teal-200",
    },
    {
      bg: "bg-emerald-100 text-emerald-800 hover:bg-emerald-200",
      border: "border-emerald-800",
      dot: "bg-emerald-200",
    },
    {
      bg: "bg-violet-100 text-violet-800 hover:bg-violet-200",
      border: "border-violet-800",
      dot: "bg-violet-200",
    },
    {
      bg: "bg-fuchsia-100 text-fuchsia-800 hover:bg-fuchsia-200",
      border: "border-fuchsia-800",
      dot: "bg-fuchsia-200",
    },
    {
      bg: "bg-rose-100 text-rose-800 hover:bg-rose-200",
      border: "border-rose-800",
      dot: "bg-rose-200",
    },
    {
      bg: "bg-sky-100 text-sky-800 hover:bg-sky-200",
      border: "border-sky-800",
      dot: "bg-sky-200",
    },
    {
      bg: "bg-lime-100 text-lime-800 hover:bg-lime-200",
      border: "border-lime-800",
      dot: "bg-lime-200",
    },
  ];

  return colorSets[hash % colorSets.length];
}
