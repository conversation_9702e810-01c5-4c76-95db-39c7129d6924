import { ref, computed } from "vue";
import { transformDateRangeForDateOnlyFilter } from "@/utils/time-helper";

export interface CashFlowFilters {
  type?: "income" | "expense";
  state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED";
  from_date?: string;
  to_date?: string;
}

export function useCashFlowFilters() {
  // Filter states
  const typeFilter = ref<"income" | "expense" | null>(null);
  const stateFilter = ref<"PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null>(null);
  
  // Date range filter - default to today only
  const getDefaultDateRange = () => {
    const today = new Date();
    return [today, today];
  };
  
  const dateRange = ref<Date[] | Date | (Date | null)[] | null | undefined>(getDefaultDateRange());

  // Build filters object for API calls
  const buildFilters = (): CashFlowFilters => {
    const filters: CashFlowFilters = {};

    if (typeFilter.value) {
      filters.type = typeFilter.value;
    }

    if (stateFilter.value) {
      filters.state = stateFilter.value;
    }

    if (dateRange.value) {
      let dateRangeArray: Date[] | null = null;

      if (Array.isArray(dateRange.value)) {
        if (dateRange.value.length === 1 && dateRange.value[0]) {
          dateRangeArray = [dateRange.value[0], dateRange.value[0]];
        } else if (dateRange.value.length === 2) {
          const startDate = dateRange.value[0];
          const endDate = dateRange.value[1];

          if (startDate && endDate) {
            dateRangeArray = [startDate, endDate];
          } else if (startDate) {
            dateRangeArray = [startDate, startDate];
          }
        }
      } else if (dateRange.value instanceof Date) {
        dateRangeArray = [dateRange.value, dateRange.value];
      }

      if (dateRangeArray) {
        const dateFilter = transformDateRangeForDateOnlyFilter(dateRangeArray);
        if (dateFilter) {
          filters.from_date = dateFilter.from;
          filters.to_date = dateFilter.to;
        }
      }
    }

    return filters;
  };

  // Current filter status display
  const currentFilterStatus = computed(() => {
    const filters = [];

    if (typeFilter.value === "income") {
      filters.push("Thu nhập");
    } else if (typeFilter.value === "expense") {
      filters.push("Chi phí");
    }

    if (stateFilter.value === "PENDING") {
      filters.push("Chờ phê duyệt");
    } else if (stateFilter.value === "APPROVED") {
      filters.push("Đã phê duyệt");
    } else if (stateFilter.value === "PAID") {
      filters.push("Đã thanh toán");
    }

    if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length >= 1) {
      const startDate = dateRange.value[0];
      const endDate = dateRange.value[1] || startDate;

      if (startDate && endDate) {
        const formatDate = (date: Date) =>
          date.toLocaleDateString("vi-VN", { day: "2-digit", month: "2-digit" });

        if (startDate.toDateString() === endDate.toDateString()) {
          filters.push(`Ngày ${formatDate(startDate)}`);
        } else {
          filters.push(`${formatDate(startDate)} - ${formatDate(endDate)}`);
        }
      }
    }

    return filters.length > 0 ? filters.join(" • ") : "";
  });

  // Filter update handlers
  const updateTypeFilter = (type: "income" | "expense" | null) => {
    typeFilter.value = type;
  };

  const updateStateFilter = (state: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null) => {
    stateFilter.value = state;
  };

  const updateDateRange = (range: Date[] | Date | (Date | null)[] | null | undefined) => {
    dateRange.value = range;
  };

  const resetFilters = () => {
    typeFilter.value = null;
    stateFilter.value = null;
    dateRange.value = getDefaultDateRange();
  };

  return {
    // State
    typeFilter,
    stateFilter,
    dateRange,
    
    // Computed
    currentFilterStatus,
    
    // Methods
    buildFilters,
    updateTypeFilter,
    updateStateFilter,
    updateDateRange,
    resetFilters,
    getDefaultDateRange,
  };
}