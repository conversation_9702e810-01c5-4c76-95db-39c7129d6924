import { defineAsyncComponent,ref } from "vue";
import { useRouter } from "vue-router";

import type { Notification } from "@/api/bcare-types-v2";

// Global state for task drawer
const isTaskDrawerVisible = ref(false);
const currentTaskId = ref<number | null>(null);

// Lazy load TaskUpdateForm
const TaskUpdateForm = defineAsyncComponent(() => import('@/pages/task/components/TaskUpdateForm.vue'));

export function useNotificationNavigation() {
  const router = useRouter();

  const navigateToNotification = async (notification: Notification, showModalOnly = true) => {
    // Priority 1: Use entity type and ID for structured navigation
    if (notification.entity_type && notification.entity_id) {
      // Handle task notifications specially to open task drawer
      if (notification.entity_type === 'task') {
        if (showModalOnly) {
          currentTaskId.value = notification.entity_id;
          isTaskDrawerVisible.value = true;
        } else {
          const currentRoute = router.currentRoute.value;

          // If we're already on the task page, open task drawer directly
          if (currentRoute.name === 'top-menu-task') {
            currentTaskId.value = notification.entity_id;
            isTaskDrawerVisible.value = true;
          } else {
            // Navigate to task page with task ID
            await router.push("/task/" + notification.entity_id);
          }
        }
      } else {
        // Default navigation for other entity types
        const routeMap: Record<string, { name: string; params?: any }> = {
          deal: { name: 'top-menu-deals' },
          person: { name: 'top-menu-customer-profile', params: { id: notification.entity_id } },
          appointment: { name: 'top-menu-appointments-doctor' },
        };

        const route = routeMap[notification.entity_type];
        if (route) {
          await router.push(route);
        }
      }
    }
    // Priority 2: Fallback to metadata route for custom navigation when no entity type
    else if (notification.metadata?.route) {
      await router.push(notification.metadata.route);
    }
  };

  const closeTaskDrawer = () => {
    isTaskDrawerVisible.value = false;
    currentTaskId.value = null;
  };

  return {
    navigateToNotification,
    // Task drawer state and controls
    TaskUpdateForm,
    isTaskDrawerVisible,
    currentTaskId,
    closeTaskDrawer,
  };
}
