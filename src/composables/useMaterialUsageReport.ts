import { computed, type Ref } from "vue";
import { round } from "lodash";
import { isCustomOperation, getUsageIdentifier, formatOperationNameFromKey } from "@/utils/operation-utils";
import type { MaterialUsageResponse, Material, Operation } from "@/api/bcare-types-v2";

/**
 * Column header for the report grid
 */
export interface ReportColumn {
  id: string;
  label: string;
  isCustom: boolean;
}

/**
 * Row header for the report grid
 */
export interface ReportRow {
  id: number;
  label: string;
  unit?: string;
}

/**
 * Cell data structure for the report grid
 */
export interface ReportCell {
  value: number;
  formattedValue: string;
}

/**
 * Processed report data structure
 */
export interface ReportData {
  columns: ReportColumn[];
  rows: ReportRow[];
  cells: Record<number, Record<string, ReportCell>>;
  totals: Record<number, ReportCell>;
  isEmpty: boolean;
}

/**
 * Props interface for the composable
 */
interface UseMaterialUsageReportProps {
  materialUsages: Ref<MaterialUsageResponse[]>;
  materials: Ref<Material[]>;
  operations: Ref<Operation[]>;
  getMaterialNameById: (id: number) => string | undefined;
  getOperationById: (id: number) => Operation | undefined;
  getMaterialById: (id: number) => Material | undefined;
}

/**
 * Composable for transforming material usage data into report structure
 * Pure transformation logic - no API calls or side effects
 */
export function useMaterialUsageReport(props: UseMaterialUsageReportProps) {
  /**
   * Helper function to get material unit
   */
  const getMaterialUnit = (materialId: number): string => {
    const material = props.getMaterialById(materialId);
    return material?.unit || "";
  };

  /**
   * Helper function to format total numbers (remove trailing zeros)
   */
  const formatTotal = (value: number): string => {
    return parseFloat(value.toString()).toString();
  };

  /**
   * Main computed property that transforms raw data into report structure
   */
  const reportData = computed((): ReportData => {
    const usages = props.materialUsages.value;

    if (!usages.length) {
      return {
        columns: [],
        rows: [],
        cells: {},
        totals: {},
        isEmpty: true,
      };
    }

    // Extract unique identifiers
    const operationIdentifiers = new Set<string>();
    const materialIds = new Set<number>();

    usages.forEach((usage) => {
      const identifier = getUsageIdentifier(usage);
      if (identifier) {
        operationIdentifiers.add(identifier);
      }
      materialIds.add(usage.material_id);
    });

    // Build columns array (operations)
    const columns: ReportColumn[] = Array.from(operationIdentifiers).map((identifier) => {
      const isCustom = isCustomOperation(identifier);
      let label: string;

      if (isCustom) {
        // For custom operations, parse the key to create a readable name
        label = formatOperationNameFromKey(identifier);
      } else {
        // For DB operations, use the operation hook
        const numericId = parseInt(identifier);
        label = props.getOperationById(numericId)?.name || `Thao tác #${identifier}`;
      }

      return {
        id: identifier,
        label,
        isCustom,
      };
    });

    // Build rows array (materials)
    const rows: ReportRow[] = Array.from(materialIds).map((id) => ({
      id,
      label: props.getMaterialNameById(id) || `Vật tư #${id}`,
      unit: getMaterialUnit(id),
    }));

    // Build cells matrix and calculate totals
    const cells: Record<number, Record<string, ReportCell>> = {};
    const totals: Record<number, ReportCell> = {};

    usages.forEach((usage) => {
      const materialId = usage.material_id;
      const operationIdentifier = getUsageIdentifier(usage);

      if (!cells[materialId]) {
        cells[materialId] = {};
      }

      if (operationIdentifier) {
        cells[materialId][operationIdentifier] = {
          value: usage.used_quantity,
          formattedValue: formatTotal(usage.used_quantity),
        };
      }

      // Calculate totals with precision handling
      if (!totals[materialId]) {
        totals[materialId] = { value: 0, formattedValue: "0" };
      }
      
      // Use lodash round to handle floating point precision issues (5 decimal places)
      const newTotal = round(totals[materialId].value + usage.used_quantity, 5);
      totals[materialId] = {
        value: newTotal,
        formattedValue: formatTotal(newTotal),
      };
    });

    return {
      columns,
      rows,
      cells,
      totals,
      isEmpty: false,
    };
  });

  return {
    reportData,
  };
}