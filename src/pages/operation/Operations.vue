<template>
  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách nội dung điều trị"
      :columns="columns"
      :data="tableState.operations"
      :loading="false"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true, custom: true }"
      :custom-action-items="getCustomActionItems"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium">
            Danh sách nội dung điều trị ({{ tableState.total }})
          </span>
        </div>
      </template>

      <template #right-header>
        <Button
          severity="primary"
          icon="pi pi-plus"
          label="Thêm nội dung điều trị"
          @click="handleCreate"
        />
      </template>

      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-cog text-blue-500"></i>
          <span class="font-medium">{{ data.name }}</span>
        </div>
      </template>

      <!-- <template #duration="{ data }">
        <div class="flex items-center gap-1">
          <i class="pi pi-clock text-gray-500"></i>
          <span>{{ data.duration }} phút</span>
        </div>
      </template> -->

      <template #status="{ data }">
        <div class="flex items-center gap-2">
          <i
            :class="[
              data.status === 2 ? 'pi pi-check-circle text-green-500' : 'pi pi-ban text-gray-500',
            ]"
          ></i>
          <span :class="[data.status === 2 ? 'text-green-600' : 'text-gray-600', 'font-medium']">
            {{ data.status === 2 ? "Active" : "Inactive" }}
          </span>
        </div>
      </template>
    </DataTable>
  </div>

  <OperationFormDrawer
    v-model:visible="showOperationForm"
    :operation-id="selectedOperationId"
    :initial-active-section="initialActiveSection"
    @success="handleFormSuccess"
  />
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import type { MenuItem } from "primevue/menuitem";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import type { OperationListResponse, OperationResponse } from "@/api/bcare-types-v2";
import { DataTable, type ColumnDefinition } from "@/components/DataTable";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useFilterList } from "@/hooks/useFilterList";
import useOperation from "@/hooks/useOperation";

import OperationFormDrawer from "./components/OperationFormDrawer.vue";

const { listOperations, deleteOperation } = useOperation({ autoLoad: false });

// State
const tableState = ref<OperationListResponse>({
  total: 0,
  total_page: 0,
  operations: [],
});
const perPage = ref<number>(10);
const currentPage = ref(1);

// Columns definition
const columns = computed<ColumnDefinition<OperationResponse>[]>(() => [
  {
    field: "name",
    header: "Tên nội dung điều trị",
    filterType: "text",
    filterPlaceholder: "Tìm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  // {
  //   field: "duration",
  //   header: "Thời gian",
  //   showFilterMenu: false,
  // },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Active", value: 2 },
      { title: "Inactive", value: 1 },
    ],
  },
]);

// Filter configurations
const filterConfigs = {
  name: { field: "search", isFilter: false },
  status: {
    field: "status",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
};

const defaultFilters = {
  page: 1,
  page_size: perPage.value,
};

// Create a debounced version of loadList to prevent rapid calls
let loadListTimeout: NodeJS.Timeout | null = null;

const debouncedLoadList = (filters: Record<string, any>) => {
  if (loadListTimeout) {
    clearTimeout(loadListTimeout);
  }
  loadListTimeout = setTimeout(() => {
    loadList(filters);
  }, 100); // 100ms debounce
};

const { filters, currentFilterPayload } = useFilterList(
  debouncedLoadList,
  filterConfigs,
  defaultFilters,
);

// Handlers
const handlePageChange = async (event: { first: number; rows: number }) => {
  try {
    const page = Math.floor(event.first / event.rows) + 1;
    currentPage.value = page;

    // Gọi trực tiếp API với page mới, không thông qua useFilterList
    const response = await listOperations({
      ...currentFilterPayload.value,
      page,
      page_size: perPage.value,
    });

    if (response) {
      tableState.value = {
        operations: response.operations || [],
        total: response.total || 0,
        total_page: response.total_page || 0,
      };
    }
  } catch (error) {
    console.error("Error changing page:", error);
    // Set empty state on error
    tableState.value = {
      operations: [],
      total: 0,
      total_page: 0,
    };
  }
};

const handlePerPageChange = (newPerPage: number) => {
  perPage.value = newPerPage;
  loadList({ ...currentFilterPayload.value, page_size: newPerPage });
};

// Add loading flag to prevent duplicate calls
const isLoading = ref(false);

const loadList = async (filters: Record<string, any>) => {
  if (isLoading.value) {
    return;
  }

  isLoading.value = true;
  try {
    const response = await listOperations({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });

    if (response) {
      tableState.value = {
        operations: response.operations || [],
        total: response.total || 0,
        total_page: response.total_page || 0,
      };

      if (response.operations?.length === 0) {
      }
    }
  } catch (error) {
    tableState.value = {
      operations: [],
      total: 0,
      total_page: 0,
    };
  } finally {
    isLoading.value = false;
  }
};

const { confirm } = useConfirmTippy();

const handleDelete = async (operation: OperationResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa nội dung điều trị",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteOperation({ id: operation.id });
      loadList(currentFilterPayload.value);
    },
  });
};

onUnmounted(() => {
  if (loadListTimeout) {
    clearTimeout(loadListTimeout);
  }
});

// Watchers
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

const showOperationForm = ref(false);
const selectedOperationId = ref<number>();
const initialActiveSection = ref<"basic" | "products" | "materials">("basic");

const handleCreate = () => {
  selectedOperationId.value = undefined;
  initialActiveSection.value = "basic";
  showOperationForm.value = true;
};

const handleEdit = (operation: OperationResponse) => {
  selectedOperationId.value = operation.id;
  initialActiveSection.value = "basic";
  showOperationForm.value = true;
};

const handleProductsManagement = (operation: OperationResponse) => {
  selectedOperationId.value = operation.id;
  initialActiveSection.value = "products";
  showOperationForm.value = true;
};

const handleMaterialsManagement = (operation: OperationResponse) => {
  selectedOperationId.value = operation.id;
  initialActiveSection.value = "materials";
  showOperationForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};

// Custom action items for DataTable menu
const getCustomActionItems = (operation: OperationResponse): MenuItem[] => [
  {
    label: "Liên kết với sản phẩm",
    icon: "pi pi-shopping-cart",
    command: () => handleProductsManagement(operation),
  },
  {
    label: "Định mức vật tư",
    icon: "pi pi-box",
    command: () => handleMaterialsManagement(operation),
  },
];
</script>
