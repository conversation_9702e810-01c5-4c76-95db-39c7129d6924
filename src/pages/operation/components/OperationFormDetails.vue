<template>
  <div class="space-y-4">
    <FormField label="Thời gian thực hiện (ph<PERSON><PERSON>)" icon="pi pi-clock">
      <InputNumber
        v-model="formData.duration"
        :min="0"
        :step="1"
        showButtons
        class="w-full"
        placeholder="Nhập thời gian thực hiện"
        :class="{ 'p-invalid': errors.duration }"
      />
      <small class="p-error">{{ errors.duration }}</small>
    </FormField>

    <FormField label="Nhóm nội dung điều trị" icon="pi pi-tags">
      <InputChips
        v-model="formData.group"
        placeholder="Nhập nhóm nội dung điều trị và nhấn Enter"
        class="w-full"
        :class="{ 'p-invalid': errors.group }"
      />
      <small class="p-error">{{ errors.group }}</small>
      <small class="mt-1 block text-xs text-gray-500">
        Nh<PERSON><PERSON> tên nhóm và nhấn Enter để thêm. <PERSON><PERSON> thể thêm nhiều nhóm (không bắt buộc).
      </small>
    </FormField>

    <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
      <div class="flex items-start gap-3">
        <i class="pi pi-info-circle mt-0.5 text-blue-500"></i>
        <div class="text-sm text-blue-700">
          <p class="mb-1 font-medium">Gợi ý về nội dung điều trị:</p>
          <ul class="list-inside list-disc space-y-1 text-xs">
            <li>Thời gian thực hiện nên được tính bằng phút</li>
            <li>Nhóm nội dung điều trị giúp phân loại và tìm kiếm dễ dàng hơn</li>
            <li>Có thể thêm nhiều nhóm cho một nội dung điều trị (không bắt buộc)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OperationAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: OperationAddRequest & { status?: number; description?: string };
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (
    e: "update:formData",
    value: OperationAddRequest & { status?: number; description?: string },
  ): void;
}>();
</script>
