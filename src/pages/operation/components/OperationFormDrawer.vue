<template>
  <Drawer
    :closable="false"
    :modal="true"
    :style="{ width: '60rem' }"
    :visible="visible"
    blockScroll
    position="right"
    @hide="onClose"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <div class="flex w-full items-center justify-between">
        <span class="text-lg font-medium">
          {{ isEdit ? "Cập nhật nội dung điều trị" : "Thêm nội dung điều trị" }}
        </span>
        <SelectButton
          v-if="isEdit"
          v-model="activeSection"
          :options="sectionOptions"
          optionLabel="label"
          optionValue="value"
          dataKey="value"
        >
          <template #option="{ option }">
            <div class="flex items-center gap-2">
              <i :class="option.icon"></i>
              <span>{{ option.label }}</span>
            </div>
          </template>
        </SelectButton>
      </div>
    </template>

    <!-- Basic Information Section -->
    <div v-if="activeSection === 'basic'" class="h-full">
      <form @submit.prevent="onSubmit">
        <div class="space-y-6">
          <BasicInformation v-model:formData="formData" :errors="errors" />
          <Divider class="!my-6" />
          <DetailsInformation v-model:formData="formData" :errors="errors" />
        </div>
      </form>
    </div>

    <!-- Product Assignment Section -->
    <div v-else-if="activeSection === 'products'" class="h-full">
      <OperationProductAssignment
        ref="productAssignmentRef"
        :operation-id="operationId"
        :is-edit="isEdit"
        :is-loading="isLoading"
        @apply-products="onApplyProducts"
        @update:selectedCount="selectedProductCount = $event"
      />
    </div>

    <!-- Material Assignment Section -->
    <div v-else-if="activeSection === 'materials'" class="h-full">
      <OperationMaterialList
        v-if="isDataLoaded"
        ref="materialListRef"
        :operation-id="operationId"
        @save="onMaterialSave"
      />
      <div v-else class="flex h-full items-center justify-center">
        <i class="pi pi-spin pi-spinner text-2xl text-blue-500"></i>
      </div>
    </div>

    <template #footer>
      <div class="flex w-full items-center justify-between">
        <div class="text-sm text-gray-600">
          <span v-if="activeSection === 'products' && selectedProductCount > 0">
            Đã chọn {{ selectedProductCount }} sản phẩm
          </span>
        </div>
        <div class="flex gap-3">
          <Button icon="pi pi-times" label="Hủy" outlined severity="danger" @click="onClose" />
          <Button
            v-if="activeSection === 'basic'"
            autofocus
            icon="pi pi-save"
            label="Lưu"
            @click="onSubmit"
          />
          <Button
            v-if="activeSection === 'products'"
            icon="pi pi-save"
            label="Lưu thay đổi"
            :disabled="!productAssignmentRef?.hasProductChanges"
            :loading="isLoading"
            @click="productAssignmentRef?.onApplyProducts()"
          />
          <Button
            v-if="activeSection === 'materials'"
            icon="pi pi-save"
            label="Lưu thay đổi"
            :disabled="!operationId"
            :loading="materialListRef?.isLoading"
            @click="materialListRef?.handleSave()"
          />
        </div>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import type { OperationAddRequest } from "@/api/bcare-types-v2";
import { useFormValidation } from "@/composables/useFormValidation";
import useOperation from "@/hooks/useOperation";
import { useMaterialQuotaStore } from "@/stores/material-quota-store";

import BasicInformation from "./OperationFormBasicInfo.vue";
import DetailsInformation from "./OperationFormDetails.vue";
import OperationProductAssignment from "./OperationProductAssignment.vue";
import OperationMaterialList from "./OperationMaterialList.vue";

interface Props {
  visible: boolean;
  operationId?: number;
  initialActiveSection?: "basic" | "products" | "materials";
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const { addOperation, updateOperation, getOperation, bulkSetProductOperation, isLoading } =
  useOperation();
const materialQuotaStore = useMaterialQuotaStore();
const isEdit = computed(() => !!props.operationId);

// Section management
const activeSection = ref<"basic" | "products" | "materials">("basic");
const isDataLoaded = ref(false);

// Section options for SelectButton
const sectionOptions = computed(() => [
  {
    label: "Thông tin cơ bản",
    value: "basic",
    icon: "pi pi-info-circle",
  },
  {
    label: "Liên kết với sản phẩm",
    value: "products",
    icon: "pi pi-shopping-cart",
    disabled: !isEdit.value,
  },
  {
    label: "Định mức vật tư",
    value: "materials",
    icon: "pi pi-box",
    disabled: !isEdit.value,
  },
]);

const formData = ref<OperationAddRequest & { status?: number; description?: string }>({
  name: "",
  group: [],
  duration: 0,
  status: 2, // Default to Active
  description: "",
});

const { errors, validateForm } = useFormValidation({
  name: [
    {
      validate: (v) => !!v && v.length >= 2,
      message: "Tên nội dung điều trị phải có ít nhất 2 ký tự",
    },
  ],
  duration: [{ validate: (v) => v >= 0, message: "Thời gian thực hiện phải lớn hơn hoặc bằng 0" }],
});

// Product assignment component ref and state
const productAssignmentRef = ref();
const selectedProductCount = ref(0);

// Material assignment component ref
const materialListRef = ref();

const onApplyProducts = async (productIds: number[]) => {
  if (!props.operationId) return;

  try {
    await bulkSetProductOperation({
      operationId: props.operationId,
      productIds,
    });

    emit("success");
    // Stay on the same tab after applying products
  } catch (error) {
    console.error("Error applying bulk product assignment:", error);
  }
};

const onMaterialSave = async () => {
  // Refresh operation data by calling operation/get again
  if (props.operationId) {
    await populateForm(props.operationId);
  }
  emit("success");
  // Stay on the same tab after saving materials
};

const resetForm = () => {
  formData.value = {
    name: "",
    group: [],
    duration: 0,
    status: 2,
    description: "",
  };

  Object.keys(errors.value).forEach((key) => {
    errors.value[key] = "";
  });

  activeSection.value = props.initialActiveSection || "basic";
  selectedProductCount.value = 0;
  isDataLoaded.value = false;
};

const populateForm = async (operationId: number) => {
  isDataLoaded.value = false;
  const operation = await getOperation({ id: operationId });
  if (operation) {
    const { name, group, duration, status, operation_materials } = operation;

    formData.value = {
      name,
      group: group || [],
      duration,
      status,
      description: "", // OperationResponse doesn't have description field
    };

    // Update operation materials in the store if they exist in the response
    if (operation_materials && operation_materials.length > 0) {
      materialQuotaStore.updateMaterialQuotasFromExternal(operationId, operation_materials);
    }
  }
  isDataLoaded.value = true;
};

// Combined watcher for operationId and visible to handle form population
watch(
  [() => props.operationId, () => props.visible],
  async ([newOperationId, newVisible], [oldOperationId, oldVisible]) => {
    // Reset form when operationId changes from value to null/undefined
    if (oldOperationId && !newOperationId) {
      resetForm();
      return;
    }

    // Reset form when closing drawer
    if (oldVisible && !newVisible) {
      resetForm();
      return;
    }

    // Populate form when:
    // 1. Drawer opens with operationId, OR
    // 2. OperationId changes while drawer is open
    if (
      newVisible &&
      newOperationId &&
      ((!oldVisible && newVisible) || // Drawer just opened
        newOperationId !== oldOperationId) // OperationId changed
    ) {
      await populateForm(newOperationId);
    }

    // Set initial active section when drawer opens
    if (newVisible && !oldVisible && props.initialActiveSection) {
      activeSection.value = props.initialActiveSection;
    }
  },
  { immediate: true },
);

// Watch for section changes - prevent switching to products/materials if not in edit mode
watch(
  () => activeSection.value,
  (newSection) => {
    if ((newSection === "products" || newSection === "materials") && !isEdit.value) {
      activeSection.value = "basic";
    }
  },
);

const onSubmit = async () => {
  if (!validateForm(formData.value)) return;

  try {
    // Create the request object with only the required fields for the API
    const requestData: OperationAddRequest = {
      name: formData.value.name,
      group: formData.value.group,
      duration: formData.value.duration,
    };

    if (isEdit.value) {
      const res = await updateOperation({
        id: props.operationId!,
        ...requestData,
      });
      if (res) {
        emit("success");
        onClose();
      }
    } else {
      const res = await addOperation(requestData);
      if (res) {
        emit("success");
        // For new operations, we could switch to product tab or close
        // For now, let's close the drawer
        onClose();
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const onClose = () => {
  emit("update:visible", false);
  // Don't call resetForm here as it will be handled by the visible watcher
};
</script>
