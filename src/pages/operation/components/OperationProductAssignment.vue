<template>
  <div class="h-full">
    <!-- Not Edit State -->
    <div v-if="!isEdit" class="p-8 text-center">
      <i class="pi pi-info-circle mb-4 block text-4xl text-primary"></i>
      <p><PERSON><PERSON> lòng lưu nội dung điều trị trước khi liên kết với sản phẩm.</p>
    </div>

    <!-- Edit State -->
    <div v-else class="flex flex-col gap-4">
      <!-- Product Selection Section -->
      <div class="rounded-lg bg-gray-50 p-4">
        <h4 class="text-md mb-3 font-medium">Chọn sản phẩm liên kết</h4>
        <div class="flex flex-col gap-2">
          <!-- Search Input (styled like MultiSelect) -->
          <div class="relative">
            <div
              ref="searchTriggerRef"
              class="flex w-full cursor-pointer items-center justify-between rounded-md border border-gray-300 bg-white p-2 text-sm transition-colors hover:border-gray-400 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              :class="{
                'border-danger focus:border-danger focus:ring-danger':
                  selectedProducts.length === 0,
              }"
              @click="toggleProductSearch"
            >
              <div class="flex flex-1 flex-wrap items-center gap-1">
                <!-- No products selected -->
                <span v-if="selectedProducts.length === 0" class="text-muted-foreground">
                  Chọn sản phẩm...
                </span>

                <!-- 1-2 products: show individual names -->
                <template v-else-if="selectedProducts.length <= 2">
                  <span
                    v-for="product in selectedProducts"
                    :key="product.id"
                    class="inline-flex items-center gap-1 rounded bg-info/10 px-2 py-1 text-xs font-medium text-info"
                  >
                    {{ product.name }}
                    <button
                      @click.stop="removeProduct(product)"
                      class="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-info/20"
                    >
                      <i class="pi pi-times text-[10px]"></i>
                    </button>
                  </span>
                </template>

                <!-- 3+ products: show count -->
                <span
                  v-else
                  class="inline-flex items-center gap-1 rounded bg-info/10 px-2 py-1 text-xs font-medium text-info"
                >
                  {{ selectedProducts.length }} sản phẩm đã chọn
                </span>
              </div>

              <div class="ml-2 flex items-center">
                <i class="pi pi-chevron-down text-muted-foreground"></i>
              </div>
            </div>

            <SearchProduct
              ref="searchProductRef"
              v-model:selectedProducts="selectedProducts"
              :multiple="true"
            />
          </div>

          <small v-if="selectedProducts.length === 0" class="text-xs text-danger">
            Vui lòng chọn ít nhất một sản phẩm
          </small>
        </div>
      </div>

      <!-- Selected Products List -->
      <div v-if="selectedProducts.length > 0" class="rounded-lg border border-gray-200">
        <div class="rounded-t-lg border-b border-gray-200 bg-gray-50 p-4">
          <h4 class="text-md font-medium">Sản phẩm đã liên kết</h4>
        </div>

        <div class="divide-y divide-gray-200">
          <div
            v-for="product in selectedProducts"
            :key="product.id"
            class="flex items-center justify-between p-2 px-4 hover:bg-gray-50"
          >
            <div class="flex items-center gap-3">
              <i class="pi pi-shopping-cart text-primary" />
              <div>
                <div class="flex items-center gap-2 font-medium">
                  {{ product.name }}
                  <span class="text-muted-foreground">#{{ product.code }}</span>
                </div>
                <div class="flex items-center gap-2 text-sm">
                  <Term :color-key="getProductTypeLabel(product.type)" size="sm" variant="soft">
                    {{ getProductTypeLabel(product.type) }}
                  </Term>
                  <Money v-if="product.price" :amount="product.price" size="small" />
                </div>
              </div>
            </div>

            <div class="flex items-center gap-3">
              <div
                v-if="isProductNew(product.id)"
                class="flex items-center gap-1 rounded-full bg-success/10 px-2 py-1 text-xs text-success"
              >
                <i class="pi pi-plus text-[10px]"></i>
                <span>Thêm mới</span>
              </div>

              <Button
                icon="pi pi-trash"
                severity="danger"
                text
                @click="removeProduct(product)"
                v-tooltip.top="'Xóa'"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import type { Product } from "@/api/bcare-types-v2";
import { PRODUCT_TYPES } from "@/api/product";
import Money from "@/base-components/Money.vue";
import Term from "@/base-components/Term/Term.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import useOperation from "@/hooks/useOperation";
import useProduct from "@/hooks/useProduct";

interface Props {
  operationId?: number;
  isEdit: boolean;
  isLoading: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "apply-products", productIds: number[]): void;
  (e: "update:selectedCount", count: number): void;
}>();

const { getProductOperations } = useOperation();
const { getProductById, products, isLoading: isProductsLoading } = useProduct({ autoLoad: true });

// Component refs
const searchTriggerRef = ref();
const searchProductRef = ref();

// Product management state
const selectedProducts = ref<Product[]>([]);

// Computed properties

const currentProductIds = computed((): number[] => {
  if (!props.operationId) return [];
  const productOperations = getProductOperations(props.operationId);
  return productOperations ? productOperations.map((po) => po.product_id) : [];
});

const selectedProductIds = computed((): number[] => {
  return selectedProducts.value.map((product) => product.id);
});

const productsToAdd = computed((): number[] => {
  return selectedProductIds.value.filter((id) => !currentProductIds.value.includes(id));
});

const productsToRemove = computed((): number[] => {
  return currentProductIds.value.filter((id) => !selectedProductIds.value.includes(id));
});

const hasProductChanges = computed((): boolean => {
  return productsToAdd.value.length > 0 || productsToRemove.value.length > 0;
});

// Methods
const isProductNew = (productId: number): boolean => {
  return productsToAdd.value.includes(productId);
};

const toggleProductSearch = (event: Event) => {
  if (searchProductRef.value?.popoverRef) {
    searchProductRef.value.popoverRef.toggle(event);
  }
};

const removeProduct = (productToRemove: Product) => {
  selectedProducts.value = selectedProducts.value.filter(
    (product) => product.id !== productToRemove.id,
  );
};

const getProductTypeLabel = (type?: string): string => {
  switch (type) {
    case PRODUCT_TYPES.SERVICE:
      return "Dịch vụ";
    case PRODUCT_TYPES.ITEM:
      return "Sản phẩm";
    case PRODUCT_TYPES.GIFT:
      return "Quà tặng";
    default:
      return "Khác";
  }
};

// Load current products when operation changes
const loadCurrentProducts = () => {
  if (!props.operationId) {
    selectedProducts.value = [];
    return;
  }

  const productOperations = getProductOperations(props.operationId);
  if (productOperations && productOperations.length > 0) {
    const currentProducts: Product[] = [];
    productOperations.forEach((po) => {
      const product = getProductById(po.product_id);
      if (product) {
        currentProducts.push(product);
      }
    });
    selectedProducts.value = currentProducts;
  } else {
    selectedProducts.value = [];
  }
};

// Watch for products loading completion and operation changes
watch(
  [() => currentProductIds.value, () => products.value.length, () => isProductsLoading.value],
  ([, productsLength, loading]) => {
    // Only load when products are available and not loading
    if (!loading && productsLength > 0) {
      loadCurrentProducts();
    }
  },
  { immediate: true },
);

// Watch for selected products count changes
watch(
  () => selectedProducts.value.length,
  (count) => {
    emit("update:selectedCount", count);
  },
  { immediate: true },
);

defineExpose({
  hasProductChanges,
  selectedProductIds,
  onApplyProducts: () => {
    emit("apply-products", selectedProductIds.value);
  },
});
</script>
