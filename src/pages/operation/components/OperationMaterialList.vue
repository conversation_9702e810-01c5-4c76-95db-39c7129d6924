<template>
  <div class="flex flex-col gap-6">
    <!-- Material Management Section -->
    <div class="flex flex-col gap-4">
      <!-- Add Material Section -->
      <div class="rounded-lg bg-gray-50 p-4">
        <h4 class="text-md mb-3 font-medium text-gray-900">Thêm vật tư</h4>
        <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
          <Select
            v-model="newMaterial.material_id"
            :options="availableMaterials"
            option-label="name"
            option-value="id"
            placeholder="Chọn vật tư"
            filter
            fluid
            showClear
            labelClass="flex items-center gap-2"
          />
          <InputNumber
            v-model="newMaterial.quantity"
            placeholder="Số lượng"
            :min="0"
            :max-fraction-digits="5"
            :step="0.1"
            fluid
          />
          <Button
            label="Thêm"
            icon="pi pi-plus"
            @click="addMaterial"
            :disabled="!newMaterial.material_id || newMaterial.quantity == null"
          />
        </div>
      </div>

      <!-- Current Materials List -->
      <div class="rounded-lg border border-gray-200">
        <div class="rounded-t-lg border-b border-gray-200 bg-gray-50 p-4">
          <h4 class="text-md font-medium text-gray-900">
            Vật tư đã được định mức ({{ currentMaterials.length }})
          </h4>
        </div>

        <div v-if="currentMaterials.length === 0" class="p-8 text-center">
          <i class="pi pi-box mb-3 text-3xl text-gray-300"></i>
          <p class="text-gray-500">Chưa có vật tư nào được thiết lập</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="(material, index) in currentMaterials"
            :key="`${material.material_id}-${index}`"
            class="flex items-center justify-between p-2 px-4 hover:bg-gray-50"
          >
            <div class="flex items-center gap-3">
              <i class="pi pi-box text-blue-500" />
              <div>
                <div class="flex items-center gap-2 font-medium text-gray-900">
                  {{ getMaterialName(material.material_id) }}
                  <span class="text-gray-500">#{{ material.material_id }}</span>
                </div>
              </div>
            </div>

            <div class="flex items-center gap-3">
              <InputNumber
                v-model="material.quantity"
                :min="0"
                :max-fraction-digits="5"
                :step="0.1"
                size="small"
              />
              <Button
                icon="pi pi-trash"
                severity="danger"
                text
                @click="removeMaterial(index)"
                v-tooltip.top="'Xóa'"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

import type { OperationMaterialSetItem } from "@/api/bcare-types-v2";
import useMaterialQuota from "@/hooks/useMaterialQuota";
import useMaterial from "@/hooks/useMaterial";

interface Props {
  operationId?: number | null;
}

interface Emits {
  (e: "save"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Composables
const { bulkSetMaterialQuotas, getMaterialQuotasForOperation, isLoading } = useMaterialQuota({
  autoLoad: false,
});
const { materials, getMaterialNameById } = useMaterial();

// State
const currentMaterials = ref<OperationMaterialSetItem[]>([]);
const newMaterial = ref<OperationMaterialSetItem>({
  material_id: 0,
  quantity: 1,
});

// Computed
const availableMaterials = computed(() => {
  const usedMaterialIds = currentMaterials.value.map((m) => m.material_id);
  return materials.value.filter((material) => !usedMaterialIds.includes(material.id));
});

// Methods
const getMaterialName = (materialId: number): string => {
  return getMaterialNameById(materialId) || `Vật tư #${materialId}`;
};

const loadCurrentMaterials = () => {
  if (!props.operationId) {
    currentMaterials.value = [];
    return;
  }

  const materialQuotas = getMaterialQuotasForOperation(props.operationId);
  currentMaterials.value = materialQuotas.map((mq) => ({
    material_id: mq.material_id,
    quantity: mq.quantity,
  }));
};

const addMaterial = () => {
  console.log(newMaterial.value);
  if (!newMaterial.value.material_id || newMaterial.value.quantity == null) {
    return;
  }

  // Check if material already exists
  const existingIndex = currentMaterials.value.findIndex(
    (m) => m.material_id === newMaterial.value.material_id,
  );

  if (existingIndex >= 0) {
    // Update existing material quantity
    currentMaterials.value[existingIndex].quantity = newMaterial.value.quantity;
  } else {
    // Add new material
    currentMaterials.value.push({ ...newMaterial.value });
  }

  // Reset form
  newMaterial.value = {
    material_id: 0,
    quantity: 1,
  };
};

const removeMaterial = (index: number) => {
  currentMaterials.value.splice(index, 1);
};

const handleSave = async () => {
  if (!props.operationId) {
    return;
  }

  try {
    await bulkSetMaterialQuotas({
      operation_id: props.operationId,
      materials: currentMaterials.value.filter((m) => m.quantity >= 0),
    });
    emit("save");
  } catch (error) {
    console.error("Error saving bulk operation materials:", error);
  }
};

// Watchers
watch(
  () => props.operationId,
  () => {
    loadCurrentMaterials();
  },
  { immediate: true },
);

// Lifecycle
onMounted(() => {
  loadCurrentMaterials();
});

// Expose functions and state for parent component
defineExpose({
  handleSave,
  isLoading,
});
</script>
