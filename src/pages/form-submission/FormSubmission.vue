<script lang="ts" setup>
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, ref } from "vue";

import { FormSubmissionResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { DataTable } from "@/components/DataTable";
import DataTableFooter from "@/components/DataTable/DataTableFooter.vue";
import { PersonCard } from "@/components/Person/";
import PopSetting from "@/components/Settings/PopSetting.vue";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { useFormSubmissionQuery } from "@/hooks/useFormSubmissionQuery";
import usePerson from "@/hooks/usePerson";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import { useToastStore } from "@/stores/toast-store";
import {
  formSubmissionBaseFilters,
  formSubmissionColumns,
  formSubmissionFilterConfigs,
} from "@/constants/columns/form-submission-columns";

// Local pagination state
const page = ref(1);
const rowsPerPage = ref(30);
const rowsPerPageOptions = [30, 50, 100];

const { submissions, total, fetchSubmissions, isLoading } = useFormSubmissionQuery();
const { convertFormSubmissionsToPerson, deleteFormSubmissions } = usePerson();
const confirm = useConfirm();
const toastStore = useToastStore();

// Selected submissions tracking
const selectedSubmissions = ref<FormSubmissionResponse[]>([]);
const selectedSubmissionIds = computed<number[]>(() =>
  selectedSubmissions.value.map((submission) => submission.id),
);

// Function to reset pagination
const resetPagination = () => {
  page.value = 1;
};

const { filters, currentFilterPayload } = useFilterQuery((appliedFilters) => {
  // Reset pagination when filters change
  resetPagination();
  loadSubmissions();
}, formSubmissionFilterConfigs);

const handlePageChange = (event: { first: number; rows: number }) => {
  page.value = Math.floor(event.first / event.rows) + 1;
  rowsPerPage.value = event.rows;
  loadSubmissions(false);
};

const loadSubmissions = (getCount: boolean = true) => {
  fetchSubmissions(
    {
      offset: (page.value - 1) * rowsPerPage.value,
      limit: rowsPerPage.value,
      ...currentFilterPayload.value,
      filters: [...formSubmissionBaseFilters, ...(currentFilterPayload.value.filters || [])],
    },
    getCount,
  );
};

// Bulk actions for selected submissions
const processSelectedSubmissions = () => {
  confirm.require({
    message: "Chuyển đổi thành khách hàng?",
    header: "Xác nhận chuyển đổi",
    icon: "pi pi-question-circle",
    acceptClass: "p-button-primary",
    accept: async () => {
      try {
        const result = await convertFormSubmissionsToPerson(selectedSubmissionIds.value);

        if (result?.success_count) {
          toastStore.success({
            title: "Thành công",
            message: `Đã chuyển đổi thành công ${result.success_count} form submission`,
          });
        }

        if (result?.fail_count) {
          toastStore.warning({
            title: "Cảnh báo",
            message: `Có ${result.fail_count} form submission không thể chuyển đổi`,
          });
          // Log errors for debugging
          console.error("Conversion errors:", result.errors);
        }

        // Reset selection and reload data
        selectedSubmissions.value = [];
        loadSubmissions();
      } catch (error) {
        toastStore.error({
          title: "Lỗi",
          message: "Đã xảy ra lỗi khi chuyển đổi form submission",
        });
        console.error("Error converting submissions:", error);
      }
    },
  });
};

const deleteSelectedSubmissions = () => {
  confirm.require({
    message: "Xóa form submission?",
    header: "Xác nhận xóa",
    icon: "pi pi-exclamation-triangle",
    acceptClass: "p-button-danger",
    accept: async () => {
      try {
        const result = await deleteFormSubmissions(selectedSubmissionIds.value);

        if (result?.success) {
          toastStore.success({
            title: "Thành công",
            message: `Đã xóa ${selectedSubmissionIds.value.length} form submission`,
          });
          // Reset selection after deletion
          selectedSubmissions.value = [];
          // Reload data
          loadSubmissions();
        }
      } catch (error) {
        toastStore.error({
          title: "Lỗi",
          message: "Đã xảy ra lỗi khi xóa form submission",
        });
        console.error("Error deleting submissions:", error);
      }
    },
  });
};

onMounted(() => {
  loadSubmissions();
});
</script>

<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <div class="flex space-x-3 divide-x divide-solid" role="toolbar" aria-label="Left Toolbar">
        <div class="flex items-center gap-2">
          <span class="text-base font-medium">Form Submissions ({{ total ?? 0 }})</span>
        </div>
      </div>
    </template>
    <template #right-toolbar>
      <div class="flex w-full items-center"></div>

      <div class="flex flex-col-reverse items-center sm:flex-row md:ml-3">
        <div class="relative mr-3 mt-3 w-full sm:mt-0 sm:w-auto"></div>
        <PopSetting title="Tuỳ chỉnh" setting-key="dashboard" />
      </div>
    </template>
    <template #footer>
      <DataTableFooter
        :totalRecords="total || 0"
        :page="page"
        :rows="rowsPerPage"
        :rowsPerPageOptions="rowsPerPageOptions"
        @pageChange="handlePageChange"
      >
        <template #bulkActions>
          <div v-if="selectedSubmissionIds.length" class="flex h-[40px] items-center gap-2">
            <Button
              severity="secondary"
              variant="outlined"
              outlined
              size="small"
              class="h-7 rounded-full px-3 text-sm"
              @click="selectedSubmissions = []"
            >
              <i class="pi pi-times mr-1 text-xs"></i>
              Đã chọn: {{ selectedSubmissionIds.length }}
            </Button>

            <Button
              icon="pi pi-user-plus"
              variant="outlined"
              rounded
              size="small"
              class="h-7 w-7 p-0"
              @click="processSelectedSubmissions"
              v-tooltip.top="'Chuyển đổi'"
            />
            <Button
              icon="pi pi-trash"
              severity="danger"
              rounded
              size="small"
              class="h-7 w-7 p-0"
              @click="deleteSelectedSubmissions"
              v-tooltip.top="'Xóa'"
            />
          </div>
          <div v-else class="flex h-[40px] items-center"></div>
        </template>
      </DataTableFooter>
    </template>

    <DataTable
      title="Form Submissions"
      :columns="formSubmissionColumns"
      :data="submissions"
      :loading="isLoading"
      :total-records="total"
      :paginator="false"
      v-model:selected-items="selectedSubmissions"
      v-model:filters="filters"
      size="small"
      checkbox
      selection-mode="multiple"
      :show-header="false"
      :hide-header-row="true"
    >
      <template #person_id="{ data }">
        <PersonCard v-if="data.person_id" :person="data.person" show-gender show-code />
        <PersonCard v-else :person="{ ...data, id: data.id }" show-gender show-code />
      </template>

      <template #processed_at="{ data }">
        <DateTime :time="data.processed_at" show-time />
      </template>

      <template #created_at="{ data }">
        <DateTime :time="data.created_at" show-time />
      </template>

      <template #referrer_url="{ data }">
        <a
          v-if="data.referrer_url"
          :href="data.referrer_url"
          target="_blank"
          @click.stop
          class="block max-w-xs truncate text-blue-500 hover:text-blue-700 hover:underline"
          :title="data.referrer_url"
        >
          {{ data.referrer_url }}
        </a>
        <span v-else>-</span>
      </template>

      <template #source_url="{ data }">
        <a
          v-if="data.source_url"
          :href="data.source_url"
          target="_blank"
          @click.stop
          class="block max-w-xs truncate text-blue-500 hover:text-blue-700 hover:underline"
          :title="data.source_url"
        >
          {{ data.source_url }}
        </a>
        <span v-else>-</span>
      </template>

      <template #email="{ data }">
        <span>{{ data.email || "-" }}</span>
      </template>

      <template #form_name="{ data }">
        <span>{{ data.form_name || "-" }}</span>
      </template>
    </DataTable>
  </ContentWithFixedToolbar>

  <ConfirmPopup />
</template>
