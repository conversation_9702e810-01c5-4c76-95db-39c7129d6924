<script setup lang="ts">
import { onMounted, ref, defineAsyncComponent } from "vue";

import { TaskResponse } from "@/api/bcare-types-v2";
import { useTaskStore } from "@/stores/task-store";

import ActivityHeader from "./ActivityHeader.vue";
import TaskItem from "./TaskItem.vue";
import Empty from "@/base-components/Empty";
import TaskTemplateSelector from "@/components/Task/TaskTemplateSelector.vue";

const taskStore = useTaskStore();
const props = defineProps<{
  personId?: number;
}>();

const TaskCreateForm = defineAsyncComponent(
  () => import("@/pages/task/components/TaskCreateForm.vue"),
);
const TaskUpdateForm = defineAsyncComponent(
  () => import("@/pages/task/components/TaskUpdateForm.vue"),
);

// Add ref for TaskCreateForm and TaskTemplateSelector
const taskCreateForm = ref<InstanceType<typeof TaskCreateForm>>();
const taskTemplateSelector = ref<InstanceType<typeof TaskTemplateSelector>>();
// Change from selectedTask to selectedTaskId to match Task.vue pattern
const selectedTaskId = ref<number | null>(null);

// Add handler function
const handleShowTask = () => {
  taskCreateForm.value?.open();
};

const handleDoneTask = async (id: number) => {
  // First update the task status
  await taskStore.updateTask({ id, state: "completed", modified: ["state"] });

  // Then check if it's the last task and show template selector if needed
  const task = taskStore.tasks.find(t => t.id === id);
  if (task && taskTemplateSelector.value) {
    await taskTemplateSelector.value.checkAndShowTemplateSelector(id, task.serial);
  }

  // Reload data regardless
  handleReloadData();
};

const handleDeleteTask = async (id: number) => {
  await taskStore.deleteTasks({ id_list: [id] });
  handleReloadData();
};

// Update handler to use taskId instead of task object
const handleEditTask = (task: TaskResponse) => {
  selectedTaskId.value = task.id;
};

// Update close handler to reset selectedTaskId
const handleCloseForm = () => {
  selectedTaskId.value = null;
};

// Handle task completion and show template selector
const handleTaskCompleted = async (data: { taskId: number; serial?: number }) => {
  if (taskTemplateSelector.value) {
    // Ensure person_id is set (should already be set from props, but double-check)
    if (props.personId && taskTemplateSelector.value.setPersonId) {
      await taskTemplateSelector.value.setPersonId(props.personId);
    }
    await taskTemplateSelector.value.checkAndShowTemplateSelector(data.taskId, data.serial);
  }
};

// Add handler for reloading data
const handleReloadData = async () => {
  await taskStore.fetchQueryTasks(
    {
      table: "task_serial_view",
      filters: [
        {
          field: "task_serial_view.person_id",
          operator: "EQ",
          value: props.personId?.toString(),
        },
      ],
      sort: [
        {
          field: "task_serial_view.start_date",
          order: "DESC",
        },
        {
          field: "task_serial_view.serial",
          order: "DESC",
        },
      ],
      limit: 30,
      offset: 0,
    },
    true,
  );
};

onMounted(async () => {
  await handleReloadData();
});
</script>

<template>
  <div class="mt-4">
    <ActivityHeader title="" button-label="Thêm công việc" @button-click="handleShowTask" />
    <div v-if="taskStore.tasks.length > 0">
      <TaskItem
        v-for="task in taskStore.tasks"
        :key="task.id"
        :task="task"
        @update-status="handleDoneTask(task.id)"
        @delete="handleDeleteTask(task.id)"
        @edit="handleEditTask(task)"
      />
    </div>
    <div v-else><Empty /></div>
  </div>

  <TaskCreateForm
    ref="taskCreateForm"
    @reload-data="handleReloadData"
    :person-id="props.personId"
  />

  <!-- Updated to use task-id prop instead of task-data-prop -->
  <TaskUpdateForm
    v-if="selectedTaskId"
    :task-id="selectedTaskId"
    :key="selectedTaskId"
    @reload-data="handleReloadData"
    @close="handleCloseForm"
    @task-completed="handleTaskCompleted"
  />

  <!-- Task Template Selector -->
  <TaskTemplateSelector
    ref="taskTemplateSelector"
    :person-id="props.personId"
    @task-created="handleReloadData"
    @template-selected="(templateId) => console.log('Template selected:', templateId)"
    @cancelled="() => console.log('Template selection cancelled')"
  />
</template>
