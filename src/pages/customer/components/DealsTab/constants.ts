import { Icon } from "@/base-components/Lucide/Lucide.vue";

export const DEAL_FILTER_PRESETS = {
  ALL: "all",
  MY_DEALS: "my_deals",
  RELATED_DEALS: "related_deals",
  CREATED_TODAY: "created_today",
  WON_TODAY: "won_today",
  LOST_TODAY: "lost_today",
  ACTIVE_DEALS: "active_deals",
  // Stage-specific presets
  STAGE_DEN_PHONG_KHAM: "stage_den_phong_kham",
  STAGE_DA_TV: "stage_da_tv",
  STAGE_CHUA_COC: "stage_chua_coc",
  STAGE_DA_COC: "stage_da_coc",
  STAGE_GAP_BS: "stage_gap_bs",
  STAGE_TONG_QUAT: "stage_tong_quat",
} as const;

export type DealFilterPreset = (typeof DEAL_FILTER_PRESETS)[keyof typeof DEAL_FILTER_PRESETS];

// Filter definitions
export const DEAL_FILTERS = [
  { type: DEAL_FILTER_PRESETS.ALL, icon: "ListOrdered", name: "Tất cả" },
  { type: DEAL_FILTER_PRESETS.MY_DEALS, icon: "User", name: "Deal của tôi" },
  { type: DEAL_FILTER_PRESETS.RELATED_DEALS, icon: "Users", name: "Deal liên quan" },
  { type: DEAL_FILTER_PRESETS.CREATED_TODAY, icon: "PlusCircle", name: "Tạo hôm nay" },
  { type: DEAL_FILTER_PRESETS.WON_TODAY, icon: "CheckCircle", name: "Thắng hôm nay" },
  { type: DEAL_FILTER_PRESETS.LOST_TODAY, icon: "XCircle", name: "Thua hôm nay" },
  { type: DEAL_FILTER_PRESETS.ACTIVE_DEALS, icon: "Activity", name: "Deal đang hoạt động" },
] as const;
