<script lang="ts" setup>
import { InputNumberInputEvent } from "primevue/inputnumber";
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import {
  AttachmentResponse,
  Discount,
  InstallmentPlanAddRequest,
  InstallmentPlanUpdateRequest,
} from "@/api/bcare-types-v2";
import DiscountTag from "@/base-components/DiscountTag.vue";
import Money from "@/base-components/Money.vue";
import AttachmentTreePro from "@/components/Attachment/AttachmentTreePro.vue";
import SearchAttachment from "@/components/Attachment/SearchAttachment.vue";
import SearchDiscount from "@/components/Discount/SearchDiscount.vue";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import useBill from "@/hooks/useBill";
import useDeal from "@/hooks/useDeal";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";
import { selectAllText } from "@/utils/string";
import UserAssignPromax from "@/components/User/UserAssignPromax.vue";
import EntityTagManager from "@/components/Tags/EntityTagManager.vue";

import { useDealFormStore } from "@/stores/deal-form-store";

// Define props
const props = defineProps<{
  personId: number;
}>();

// Define emits
const emit = defineEmits([
  "update:selectedProduct",
  "update:selectedAttachments",
  "update:selectedAttachment",
  "save",
]);

//
const { addBillFromDeal } = useBill({ useStore: false });
const { updateDeal } = useDeal({ useStore: false });

// Initialize the store
const dealFormStore = useDealFormStore();

// No need for tag relations anymore with EntityTagManager

// Initialize the payment plan hook
const paymentPlan = useInstallmentPlan();

// Refs for popovers
const sectionFocused = ref();
const attachmentPopRef = ref();
const productPopRef = ref();
const discountPopRef = ref();

// Add this near the top of your script
const confirmDialog = useConfirm();

// Computed for deal name
const dealName = computed({
  get: () => dealFormStore.currentDeal?.name || "",
  set: (value: string) => {
    if (dealFormStore.currentDeal) {
      dealFormStore.currentDeal.name = value;
    }
  },
});

// Watchers to emit updates
watch(
  () => dealFormStore.pendingAttachments,
  (newAttachments) => {
    emit("update:selectedAttachments", Object.values(newAttachments));
  },
  { deep: true },
);

// Toggle attachment search
const toggleAttachmentSearch = async (event: MouseEvent, attachment: any | null) => {
  dealFormStore.toggleAttachmentSearch(attachment);
  attachmentPopRef.value?.popoverRef.toggle(event);
};

// Toggle product search
const toggleProductSearch = (event: MouseEvent, attachment: any | null) => {
  dealFormStore.toggleProductSearch(attachment);
  productPopRef.value?.popoverRef.toggle(event);
};

// Add ref for controlling discount input visibility
const isDiscountMode = ref(false);

// Update computed
const showDiscountInputs = computed(
  () => isDiscountMode.value && paymentMethod.value.val === "full",
  // () => true,
);

// Update toggle function
const toggleDiscountSearch = (event: MouseEvent) => {
  if (paymentMethod.value.val === "full") {
    isDiscountMode.value = !isDiscountMode.value;
    if (!isDiscountMode.value) {
      dealFormStore.clearAttachmentDiscounts();
    }
  } else {
    discountPopRef.value?.popoverRef.toggle(event);
  }
};

// Handle parent click
const handleParentClick = (event: MouseEvent, attachment: any) => {
  // Optional: Implement if needed
};

// Select parent attachment
const selectParentAttachment = async (selectedParent: any) => {
  await dealFormStore.selectParentAttachment(selectedParent);
  emit("update:selectedAttachment", selectedParent);
};

// Select product
const selectProduct = async () => {
  await dealFormStore.selectProduct();
  emit("update:selectedProduct", dealFormStore.selectedProduct);
};

// Select discount
const selectDiscounts = (discounts: Discount[]) => {
  if (discounts) {
    dealFormStore.selectDiscounts(discounts);
  }
};

// Remove attachment
const removeAttachment = async (id: number) => {
  await dealFormStore.removeAttachment(id);
};

// Define menu items
const menuItems = [
  {
    label: "Lưu và tạo hóa đơn",
    disabled: dealFormStore.isEditMode || !dealFormStore.currentDeal,
    icon: "pi pi-money-bill",
    command: async () => {
      try {
        const dealId = await saveAll(AttachmentStatus.UNPAID, false);
        if (dealId) {
          await addBillFromDeal({ deal_id: dealId });
          await updateDeal({
            id: dealId,
            state: "won",
          });
          emit("save");
        }
      } catch (error) {
        console.error("Error in save and create bill flow:", error);
        // TODO: Show error notification to user
      }
    },
  },
  {
    label: "Clear",
    command: () => {
      dealFormStore.clearAll();
    },
  },
  {
    label: "Delete",
    command: () => {
      confirmDialog.require({
        message: "Bạn có muốn xóa deal này?",
        icon: "pi pi-trash",
        rejectProps: {
          label: "Hủy",
          severity: "secondary",
          outlined: true,
        },
        acceptProps: {
          label: "Xóa",
          severity: "danger",
        },
        accept: () => {
          saveAll(AttachmentStatus.DELETED);
        },
        reject: () => {},
      });
    },
  },
];

// Handle quantity updates from AttachmentTreePro
const updateAttachmentQuantity = async (
  attachmentId: number,
  newQuantity: number,
  parentId: number,
) => {
  await dealFormStore.updateQuantity(attachmentId, newQuantity, parentId);
};

// Compute selected product IDs for the SearchDiscount component
const selectedProductIds = computed(() => {
  return Object.values(dealFormStore.pendingAttachments)
    .filter((attachment) => attachment.self.product_id)
    .map((attachment) => attachment.self.product_id!);
});

// Installment
const paymentMethod = ref({ name: "Trả nhiều lần", val: "installment" });
const paymentMethods = [
  { name: "Trả nhiều lần", val: "installment" },
  { name: "Trả một lần", val: "full" },
];

const downPaymentAmount = ref(1000000);
const remainingAmount = computed(() => dealFormStore.totalValue - downPaymentAmount.value);
const installmentCount = ref(10);
const installmentAmount = computed(() =>
  Math.round(remainingAmount.value / installmentCount.value),
);

// Function to save payment plan (create or update)
const savePaymentPlan = async (): Promise<boolean> => {
  if (!dealFormStore.hasPendingAttachments) {
    return true;
  }

  if (!dealFormStore.currentDeal || dealFormStore.currentDeal.status == CommonStatus.DELETED)
    return true;

  const existingPlan = paymentPlan.installmentPlans.value.find(
    (plan) => plan.deal_id === dealFormStore.currentDeal?.id,
  );

  const planRequest: InstallmentPlanAddRequest | InstallmentPlanUpdateRequest = {
    person_id: props.personId,
    deal_id: dealFormStore.currentDeal.id,
    name: `${dealFormStore.currentDeal.name}`,
    total_amount: dealFormStore.totalValue,
    down_payment: downPaymentAmount.value,
    total_installments: paymentMethod.value.val === "full" ? 0 : installmentCount.value,
  };

  try {
    let plan;
    if (existingPlan) {
      const updateRequest: InstallmentPlanUpdateRequest = {
        id: existingPlan.id,
        ...planRequest,
      };
      plan = await paymentPlan.updateInstallmentPlan(updateRequest);
      console.log("Payment plan updated:", plan);
    } else {
      plan = await paymentPlan.addInstallmentPlan(planRequest as InstallmentPlanAddRequest);
      console.log("Payment plan created:", plan);
    }
    return !!plan;
  } catch (error) {
    console.error("Error saving payment plan:", error);
    return false;
  }
};

// Save all attachments with a specific status and create/update payment plan
const saveAll = async (
  status: AttachmentStatus,
  emitSave: boolean = true,
): Promise<number | false> => {
  try {
    const [dealId, paymentPlanResult] = await Promise.all([
      dealFormStore.saveAll(status, paymentMethod.value.val),
      savePaymentPlan(),
    ]);

    if (emitSave) {
      emit("save");
    }
    if (dealId) {
      return dealId; // Return deal id from store
    }
    return false;
  } catch (error) {
    console.error("Error executing saveAll and savePaymentPlan:", error);
    return false;
  }
};

// Handle child attachments change
const handleChildAttachmentsChanged = (
  parent: AttachmentResponse,
  childAttachments: AttachmentResponse[],
) => {
  dealFormStore.updateAllChildAttachments(parent.id, childAttachments);
};

// Load existing installment plan
const loadExistingInstallmentPlan = async () => {
  if (dealFormStore.isEditMode && dealFormStore.currentDeal) {
    await paymentPlan.listInstallmentPlans({
      filter: { deal_id: dealFormStore.currentDeal.id },
    });
    const existingPlan = paymentPlan.installmentPlans.value.find(
      (plan) => plan.deal_id === dealFormStore.currentDeal!.id,
    );
    if (existingPlan) {
      downPaymentAmount.value = existingPlan.down_payment;
      installmentCount.value = existingPlan.total_installments;
      paymentMethod.value = { name: "Trả nhiều lần", val: "installment" };
    } else {
      paymentMethod.value = { name: "Trả một lần", val: "full" };
    }
  }
};

// Reset form function
const resetForm = () => {
  dealName.value = "";
  downPaymentAmount.value = 1000000;
  installmentCount.value = 10;
  paymentMethod.value = { name: "Trả nhiều lần", val: "installment" };
};

// Expose resetForm and loadExistingInstallmentPlan to parent component
defineExpose({
  resetForm,
  loadExistingInstallmentPlan,
});

onMounted(async () => {
  await loadExistingInstallmentPlan();
});

onUnmounted(() => {
  dealFormStore.currentDeal = null;
});

// Simplified watch function
watch(
  () => paymentMethod.value.val,
  (newVal, oldVal) => {
    if (newVal === "full" && oldVal === "installment") {
      // Switching from installment to full
      dealFormStore.clearDiscounts();
    } else if (newVal === "installment" && oldVal === "full") {
      // Switching from full to installment
      isDiscountMode.value = false;
      dealFormStore.clearAttachmentDiscounts();
      // Trigger recalculation để cập nhật UI
      dealFormStore.recalculateTrigger++;
    }
  },
);

// Handle discount updates from AttachmentTreePro
const handleAttachmentDiscount = (attachmentId: number, amount: number, parentId?: number) => {
  dealFormStore.updateDiscount(attachmentId, amount, parentId);
};

const onParticipantChange = async (role: string, value: number | number[] | undefined) => {
  // Handle undefined or array values safely
  const userId = value === undefined ? 0 : Array.isArray(value) ? value[0] : value;
  dealFormStore.participants[role] = userId;
  // Gọi API qua store
  await dealFormStore.saveParticipant(role, userId);
};

// Handle tags updated from EntityTagManager
const handleTagsUpdated = (updatedTags: any[]) => {
  console.log("🏷️ Deal tags updated successfully:", updatedTags);
  // The EntityTagManager handles optimistic updates internally
  // We could optionally update the deal store here if needed
};
</script>

<template>
  <ConfirmPopup group="deal-form" />
  <div class="grid grid-cols-12 gap-2 py-2">
    <!-- Deal Name Input -->
    <div class="col-span-6">
      <InputText
        v-model="dealName"
        fluid
        placeholder="Tên deal"
        type="text"
        @focus="selectAllText"
      />
    </div>
    <div class="col-span-6 flex flex-wrap items-center gap-3">
      <PipelineStageSelect
        v-if="dealFormStore.currentDeal"
        v-model="dealFormStore.currentDeal.stage_id"
        :pipeline-id="2"
        label-class="text-base"
      />
      <EntityTagManager
        v-if="dealFormStore.currentDeal"
        :key="`deal-tags-${dealFormStore.currentDeal.id}`"
        :entity-id="dealFormStore.currentDeal.id"
        entity-type="deal"
        :initial-tags="dealFormStore.currentDeal.tags"
        @tags-updated="handleTagsUpdated"
      />
    </div>

    <!-- Primary Attachments Section -->
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': sectionFocused === 'primary',
          'hover:border-gray-300': sectionFocused !== 'primary',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="sectionFocused = 'primary'"
      >
        <div v-if="dealFormStore.hasPendingAttachments" class="mb-2 space-y-1">
          <!-- Render attachments with parents -->
          <AttachmentTreePro
            v-for="(record, id) in dealFormStore.pendingAttachments"
            :key="id"
            :attachment="record.self"
            :initial-child-attachments="record.children"
            :search-product-filter="{ collection: 'secondary' }"
            mode="product"
            @on-attachment-click="handleParentClick($event, record)"
            @remove-attachment="removeAttachment(record.self.id)"
            @update-child-attachments="handleChildAttachmentsChanged(record.self, $event)"
            @update-quantity="updateAttachmentQuantity"
            @update:discount="handleAttachmentDiscount"
            :show-discount-input="showDiscountInputs"
          />
        </div>
        <!-- Selected Discounts Section -->
        <div v-if="dealFormStore.selectedDiscounts?.length" class="mb-2 space-y-1.5">
          <div
            v-for="discount in dealFormStore.selectedDiscounts"
            :key="discount.id"
            class="flex items-center justify-between"
          >
            <DiscountTag
              :discount="discount"
              @remove-discount="dealFormStore.selectDiscount(discount)"
            />
            <div class="pr-[108px] text-right text-success">
              -
              <Money
                :amount="dealFormStore.calculatedDiscount?.discount_amounts[discount.id] ?? 0"
              />
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex gap-2">
            <Button
              aria-label="Add bought service"
              class="size-8 text-xs"
              icon="pi pi-search-plus"
              outlined
              severity="success"
              size="small"
              @click="toggleAttachmentSearch($event, null)"
            />
            <Button
              aria-label="Add product"
              class="size-8 text-xs"
              icon="pi pi-cart-plus"
              outlined
              severity="warn"
              size="small"
              @click="toggleProductSearch($event, null)"
            />
            <div v-if="!dealFormStore.hasPendingAttachments" class="content-center text-muted">
              Dịch vụ chính
            </div>
          </div>
          <div class="flex items-center gap-2">
            <Money :amount="dealFormStore.totalValue" class="font-semibold" />
            <template
              v-if="paymentMethod.val === 'full' && dealFormStore.totalAttachmentDiscounts > 0"
            >
              <Money
                :amount="-dealFormStore.totalAttachmentDiscounts"
                variant="info"
                class="font-semibold"
              />
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Plan Section -->
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': sectionFocused === 'primary',
          'hover:border-gray-300': sectionFocused !== 'primary',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="sectionFocused = 'primary'"
      >
        <div v-if="dealFormStore.hasPendingAttachments" class="mb-2 space-y-2">
          <div class="flex items-center gap-2">
            <span>Kế hoạch thanh toán</span>
            <SelectButton
              v-model="paymentMethod"
              :allowEmpty="false"
              :options="paymentMethods"
              class="flex-grow text-xs"
              dataKey="val"
              :disabled="dealFormStore.isEditMode"
              optionLabel="name"
            />
          </div>

          <!-- Show only for installment -->
          <template v-if="paymentMethod.val === 'installment'">
            <div class="flex items-center gap-2">
              <span>Trả trước</span>
              <InputNumber
                :model-value="downPaymentAmount"
                class="w-28"
                currency="VND"
                locale="vi-VN"
                mode="currency"
                pt:pcInputText:root:class="text-sm w-full text-warning font-semibold rounded-none border-0 border-b p-0 shadow-none focus:ring-0 text-center"
                @input="
                  (e: InputNumberInputEvent) => {
                    downPaymentAmount = typeof e.value === 'number' ? e.value : 0;
                  }
                "
              />
            </div>
            <div class="flex flex-wrap items-center gap-2">
              <span>Còn lại</span>
              <Money :amount="remainingAmount" class="font-semibold" />
              <span>sẽ trả sau</span>
              <InputNumber
                v-model="installmentCount"
                :min="1"
                class="w-10 ring-0"
                pt:pcInputText:root:class="text-sm w-full text-warning font-semibold rounded-none border-0 border-b p-0 shadow-none focus:ring-0 text-center"
                @input="
                  (e: InputNumberInputEvent) => {
                    installmentCount = typeof e.value === 'number' ? e.value : 0;
                  }
                "
              />
              <span>lần, mỗi lần</span>
              <Money :amount="installmentAmount" class="font-semibold" />
            </div>
          </template>

          <!-- Show for full payment -->
          <template v-else>
            <div class="flex items-center gap-2">
              <span>Thanh toán</span>
              <Money :amount="dealFormStore.finalValue" class="font-semibold" />
            </div>
          </template>
        </div>
        <div class="flex gap-2">
          <Button
            aria-label="Payment plan"
            class="size-8 text-xs"
            icon="pi pi-dollar"
            outlined
            severity="success"
            size="small"
          />
          <Button
            v-if="dealFormStore.hasPendingAttachments"
            aria-label="Add discount"
            class="size-8 text-xs"
            icon="pi pi-tags"
            outlined
            severity="info"
            size="small"
            @click="toggleDiscountSearch($event)"
          />
          <div v-if="!dealFormStore.hasPendingAttachments" class="content-center text-muted">
            Kế hoạch thanh toán
          </div>
        </div>
      </div>
    </div>

    <div class="col-span-12 flex flex-wrap items-center justify-between gap-2">
      <div class="flex flex-grow flex-wrap gap-2">
        <!-- Treatment Doctor -->
        <div
          class="flex min-w-[120px] flex-grow basis-40 items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            :model-value="dealFormStore.participants.treatment_doctor"
            :global-unique-name="'treatment_doctor'"
            class="w-full"
            placeholder="BS điều trị"
            @update:model-value="onParticipantChange('treatment_doctor', $event)"
          />
        </div>

        <!-- Consultant Doctor -->
        <div
          class="flex min-w-[120px] flex-grow basis-40 items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            :model-value="dealFormStore.participants.consultant_doctor"
            :global-unique-name="'consultant_doctor'"
            class="w-full"
            placeholder="BS tư vấn"
            @update:model-value="onParticipantChange('consultant_doctor', $event)"
          />
        </div>

        <!-- Doctor Assistant -->
        <div
          class="flex min-w-[120px] flex-grow basis-40 items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            :model-value="dealFormStore.participants.doctor_assistant"
            :global-unique-name="'doctor_assistant'"
            class="w-full"
            placeholder="Trợ lý"
            @update:model-value="onParticipantChange('doctor_assistant', $event)"
          />
        </div>

        <!-- CSKH -->
        <!-- <div
          class="flex min-w-[120px] flex-grow basis-40 items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            v-model="dealFormStore.participants.consultant"
            :global-unique-name="'consultant'"
            class="w-full"
            placeholder="Trợ lý"
            @update:model-value="onParticipantChange('consultant', $event)"
          />
        </div> -->

        <!-- Sale -->
        <div
          class="flex min-w-[120px] flex-grow basis-40 items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            :model-value="dealFormStore.participants.sale"
            :global-unique-name="'sale'"
            class="w-full"
            placeholder="Telesales"
            @update:model-value="onParticipantChange('sale', $event)"
          />
        </div>
      </div>

      <!-- Save Button -->
      <div class="flex-shrink-0">
        <SplitButton
          :model="menuItems"
          label="Save"
          icon="pi pi-save"
          severity="primary"
          size="small"
          @click="saveAll(AttachmentStatus.UNPAID)"
        />
      </div>
    </div>

    <!-- Popovers and Selects -->
    <SearchAttachment
      ref="attachmentPopRef"
      v-model:selectedAttachment="dealFormStore.selectedParentAttachment"
      :default-filters="{ kind: 'product', product_type: 'service' }"
      :filter-product-type="true"
      :multiple="false"
      :person-id="props.personId"
      search-mode="alone_attachment"
      @update:selectedAttachment="selectParentAttachment"
    />
    <SearchProduct
      ref="productPopRef"
      v-model:selectedProduct="dealFormStore.selectedProduct"
      :defaultFilters="{ type: 'service', collection: 'primary' }"
      :multiple="false"
      @update:selectedProduct="selectProduct"
    />
    <SearchDiscount
      ref="discountPopRef"
      :deal-id="dealFormStore.currentDeal?.id"
      :multiple="true"
      :person-id="props.personId"
      :product-ids="selectedProductIds"
      :selected-discounts="dealFormStore.selectedDiscounts"
      @update:selectedDiscounts="selectDiscounts"
    />
  </div>
</template>
