<template>
  <div>
    <Button @click="visible = true" class="h-9" icon="pi pi-upload" label="Thêm hình ảnh" />

    <Dialog
      v-model:visible="visible"
      modal
      header="Thêm hình ảnh"
      dismissable-mask
      class="min-w-1/4 max-w-1/2"
    >
      <Dropzone ref-key="dropzoneRef" :options="dropzoneOptions" class="drop-zone">
        <div class="text-sm text-gray-600">Kéo/Thả hình ảnh vào đây hoặc nhấn vào để chọn</div>
      </Dropzone>

      <div class="mt-4">
        <label class="mb-2 block text-sm font-medium text-gray-700">Chọn ngày khám</label>
        <Select
          v-model="selectedTrack"
          :options="trackOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Chọn ngày khám"
          class="w-full"
        />
      </div>

      <div class="flex justify-end border-t border-gray-200 pt-5">
        <Button
          variant="outlined"
          severity="danger"
          class="mr-1 w-24"
          @click="visible = false"
          icon="pi pi-times"
          label="Đóng"
        />
        <Button
          class="w-24"
          @click="handleSubmit"
          :loading="isUploading"
          icon="pi pi-upload"
          label="Lưu"
        />
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { DropzoneFile } from "dropzone";
import { computed, onMounted, provide, ref } from "vue";

import { BASE_URL } from "@/api/http";
import Dropzone, { DropzoneElement } from "@/base-components/Dropzone";
import { useMedia } from "@/hooks/useMedia";
import useTrack from "@/hooks/useTrack";
import { useToastStore } from "@/stores/toast-store";

const props = defineProps<{
  personId: number;
  usageType: string;
}>();

const visible = ref(false);
const dropzoneRef = ref<DropzoneElement>();

const emit = defineEmits(["fetch-media"]);

const toastStore = useToastStore();
const { addFile, addFileUsage } = useMedia();
const { tracks, listTracks } = useTrack();

const dropzoneOptions = {
  url: `${BASE_URL}/v1/file/add`,
  method: "POST",
  thumbnailWidth: 150,
  maxFilesize: 20,
  paramName: "file_upload",
  acceptedFiles: "image/*",
  autoProcessQueue: false,
  addRemoveLinks: true,
  headers: { "My-Awesome-Header": "header value" },
};

const trackOptions = computed(() => [
  ...new Map(
    tracks.value.map((track) => {
      const date = new Date(track.begin).toLocaleDateString("vi-VN");
      return [date, { value: track.id, label: date }];
    }),
  ).values(),
]);

const selectedTrack = ref<number>();

onMounted(async () => {
  await listTracks({
    filter: { person_id: props.personId },
    order_by: "created_at DESC",
  });
  if (trackOptions.value.length > 0) {
    selectedTrack.value = trackOptions.value[0].value;
  }
});

provide(`bind[dropzoneRef]`, (el: DropzoneElement) => {
  if (el) {
    dropzoneRef.value = el;
    el.dropzone.on("addedfile", handleAddedFile);
  }
});

function handleAddedFile(file: DropzoneFile) {
  const maxSizeConfig = (dropzoneRef.value?.dropzone.options.maxFilesize ?? 0) * 1024 * 1024;
  if (file.size > maxSizeConfig) {
    dropzoneRef.value?.dropzone.removeFile(file);
    toastStore.success({
      title: "Kích thước của tệp tin vượt quá giới hạn cho phép.",
      message: "",
    });
  }
}

const isUploading = ref(false);

async function handleSubmit() {
  const dropzone = dropzoneRef.value?.dropzone;
  if (!dropzone) return;

  try {
    isUploading.value = true;
    const files = dropzone.getAcceptedFiles();

    for (const file of files) {
      const res = await addFile(file);
      if (res.code !== 0) throw new Error("Upload failed");

      await addFileUsage({
        entity_id: props.personId || 0,
        entity_type: "person",
        file_id: res.data?.id || 0,
        usage_meta: {},
        usage_type: props.usageType,
        track_id: selectedTrack.value || 0,
      });
    }

    toastStore.success({
      title: "Thêm hình ảnh thành công",
      message: "",
    });
    emit("fetch-media");
    visible.value = false;
  } catch (error) {
    toastStore.error({
      title: "Có lỗi xảy ra khi tải ảnh lên",
      message: "",
    });
  } finally {
    isUploading.value = false;
  }
}
</script>

<style scoped>
.drop-zone :deep(.dz-progress) {
  display: none;
}

.drop-zone :deep(.dz-image img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
