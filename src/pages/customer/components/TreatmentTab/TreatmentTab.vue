<script lang="ts" setup>
import { useLocalStorage } from "@vueuse/core";
import { computed, onMounted, ref } from "vue";

import { AttachmentStatus } from "@/api/bcare-enum";
import { AttachmentResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Empty from "@/base-components/Empty";
import Money from "@/base-components/Money.vue";
import AttachmentItem from "@/components/Attachment/AttachmentItem.vue";
import RootAttachment from "@/components/Attachment/RootAttachment.vue";
import TinyToothChart from "@/components/TinyToothChart.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import { usePermissions } from "@/composables/usePermissions";
import { usePrint } from "@/composables/usePrint";
import useAttachment from "@/hooks/useAttachment";
import useMetadataParser from "@/hooks/useAttachmentDataParser";
import TreatmentForm from "@/pages/customer/components/TreatmentTab/TreatmentForm.vue";
import MaterialUsageReport from "@/components/MaterialUsage/MaterialUsageReport.vue";
import { useMaterialUsageMatrixStore } from "@/stores/material-usage-matrix-store";

const props = defineProps<{
  personId: number;
}>();

const { listAttachments, attachments, deleteAttachment, forceUpdateCreatedAt } = useAttachment();
const { onlyAdmin } = usePermissions();
const {
  teethDataByAttachment,
  nextTeethDataByAttachment,
  metaDataByAttachment,
  medicationsByAttachment,
  processAttachments,
  getSafeParticipants,
  getTotalPrice,
} = useMetadataParser();

const { printMedication } = usePrint();

const visibleTeethCharts = ref<Record<string, boolean>>({});
const expandedSectionLoading = ref<Record<string, boolean>>({});
const showProducts = useLocalStorage("treatment-tab-show-products", false);
const showOperations = useLocalStorage("treatment-tab-show-operations", true);

const selectedDate = ref<string | null>(null);
const selectedAttachmentId = ref<number | null>(null);

const resetAllMaterialStates = () => {
  const materialUsageMatrixStore = useMaterialUsageMatrixStore();
  materialUsageMatrixStore.resetStore();
};

const op = ref();

const availableDates = computed(() => {
  const dates = attachments.value.map((item) => {
    return new Date(item.created_at).toISOString().split("T")[0];
  });
  return [...new Set(dates)].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
});

const popoverRefs = ref<Record<number, any>>({});
const selectedDates = ref<Record<number, Date | null>>({});

onMounted(async () => {
  await reload();
});

const reload = async () => {
  await listAttachments({ filter: { person_id: props.personId } });
  processAttachments(attachments.value);
};

const handleAddNew = () => {
  // Clean up material states before creating new treatment
  resetAllMaterialStates();
  selectedAttachmentId.value = null;
};

const items = ref([
  {
    label: "Thêm mới",
    icon: "pi pi-plus",
    command: handleAddNew,
  },
]);

const getMenuItems = (item: AttachmentResponse) => {
  // Early return nếu đã ACTIVE và price !== 0
  if (item.status === AttachmentStatus.ACTIVE && item.price !== 0) {
    return undefined;
  }

  return [
    // Edit chỉ cho phép với operation
    ...(item.kind !== "product"
      ? [
          {
            label: "Edit",
            icon: "pi pi-pencil",
            command: () => (selectedAttachmentId.value = item.id),
          },
        ]
      : []),
    {
      label: "Delete",
      icon: "pi pi-trash",
      command: () => handleDelete(item),
    },
  ];
};

const canShowMenu = (item: AttachmentResponse) => {
  return item.status !== AttachmentStatus.ACTIVE || item.price === 0;
};

const handleDelete = async (item: AttachmentResponse) => {
  if (item.status === AttachmentStatus.TEMP || item.status === AttachmentStatus.UNPAID) {
    try {
      await deleteAttachment({ id: item.id });
      await reload();
    } catch (error) {
      console.error("Failed to delete attachment:", error);
    }
  } else {
    console.warn(`Cannot delete attachment with status: ${item.status}`);
  }
};

const filteredAttachments = computed(() => {
  let filtered = attachments.value;

  if (!showProducts.value) {
    filtered = filtered.filter((item) => item.kind !== "product");
  }

  if (selectedDate.value) {
    filtered = filtered.filter(
      (item) => new Date(item.created_at).toISOString().split("T")[0] === selectedDate.value,
    );
  }

  return filtered;
});

const toggleDatePicker = (event: Event, attachmentId: number) => {
  if (!onlyAdmin()) return;
  popoverRefs.value[attachmentId]?.toggle(event);
};

const handleDateSelect = async (attachmentId: number) => {
  if (!onlyAdmin() || !selectedDates.value[attachmentId]) {
    popoverRefs.value[attachmentId]?.hide();
    return;
  }

  try {
    await forceUpdateCreatedAt({
      id: attachmentId,
      created_at: selectedDates.value[attachmentId]?.toISOString(),
    });
    await reload();
  } catch (err) {
    console.error("Error updating date:", err);
  }

  popoverRefs.value[attachmentId]?.hide();
};

const handleExpandSection = async (attachmentId: number) => {
  const isCurrentlyVisible = visibleTeethCharts.value[attachmentId];

  if (!isCurrentlyVisible) {
    expandedSectionLoading.value[attachmentId] = true;
    await new Promise((resolve) => requestAnimationFrame(resolve));
    expandedSectionLoading.value[attachmentId] = false;
  }

  visibleTeethCharts.value[attachmentId] = !isCurrentlyVisible;
};
</script>

<template>
  <div>
    <div class="card px-5">
      <Menubar :model="items" class="p-1">
        <template #item="{ item, props, hasSubmenu, root }">
          <a class="flex items-center" v-bind="props.action">
            <span :class="item.icon" />
            <span>{{ item.label }}</span>
            <Badge
              v-if="item.badge"
              :class="{ 'ml-auto': !root, 'ml-2': root }"
              :value="item.badge"
            />
            <span
              v-if="item.shortcut"
              class="ml-auto rounded border p-1 text-xs border-surface bg-emphasis text-muted-color"
              >{{ item.shortcut }}</span
            >
            <i
              v-if="hasSubmenu"
              :class="[
                'pi pi-angle-down',
                { 'pi-angle-down ml-2': root, 'pi-angle-right ml-auto': !root },
              ]"
            ></i>
          </a>
        </template>
        <template #end>
          <div class="flex items-center gap-2">
            <InputText
              class="w-32 text-sm sm:w-auto"
              placeholder="Search"
              size="small"
              type="text"
            />
            <Button icon="pi pi-print" severity="secondary" text />
          </div>
        </template>
      </Menubar>
    </div>
    <div class="card p-5">
      <TreatmentForm
        :attachment-id="selectedAttachmentId"
        :person-id="personId"
        @save="
          () => {
            reload();
            resetAllMaterialStates();
            selectedAttachmentId = null;
          }
        "
      />
    </div>
    <DataView :rows="10" :value="filteredAttachments" class="p-5" dataKey="id" paginator>
      <template #header>
        <div class="grid grid-cols-24 gap-2 border-b-2 py-2 font-medium">
          <div class="col-span-3">
            <div class="flex min-w-0 items-center gap-1">
              <span class="truncate">{{
                selectedDate ? new Date(selectedDate).toLocaleDateString("vi-VN") : "Ngày tạo"
              }}</span>
              <i
                v-tooltip="'Lọc theo ngày'"
                class="pi pi-calendar flex-shrink-0 cursor-pointer text-slate-500 hover:text-slate-700"
                @click="op.toggle($event)"
              />
              <Popover ref="op">
                <Select
                  v-model="selectedDate"
                  :options="availableDates"
                  :showClear="true"
                  class="w-40 text-xs"
                  optionLabel="name"
                  placeholder="Chọn ngày"
                  size="small"
                >
                  <template #value="slotProps">
                    <template v-if="slotProps.value">
                      {{ new Date(slotProps.value).toLocaleDateString("vi-VN") }}
                    </template>
                    <span v-else>Chọn ngày</span>
                  </template>
                  <template #option="slotProps">
                    {{ new Date(slotProps.option).toLocaleDateString("vi-VN") }}
                  </template>
                </Select>
              </Popover>
            </div>
          </div>
          <div class="col-span-8 flex items-center gap-2">
            Công việc thực hiện
            <i
              v-tooltip="'Hiển thị CVTH'"
              :class="[showOperations ? 'pi-eye' : 'pi-eye-slash']"
              class="pi cursor-pointer text-slate-500 transition-all hover:text-slate-700"
              @click="showOperations = !showOperations"
            ></i>
          </div>
          <div class="col-span-7 flex items-center gap-2">
            Công việc dự kiến
            <i
              v-tooltip="'Hiển thị sản phẩm'"
              :class="[showProducts ? 'pi-eye' : 'pi-eye-slash']"
              class="pi cursor-pointer text-slate-500 transition-all hover:text-slate-700"
              @click="showProducts = !showProducts"
            ></i>
          </div>
          <div class="col-span-6">Phụ trách</div>
        </div>
      </template>
      <template #list="slotProps">
        <div
          v-for="(item, index) in slotProps.items"
          :key="index"
          :class="{ 'opacity-50': item.status == AttachmentStatus.TEMP }"
          class="grid grid-cols-24 items-center gap-2 py-2 odd:bg-white even:bg-slate-50"
        >
          <div class="col-span-3 flex flex-col">
            <span
              class="flex items-center"
              :class="{
                'cursor-pointer hover:text-primary': onlyAdmin(),
                'cursor-not-allowed': !onlyAdmin(),
              }"
              @click="onlyAdmin() && toggleDatePicker($event, item.id)"
              v-tooltip="'Cập nhật ngày tạo'"
            >
              <DateTime :time="item.created_at" />
              <i class="pi pi-calendar ml-1 text-xs"></i>
            </span>

            <Popover :ref="(el) => (popoverRefs[item.id] = el)">
              <DatePicker
                v-model="selectedDates[item.id]"
                inline
                @date-select="handleDateSelect(item.id)"
                :maxDate="new Date()"
              />
            </Popover>

            <span class="text-xs font-medium text-slate-400">
              <i class="pi pi-hashtag pr-1 text-xs" />{{ item.id }}
            </span>
          </div>
          <div class="col-span-8 flex flex-wrap items-center gap-1">
            <AttachmentItem
              v-if="metaDataByAttachment[item.id]?.operation"
              :key="item.id"
              :operations="metaDataByAttachment[item.id].operation"
              :parent="item.parent"
              :show-operations="showOperations"
              :teeth-data="teethDataByAttachment[item.id] || {}"
              hide-default-operations
            />
            <RootAttachment v-else :attachment="item" />
          </div>
          <div class="col-span-7 flex flex-wrap gap-1">
            <template v-if="metaDataByAttachment[item.id] && item.kind !== 'product'">
              <Chip
                v-for="(operationName, operationId) in metaDataByAttachment[item.id]
                  .next_operation || []"
                :key="operationId"
                :label="operationName"
                class="border"
              />
            </template>
            <template v-else>
              <Money :amount="getTotalPrice(item)" :show-icon="false" class="font-semibold" />
            </template>
          </div>

          <div class="col-span-6 flex flex-wrap items-center gap-1 pr-1.5">
            <div class="grow">
              <UserAvatarGroup
                :key="item.id"
                :expand="true"
                :users="[item.user_id, ...getSafeParticipants(item.id)]"
                size="small"
              />
            </div>
            <div class="flex items-center justify-center">
              <Button
                v-if="!canShowMenu(item)"
                icon="pi pi-eye"
                outlined
                severity="secondary"
                size="small"
                @click="handleExpandSection(item.id)"
              />
              <SplitButton
                v-else
                :model="getMenuItems(item)"
                icon="pi pi-eye"
                outlined
                severity="secondary"
                size="small"
                @click="handleExpandSection(item.id)"
              />
            </div>
          </div>

          <template v-if="visibleTeethCharts[item.id]">
            <!-- Loading State for Entire Expanded Section -->
            <div
              v-if="expandedSectionLoading[item.id]"
              class="col-span-24 flex items-center justify-center py-4"
            >
              <div class="flex items-center space-x-2">
                <i class="pi pi-spin pi-spinner text-lg text-blue-500"></i>
                <span class="text-sm text-gray-600">Đang tải...</span>
              </div>
            </div>

            <!-- Original Content -->
            <template v-else>
              <div class="col-span-10">
                <TinyToothChart
                  :model-value="teethDataByAttachment[item.id] || {}"
                  disabled
                  label="Thực hiện"
                />
              </div>

              <div class="col-span-10">
                <TinyToothChart
                  :model-value="nextTeethDataByAttachment[item.id] || {}"
                  disabled
                  label="Dự kiến"
                />
              </div>

              <div class="col-span-4 flex flex-wrap gap-1">
                <template v-if="Object.keys(medicationsByAttachment[item.id] || {}).length">
                  <span
                    v-for="(med, key) in medicationsByAttachment[item.id]"
                    :key="key"
                    v-tooltip="'In toa thuốc'"
                    class="inline-flex h-full cursor-pointer items-center rounded-full border border-[var(--p-button-outlined-help-border-color)] bg-transparent px-2 py-1 text-xs font-medium text-[var(--p-button-outlined-help-color)]"
                    @click="
                      printMedication(med.ten, item.person_id, {
                        attachmentId: item.id,
                      })
                    "
                  >
                    <i class="pi pi-file-plus mr-1 text-xs" />
                    {{ med.tieu_de }}
                  </span>
                </template>
              </div>
            </template>
          </template>

          <!-- Material Usage Section -->
          <div class="col-span-12">
            <template v-if="visibleTeethCharts[item.id] && !expandedSectionLoading[item.id]">
              <MaterialUsageReport :attachment-id="item.id" size="xs" />
            </template>
          </div>
        </div>
      </template>
      <template #empty>
        <Empty class="mx-5 p-10" />
      </template>
    </DataView>
  </div>
</template>
