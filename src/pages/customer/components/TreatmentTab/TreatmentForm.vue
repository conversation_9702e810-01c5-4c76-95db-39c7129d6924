<script lang="ts" setup>
import { computed, onMounted, ref, toRefs, watch, onUnmounted } from "vue";

import { AttachmentStatus } from "@/api/bcare-enum";
import AttachmentTree from "@/components/Attachment/AttachmentTree.vue";
import SearchAttachment from "@/components/Attachment/SearchAttachment.vue";
import OperationSelect from "@/components/Operation/OperationSelect.vue";
import MaterialUsageTable from "@/components/MaterialUsage/MaterialUsageTable.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import UserAssignPromax from "@/components/User/UserAssignPromax.vue";
import SelectMedication from "@/pages/customer/components/TreatmentTab/SelectMedication.vue";
import useMaterialUsage from "@/hooks/useMaterialUsage";
import { useTreatmentFormStore } from "@/stores/treatment-form-store";
import { useMaterialUsageMatrixStore } from "@/stores/material-usage-matrix-store";

const props = defineProps<{
  attachmentId?: number | null;
  personId: number;
}>();

const emit = defineEmits([
  "update:selectedProduct",
  "update:selectedAttachments",
  "update:selectedAttachment",
  "update:selectedMedication",
  "save",
]);

// Khởi tạo store
const attachmentStore = useTreatmentFormStore();

const { attachmentId } = toRefs(props);

// Track current operations for V2 system
const currentOperations = ref<{ id: number; name: string }[]>([]);

// Track operations per attachment to support multi-attachment workflows
const operationsByAttachment = ref<Map<number, { id: number; name: string }[]>>(new Map());

// Material Usage V2 Integration
const materialUsageV2 = useMaterialUsage({ autoLoad: false });

// Material Usage V2 functions
const initializeMaterialFlow = async () => {
  try {
    const isEditMode = !!attachmentId.value;
    
    currentOperations.value = [];
    operationsByAttachment.value.clear();
    
    await materialUsageV2.initializeMaterialUsageV2(isEditMode, attachmentId.value);
  } catch (error) {
    console.error("❌ Failed to initialize Material Usage V2:", error);
  }
};

const handleOperationsChanged = async (data: {
  attachmentId: number;
  operations: { id: number; name: string }[];
}) => {
  // 1. Update operations for this specific attachment
  operationsByAttachment.value.set(data.attachmentId, [...data.operations]);

  // 2. Merge ALL operations from ALL attachments (no duplicates)
  const allOperations = new Map<number, { id: number; name: string }>();

  operationsByAttachment.value.forEach((ops) => {
    ops.forEach(op => {
      if (!allOperations.has(op.id)) {
        allOperations.set(op.id, op);
      }
    });
  });

  currentOperations.value = Array.from(allOperations.values());

  try {
    // 🔧 FIX: Pass ALL operations from ALL attachments, not just from current attachment
    await materialUsageV2.handleOperationsChangedV2({
      attachmentId: data.attachmentId,
      operations: currentOperations.value,  // ✅ ALL operations from ALL attachments
      changedAttachmentId: data.attachmentId,  // Track which attachment triggered the change
      changedOperations: data.operations       // Operations that actually changed
    });
  } catch (error) {
    console.error("❌ Failed to handle operations changed:", error);
  }
};

const saveWithMaterialSubmission = async (
  saveCallback: () => Promise<any>,
  successCallback?: () => void,
) => {
  try {
    const savedAttachments = await saveCallback();
    
    // 🔧 SIMPLE FIX: Submit materials for each attachment separately
    if (savedAttachments?.length > 0) {
      for (const attachment of savedAttachments) {
        await materialUsageV2.submitMaterialUsageV2ForAttachment(attachment.self.id);
      }
    }
    
    if (successCallback) {
      successCallback();
    }
    return true;
  } catch (error) {
    console.error("❌ Failed to save with material submission:", error);
    return false;
  }
};

const resetAllMaterialStates = () => {
  const matrixStore = useMaterialUsageMatrixStore();
  matrixStore.resetStore();
  
  currentOperations.value = [];
  operationsByAttachment.value.clear();
};

// V2 computed properties
const selectedOperationIds = computed(() => {
  return currentOperations.value.map((op) => String(op.id));
});

const isMaterialSubmitting = computed(() => {
  return materialUsageV2.isLoading.value;
});

const materialSubmissionError = computed(() => {
  return materialUsageV2.error.value;
});

// Component-level validation for operations
const canSaveWithValidOperations = computed(() => {
  // Kiểm tra có attachment cơ bản
  const hasAttachments = attachmentStore.canSave;
  
  // Kiểm tra current operations từ component state
  const hasCurrentOperations = currentOperations.value.length > 0;
  
  // Kiểm tra next operations từ store
  const hasNextOperations = attachmentStore.nextOperations.length > 0;
  
  // Cần có attachment VÀ CẢ current operations VÀ next operations
  return hasAttachments && hasCurrentOperations && hasNextOperations;
});

// Refs cho popovers
const attachmentPopRef = ref();
const productPopRef = ref();
const nextOperationPop = ref();
const medicationPopRef = ref();

// Khởi tạo data
const init = async () => {
  // Clear form trước khi init
  attachmentStore.clearAll();
  // Initialize material flow (replaces manual store clearing and material loading)
  await initializeMaterialFlow();
  if (props.attachmentId) {
    await attachmentStore.loadExistingAttachment(props.attachmentId);
  } else {
    attachmentStore.setPersonId(props.personId);
  }
};

// Lifecycle hooks
onMounted(() => {
  init();
});

// Clear material flow state khi component unmounted
onUnmounted(() => {
  resetAllMaterialStates();
});

// Watchers để emit updates
watch(
  () => attachmentStore.pendingAttachments,
  (newAttachments) => {
    emit("update:selectedAttachments", Object.values(newAttachments));
  },
  { deep: true },
);

// Toggle handlers
const toggleAttachmentSearch = async (event: MouseEvent, attachment: any | null) => {
  attachmentStore.toggleAttachmentSearch(attachment);
  attachmentPopRef.value?.popoverRef.toggle(event);
};

const toggleProductSearch = (event: MouseEvent, attachment: any | null) => {
  attachmentStore.toggleProductSearch(attachment);
  productPopRef.value?.popoverRef.toggle(event);
};

const toggleMedicationSelect = (event: MouseEvent) => {
  medicationPopRef.value?.popoverRef.toggle(event);
};

const toggleNextOperationPop = (event: MouseEvent) => {
  attachmentStore.toggleNextOperationPop();
  nextOperationPop.value?.popoverRef.toggle(event);
};

// Action handlers
const selectParentAttachment = async (selectedParent: any) => {
  await attachmentStore.selectParentAttachment(selectedParent);
  emit("update:selectedAttachment", selectedParent);
};

const selectProduct = async () => {
  await attachmentStore.selectProduct();
  emit("update:selectedProduct", attachmentStore.selectedProduct);
};

const handleRemoveOperation = (id: number | string) => {
  attachmentStore.handleRemoveOperation(id);
};

const removeAttachment = async (id: number) => {
  // 1. Remove attachment from store
  await attachmentStore.removeAttachment(id);

  // 2. Remove operations for this attachment from tracking
  operationsByAttachment.value.delete(id);

  // 3. Rebuild current operations from remaining attachments
  const allOperations = new Map<number, { id: number; name: string }>();

  operationsByAttachment.value.forEach((ops) => {
    ops.forEach(op => {
      if (!allOperations.has(op.id)) {
        allOperations.set(op.id, op);
      }
    });
  });

  currentOperations.value = Array.from(allOperations.values());

  // 4. Update material usage system with remaining operations
  try {
    if (operationsByAttachment.value.size > 0) {
      // If there are still attachments, update with remaining operations
      const firstAttachmentId = Array.from(operationsByAttachment.value.keys())[0];
      await materialUsageV2.handleOperationsChangedV2({
        attachmentId: firstAttachmentId,
        operations: currentOperations.value,
        changedAttachmentId: id,  // The removed attachment
        changedOperations: []     // No operations (removed)
      });
    } else {
      // If no attachments left, reset material usage
      resetAllMaterialStates();
    }
  } catch (error) {
    console.error("❌ Failed to update material usage after attachment removal:", error);
  }
};

const saveParticipant = async (role: string, value: number | number[] | undefined) => {
  if (Array.isArray(value)) return;

  await attachmentStore.saveParticipant(role, value || 0);
  emit("update:selectedAttachments", Object.values(attachmentStore.pendingAttachments));
};

// Save handler với xử lý edit/create + material usage submission
const saveAll = async (status: AttachmentStatus) => {
  const success = await saveWithMaterialSubmission(
    () => attachmentStore.saveAll(status),
    () => {
      attachmentStore.clearAll();
      resetAllMaterialStates(); // Clean up material usage states
      emit("save");
    },
  );

  if (!success) {
    // Handle error appropriately - error is already logged in saveWithMaterialSubmission
  }
};

// Menu items
const menuItems = [
  {
    label: "Clear",
    command: () => {
      saveAll(AttachmentStatus.DELETED);
    },
  },
  {
    label: "Delete",
    command: () => {
      saveAll(AttachmentStatus.DELETED);
    },
  },
];

watch(
  attachmentId,
  async (newId) => {
    if (newId) {
      await init();
    } else {
      attachmentStore.clearAll();
      resetAllMaterialStates();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div class="grid grid-cols-12 gap-2 py-2">
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': attachmentStore.operationFocused === 'today',
          'hover:border-gray-300': attachmentStore.operationFocused !== 'today',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="attachmentStore.operationFocused = 'today'"
      >
        <div v-if="attachmentStore.hasPendingAttachments" class="mb-2 space-y-1">
          <!-- Render attachments with parents -->
          <AttachmentTree
            v-for="(record, id) in Object.values(attachmentStore.pendingAttachments).filter(
              (r) => r.parent,
            )"
            :key="id"
            :attachment="record.self"
            :parent="record.parent"
            @remove-attachment="removeAttachment(record.self.id)"
            @operations-changed="handleOperationsChanged"
          />
        </div>
        <div class="flex gap-2">
          <Button
            aria-label="Add bought service"
            class="size-8 text-xs"
            icon="pi pi-search-plus"
            outlined
            severity="success"
            size="small"
            :disabled="attachmentStore.isEditing"
            @click="toggleAttachmentSearch($event, null)"
          />
          <Button
            aria-label="Add product"
            class="size-8 text-xs"
            icon="pi pi-cart-plus"
            outlined
            severity="warn"
            size="small"
            :disabled="attachmentStore.isEditing"
            @click="toggleProductSearch($event, null)"
          />
          <div v-if="!attachmentStore.hasPendingAttachments" class="content-center text-muted">
            Công việc hôm nay
          </div>
        </div>
      </div>
    </div>

    <!-- People Search Sections -->
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': attachmentStore.operationFocused === 'next',
          'hover:border-gray-300': attachmentStore.operationFocused !== 'next',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="attachmentStore.operationFocused = 'next'"
      >
        <div v-if="attachmentStore.nextOperations.length" class="flex flex-wrap gap-1">
          <Chip
            v-for="operation in attachmentStore.nextOperations"
            :key="operation.id"
            :label="operation.name"
            class="cursor-pointer"
            removable
            @click="toggleNextOperationPop"
            @remove.stop="handleRemoveOperation(operation.id)"
          />
        </div>
        <div v-else class="flex gap-2">
          <Button
            aria-label="Add bought service"
            class="size-8 text-xs"
            icon="pi pi-hammer"
            outlined
            severity="info"
            size="small"
            @click="toggleNextOperationPop"
          />
          <div class="content-center text-muted">Công việc dự kiến</div>
        </div>
      </div>
    </div>

    <!-- Operation Material Matrix -->
    <div class="col-span-6" v-if="selectedOperationIds.length > 0">
      <div
        :class="{
          'border-highlight': attachmentStore.operationFocused === 'matrix',
          'hover:border-gray-300': attachmentStore.operationFocused !== 'matrix',
        }"
        class="rounded border-2 border-dashed border-gray-300"
        @click="attachmentStore.operationFocused = 'matrix'"
      >
        <!-- Material Usage V2 -->
        <MaterialUsageTable :readonly="false" :operations="currentOperations" />
      </div>
    </div>

    <!-- Participant Assignment - kế bên MaterialUsageTable khi có operations -->
    <div :class="selectedOperationIds.length > 0 ? 'col-span-6' : 'col-start-1 col-end-9'">
      <div class="space-y-3">
        <!-- Participant Assignment -->
        <div
          :class="
            selectedOperationIds.length > 0
              ? 'grid grid-cols-2 gap-3'
              : 'grid grid-cols-2 gap-3 lg:grid-cols-4'
          "
        >
          <!-- Bác sĩ -->
          <div
            class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
          >
            <UserAssignPromax
              v-model="attachmentStore.participant.bac_si"
              :global-unique-name="'bac_si'"
              class="w-full"
              placeholder="Bác sĩ"
              @update:model-value="saveParticipant('bac_si', $event)"
            />
          </div>

          <!-- Phụ tá -->
          <div
            class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
          >
            <UserAssignPromax
              v-model="attachmentStore.participant.phu_ta"
              :global-unique-name="'phu_ta'"
              class="w-full"
              placeholder="Phụ tá"
              @update:model-value="saveParticipant('phu_ta', $event)"
            />
          </div>

          <!-- Điều phối -->
          <div
            class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
          >
            <UserAssignPromax
              v-model="attachmentStore.participant.dieu_phoi"
              :global-unique-name="'dieu_phoi'"
              class="w-full"
              placeholder="Điều phối"
              @update:model-value="saveParticipant('dieu_phoi', $event)"
            />
          </div>

          <!-- X-Quang -->
          <div
            class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
          >
            <UserAssignPromax
              v-model="attachmentStore.participant.x_quang"
              :global-unique-name="'x_quang'"
              class="w-full"
              placeholder="X-Quang"
              @update:model-value="saveParticipant('x_quang', $event)"
            />
          </div>
        </div>

        <!-- Action Buttons - nằm bên dưới Participant Assignment, sát phải khi có operations -->
        <div v-if="selectedOperationIds.length > 0" class="flex items-center justify-end space-x-2">
          <Button
            v-if="Object.keys(attachmentStore.selectedMedications).length === 0"
            v-tooltip="'Thêm toa thuốc'"
            aria-label="Add medication"
            class="size-9 text-xs"
            icon="pi pi-file-plus"
            outlined
            severity="help"
            @click="toggleMedicationSelect($event)"
          />

          <SelectMedication
            ref="medicationPopRef"
            v-model="attachmentStore.selectedMedications"
            :multiple="true"
            :person-id="props.personId"
          />

          <!-- Material submission error display -->
          <div v-if="materialSubmissionError" class="mx-2 text-xs text-red-600">
            <i class="pi pi-exclamation-triangle mr-1"></i>
            {{ materialSubmissionError }}
          </div>

          <SplitButton
            v-tooltip.left="!canSaveWithValidOperations ? 'Chọn nội dung điều trị, công việc dự kiến để lưu' : ''"
            :disabled="!canSaveWithValidOperations || isMaterialSubmitting"
            :loading="isMaterialSubmitting"
            :model="menuItems"
            class="ml-2"
            :label="isMaterialSubmitting ? 'Submitting...' : 'Save'"
            severity="primary"
            size="small"
            @click="saveAll(AttachmentStatus.UNPAID)"
          ></SplitButton>
        </div>
      </div>
    </div>

    <!-- Action Buttons - khi không có operations, hiển thị ở hàng riêng biệt -->
    <div
      v-if="selectedOperationIds.length === 0"
      class="col-start-9 col-end-13 flex h-full flex-row items-center justify-end space-x-2"
    >
      <Button
        v-if="Object.keys(attachmentStore.selectedMedications).length === 0"
        v-tooltip="'Thêm toa thuốc'"
        aria-label="Add medication"
        class="size-9 text-xs"
        icon="pi pi-file-plus"
        outlined
        severity="help"
        @click="toggleMedicationSelect($event)"
      />

      <SelectMedication
        ref="medicationPopRef"
        v-model="attachmentStore.selectedMedications"
        :multiple="true"
        :person-id="props.personId"
      />

      <!-- Material submission error display -->
      <div v-if="materialSubmissionError" class="mx-2 text-xs text-red-600">
        <i class="pi pi-exclamation-triangle mr-1"></i>
        {{ materialSubmissionError }}
      </div>

      <SplitButton
        v-tooltip.left="!canSaveWithValidOperations ? 'Chọn nội dung điều trị, công việc dự kiến để lưu' : ''"
        :disabled="!canSaveWithValidOperations || isMaterialSubmitting"
        :loading="isMaterialSubmitting"
        :model="menuItems"
        class="ml-2"
        :label="isMaterialSubmitting ? 'Submitting...' : 'Save'"
        severity="primary"
        size="small"
        @click="saveAll(AttachmentStatus.UNPAID)"
      ></SplitButton>
    </div>
  </div>

  <!-- Popovers and Selects -->
  <SearchAttachment
    ref="attachmentPopRef"
    v-model:selectedAttachment="attachmentStore.selectedParentAttachment"
    :default-filters="{ kind: 'product', product_type: 'service' }"
    :filter-product-type="true"
    :multiple="false"
    :person-id="props.personId"
    search-mode="paid_attachment"
    @update:selectedAttachment="selectParentAttachment"
  />

  <SearchProduct
    ref="productPopRef"
    v-model:selectedProduct="attachmentStore.selectedProduct"
    :defaultFilters="{ type: 'service' }"
    :multiple="false"
    @update:selectedProduct="selectProduct"
  />

  <OperationSelect
    ref="nextOperationPop"
    v-model="attachmentStore.nextOperations"
    :product-ids="attachmentStore.productIds"
  />
</template>
