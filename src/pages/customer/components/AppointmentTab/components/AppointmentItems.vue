<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import Chip from "primevue/chip";
import SplitButton from "primevue/splitbutton";
import { computed, reactive, watch } from "vue";

import { AppointmentReminderStatus } from "@/api/bcare-enum";
import { AppointmentResponse, ConstantResponse } from "@/api/bcare-types-v2";
import Lucide, { Icon } from "@/base-components/Lucide";
import Table from "@/base-components/Table";
import Tippy from "@/base-components/Tippy";
import { NoteInfo } from "@/components/InfoText";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { GLOBAL_TEXT, HEADER_APPOINTMENT } from "@/constants";
import { formatCustom, formatShortDate, formatShortDateTime } from "@/utils/time-helper";
import useColorHash from "@/utils/use-tag";
import Empty from "@/base-components/Empty";

const hashColor = useColorHash();
const emits = defineEmits<{
  (event: "updateAppointmentSuccess"): void;
  (event: "updateNotes", appointmentId: number, notes: string): void;
  (event: "onRemind", item: AppointmentResponse): void;
  (event: "onViewHistory", item: AppointmentResponse): void;
  (event: "onSendMessage", item: AppointmentResponse): void;
  (event: "onDelete", item: AppointmentResponse): void;
  (event: "onEditV2", item: AppointmentResponse): void;
  (event: "onEditConsultation", item: AppointmentResponse): void;
}>();
const props = defineProps<{
  appointments?: Array<AppointmentResponse>;
  settingConfigs: ConstantResponse | null;
}>();
const notes = reactive<Record<string, string>>({});

const statusConfig: Record<number, { icon: string; color: string }> = {
  1: { icon: "Lock", color: "bg-red-500" },
  2: { icon: "CalendarPlus", color: "bg-info" },
  3: { icon: "CheckCircle", color: "bg-success" },
  4: { icon: "AlertTriangle", color: "bg-warning" },
  5: { icon: "RefreshCcw", color: "bg-green-500" },
  6: { icon: "PlusCircle", color: "bg-blue-500" },
};

const getStatusChip = (statusId: number, isPast: boolean = false) => {
  const status = props.settingConfigs?.appointment_status?.[statusId];
  const { icon = "HelpCircle", color = "bg-slate-500" } = statusConfig[statusId] || {};

  return {
    label: status || "Không xác định",
    icon: icon as Icon,
    class: isPast ? "border border-slate-400 bg-transparent text-slate-500" : `${color} text-white`,
  };
};

// const now = useNow();

const isPastAppointment = (startTime: string) => {
  const appointmentDate = new Date(startTime);
  const today = new Date();

  // Reset time to start of day (00:00:00)
  appointmentDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);

  return appointmentDate < today;
};

watch(
  () => props?.appointments,
  () => {
    props.appointments?.forEach((item) => {
      if (item.id) {
        notes[item.id] = item.notes ?? "";
      }
    });
  },
);

// Move button logic here
const getButtonLabel = (item: AppointmentResponse) =>
  item.reminder_status !== AppointmentReminderStatus.REMINDED ? "Nhắc" : "Đã nhắc";

const isButtonDisabled = (item: AppointmentResponse) =>
  item.reminder_status === AppointmentReminderStatus.REMINDED;

const isArrived = (item: AppointmentResponse) => {
  return Boolean(item.arrived_at && item.arrived_at.trim());
};

const menuItems = computed(() => (item: AppointmentResponse) => [
  ...(!isPastAppointment(item.start_time)
    ? [
        {
          label: "Chỉnh sửa",
          icon: "Edit",
          command: () => {
            if (!item.doctor_id || item.doctor_id === 0) {
              emits("onEditConsultation", item);
            } else {
              emits("onEditV2", item);
            }
          },
          disabled: isArrived(item),
        },
      ]
    : []),
  {
    label: "Lịch sử",
    icon: "History",
    command: () => emits("onViewHistory", item),
  },
  {
    label: "Gửi SMS / ZNS",
    icon: "Send",
    class: "text-success",
    command: () => emits("onSendMessage", item),
  },
  // Chỉ hiển thị nút xóa nếu appointment không ở trong quá khứ
  ...(!isPastAppointment(item.start_time)
    ? [
        {
          label: "Xóa",
          icon: "Trash",
          class: "text-danger",
          command: () => emits("onDelete", item),
        },
      ]
    : []),
]);

const handleSplitButtonClick = (item: AppointmentResponse) => {
  if (!isButtonDisabled(item)) {
    emits("onRemind", item);
  }
};

const copyToClipboard = (startTime: string, endTime: string): void => {
  const date = useDateFormat(startTime, "DD/MM/YYYY").value;
  const start = useDateFormat(startTime, "HH:mm").value;
  const end = useDateFormat(endTime, "HH:mm").value;
  const formattedText = `${date} lúc ${start} - ${end} `;

  navigator.clipboard
    .writeText(formattedText)
    .then(() => console.log("Đã sao chép"))
    .catch((err) => console.error("Lỗi sao chép:", err));
};
</script>

<template>
  <div class="overflow-x-auto">
    <Table v-if="props.appointments && props.appointments.length > 0" class="min-w-full">
      <Table.Thead>
        <Table.Tr>
          <Table.Th
            v-for="(item, index) in HEADER_APPOINTMENT"
            :key="index"
            class="whitespace-nowrap !py-5"
          >
            <div class="flex items-center justify-between">
              {{ item.name }}
            </div>
          </Table.Th>
          <Table.Th></Table.Th>
        </Table.Tr>
      </Table.Thead>

      <Table.Tbody>
        <Table.Tr
          v-for="(item, key) in props?.appointments"
          :key="key"
          :class="[
            'ring-10 mb-5 rounded-md border-b border-l-4 border-slate-300 border-l-red-500 ring-blue-500 transition-all duration-200',
            {
              'border-l-blue-500': item.status === 3,
              'border-l-green-600 bg-green-200 shadow-md hover:shadow-lg dark:bg-slate-700/50':
                item.reminder_status === AppointmentReminderStatus.REMINDED &&
                !isPastAppointment(item.start_time),
              'bg-slate-100 text-slate-500 dark:bg-slate-800/50 dark:text-slate-400':
                isPastAppointment(item.start_time) &&
                item.reminder_status !== AppointmentReminderStatus.REMINDED,
              'bg-green-200 text-slate-500 dark:bg-slate-700/30 dark:text-slate-400':
                isPastAppointment(item.start_time) &&
                item.reminder_status === AppointmentReminderStatus.REMINDED,
            },
          ]"
        >
          <Table.Td
            class="w-44 border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            :class="{
              '[&_a]:text-slate-500 [&_span]:text-slate-500': isPastAppointment(item.start_time),
            }"
          >
            <div class="flex items-center gap-2">
              <UserAvatar :user="item.doctor" :muted="isPastAppointment(item.start_time)" />
              <div>
                <span class="whitespace-nowrap font-medium" v-if="item.doctor?.name">
                  {{ item.doctor?.name }}
                </span>
                <span
                  class="flex items-center gap-1 whitespace-nowrap font-medium text-danger dark:text-primary"
                  v-else
                >
                  Lịch hẹn LT
                  <i class="pi pi-star" />
                </span>
                <div
                  class="background mt-0.5 whitespace-nowrap text-sm text-slate-500"
                  v-if="props.settingConfigs?.appointment_type?.[item.type]"
                >
                  Hẹn:
                  <span
                    v-if="props.settingConfigs?.appointment_type?.[item.type]"
                    class="py-0.2 ml-1 inline-block whitespace-nowrap rounded-md px-1.5 text-sm"
                    :class="{
                      'text-white': !isPastAppointment(item.start_time),
                      'border border-slate-400 bg-transparent text-slate-500': isPastAppointment(
                        item.start_time,
                      ),
                    }"
                    :style="{
                      background: !isPastAppointment(item.start_time)
                        ? hashColor(
                            props.settingConfigs?.appointment_type?.[item.type]?.toString() ?? '1',
                            'source',
                          )
                        : 'transparent',
                    }"
                  >
                    {{ props.settingConfigs?.appointment_type?.[item.type] }}
                  </span>
                  <span v-else>Chưa xác định</span>
                </div>
              </div>
            </div>
          </Table.Td>

          <Table.Td
            class="cursor-pointer border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            :class="{
              '[&_.text-fuchsia-600]:text-slate-500': isPastAppointment(item.start_time),
            }"
          >
            <Tippy
              variant="primary"
              :content="GLOBAL_TEXT.COPIED"
              :options="{ trigger: 'click' }"
              @click="copyToClipboard(item.start_time, item.end_time)"
            >
              <div class="mt-1.5 flex items-center">
                <Lucide icon="CalendarDays" class="mr-1 h-4 w-4 text-warning" />
                <span class="text-sm font-medium">
                  {{ formatShortDate(item.end_time) }}
                </span>
              </div>

              <div class="flex w-max items-center">
                <Lucide icon="Clock" class="mr-1 h-4 w-4 text-yellow-500" />
                <span class="text-sm font-medium">
                  {{ formatCustom(item.start_time, "HH:mm") }} -
                  {{ formatCustom(item.end_time, "HH:mm") }}
                </span>
              </div>
            </Tippy>
          </Table.Td>

          <Table.Td
            :class="['border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600']"
          >
            <div class="flex min-w-max items-center">
              <Chip
                class="rounded-full px-1.5 py-1 text-sm font-medium"
                v-bind="getStatusChip(item.status, isPastAppointment(item.start_time))"
              >
                <template #icon>
                  <Lucide
                    :icon="getStatusChip(item.status).icon"
                    class="mr-1 h-3 w-3"
                    :class="{ 'text-slate-500': isPastAppointment(item.start_time) }"
                  />
                </template>
                <span v-if="item.status === 3" class="flex items-center">
                  <Lucide
                    icon="Check"
                    class="mr-1 h-3 w-3"
                    :class="{ 'text-slate-500': isPastAppointment(item.start_time) }"
                  />
                  <span
                    class="text-sm"
                    :class="{ 'text-slate-500': isPastAppointment(item.start_time) }"
                  >
                    {{ formatShortDateTime(item.arrived_at) }}
                  </span>
                </span>
              </Chip>
            </div>
          </Table.Td>

          <Table.Td
            class="border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            :class="{
              '[&_.note-info]:text-slate-500': isPastAppointment(item.start_time),
            }"
          >
            <NoteInfo
              :extra_notes="item.extra_notes"
              mode="list"
              :muted="isPastAppointment(item.start_time)"
            />
          </Table.Td>

          <Table.Td
            class="border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            :class="{
              '[&_.note-info]:text-slate-500': isPastAppointment(item.start_time),
            }"
          >
            <NoteInfo
              :size="'normal'"
              :notes="item.notes"
              mode="tag"
              :muted="isPastAppointment(item.start_time)"
              @update:notes="(newNotes) => emits('updateNotes', item.id, newNotes)"
              :editable="!isPastAppointment(item.start_time)"
            />
          </Table.Td>

          <Table.Td class="border-b-0 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600">
            <div class="flex items-center justify-end">
              <SplitButton
                size="small"
                :label="getButtonLabel(item)"
                @click="() => handleSplitButtonClick(item)"
                :model="menuItems(item)"
                :buttonProps="{
                  class: isButtonDisabled(item) ? 'cursor-not-allowed opacity-50 w-max' : 'w-max',
                }"
              >
                <template #item="slotProps">
                  <div class="flex cursor-pointer items-center p-3">
                    <Lucide :icon="slotProps.item.icon as Icon" class="mr-2 h-4 w-4" />
                    {{ slotProps.item.label }}
                  </div>
                </template>
              </SplitButton>
            </div>
          </Table.Td>
        </Table.Tr>
      </Table.Tbody>
    </Table>
    <div v-else class="p-4"><Empty /></div>
  </div>
</template>
