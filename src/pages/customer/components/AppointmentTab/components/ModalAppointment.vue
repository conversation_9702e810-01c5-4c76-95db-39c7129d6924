<!-- eslint-disable max-statements -->
<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { useScroll } from "@vueuse/core";
import SelectButton from "primevue/selectbutton";
import { computed, defineAsyncComponent, onUnmounted, reactive, ref, watch } from "vue";

import { AppointmentReminderStatus, DoctorType } from "@/api/bcare-enum";
import {
  AppointmentAddRequest,
  AppointmentResponse,
  AppointmentUpdateRequest,
  ConstantResponse,
} from "@/api/bcare-types-v2";
import { FormCheck, FormInput, FormLabel, InputGroup } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import DateTimeInput from "@/components/DateTimeInput/DateTimeInput.vue";
import { FormField } from "@/components/Form";
import { APPOINTMENT_IN_OPTIONS } from "@/constants";
import { rules, useFormValidation } from "@/composables/useFormValidation";
import {
  DoctorScheduleWithAppointments,
  formDataDefault,
  useAppointment,
} from "@/hooks/useAppointment";
import { calculateTimeDifference, deepClone, formatNumberWithCommas } from "@/utils/helper";
import { parseExtraNotesV2 } from "../utils";

// Async import
const DoctorCalendarView = defineAsyncComponent(() => import("./DoctorCalendarView.vue"));
const DoctorScheduleView = defineAsyncComponent(() => import("./DoctorScheduleView.vue"));

const props = defineProps<{
  isOpen: boolean;
  personId: number;
  settingConfigs: ConstantResponse | null;
  selectedItem?: AppointmentResponse;
}>();

const emits = defineEmits<{
  (event: "onClose"): void;
  (event: "updateAppointmentSuccess"): void;
}>();

// Form and UI state
let resExpectTask: string[] = [];

const formData = reactive<AppointmentAddRequest | AppointmentUpdateRequest>({
  ...deepClone(formDataDefault),
  person_id: props.personId,
});

const configExpectTask = ref(new Set<string>());
const configExpectTaskOther = ref(new Set<string>());
const expectedTask = ref(new Set<string>());
const expectedTaskOther = ref(new Set<string>());
const isLoading = ref(false);
const reminderFee = ref<number>(0);
const appointmentTime = ref<Date | Date[] | (Date | null)[] | null | undefined>(new Date());
const appointmentDuration = ref<number>(3);
const selectedTime = ref<Date | null>(null);
const formContainerRef = ref<HTMLElement | null>(null);
const scheduleData = ref<DoctorScheduleWithAppointments[]>([]);
const isLoadingSchedule = ref(false);
const selectedView = ref("schedule");

// Thêm options cho SelectButton
const viewOptions = [
  { icon: "pi pi-list", value: "schedule" },
  { icon: "pi pi-calendar", value: "calendar" },
];

// Thêm ref để track selected view

const {
  getWorkScheduleWithAppointments,
  roundToMinute,
  createAppointment,
  updateAppointment,
  fetchExpectTask,
  clearAppointments,
  getSortedAppointmentTypes,
} = useAppointment();

// Form validation setup
interface AppointmentFormValidation {
  type: number | undefined;
  selectedTime: Date | null;
  doctor_id: number | undefined;
}

const validationRules = {
  type: [rules.required("Vui lòng chọn loại hẹn")],
  selectedTime: [rules.required("Vui lòng chọn giờ hẹn")],
  doctor_id: [rules.required("Vui lòng chọn bác sĩ")],
};

const { errors, validateForm, clearErrors } =
  useFormValidation<AppointmentFormValidation>(validationRules);

// Transform appointment_type với thứ tự custom theo yêu cầu client
const appointmentTypeOptions = computed(() => {
  return getSortedAppointmentTypes(props.settingConfigs?.appointment_type, "options");
});
const appointmentStatusOptions = computed(() => {
  if (!props.settingConfigs?.appointment_status) return [];
  return Object.entries(props.settingConfigs.appointment_status).map(([value, label]) => ({
    value: Number(value),
    label,
  }));
});
// Add scroll handling
const { arrivedState } = useScroll(formContainerRef, {
  onScroll: (e) => {
    if (e instanceof WheelEvent) {
      const isScrollingUp = e.deltaY < 0;
      const isScrollingDown = e.deltaY > 0;

      if ((arrivedState.top && isScrollingUp) || (arrivedState.bottom && isScrollingDown)) {
        e.preventDefault();
      }
    }
  },
});
// Thêm function fetch schedule
const fetchScheduleData = async (date: Date) => {
  try {
    isLoadingSchedule.value = true;
    const formattedDate = useDateFormat(date, "YYYY-MM-DD").value;
    const result = await getWorkScheduleWithAppointments({
      from: `${formattedDate}T00:00:00+07:00`,
      to: `${formattedDate}T23:59:59+07:00`,
    });
    if (result && result.schedulesWithAppointments) {
      scheduleData.value = result.schedulesWithAppointments;
    } else {
      scheduleData.value = []; // Set to empty array if no data
    }
  } catch (error) {
    console.error("Error fetching schedule data:", error);
  } finally {
    isLoadingSchedule.value = false;
  }
};
// Xử lý khi date thay đổi từ DoctorScheduleView
const handleDateChange = async (newDate: undefined | null | Date | Date[] | (null | Date)[]) => {
  if (!(newDate instanceof Date) || isNaN(newDate.getTime())) {
    console.error("Invalid date provided:", newDate);
    return; // Exit early if the date is invalid
  }
  appointmentTime.value = newDate;
  await fetchScheduleData(newDate);
};
// Thêm hàm xử lý slot selection
const handleSlotSelect = ({
  doctorId,
  startTime,
  departmentId,
}: {
  doctorId: number;
  startTime: string;
  doctorName: string;
  departmentId?: number;
}) => {
  selectedTime.value = new Date(startTime);
  formData.doctor_id = doctorId;
  appointmentDuration.value = 3;
};

const doctors = computed(() => {
  return scheduleData.value.map((doctor) => {
    return {
      value: doctor.doctor_id,
      label: doctor.doctor_name,
      department_id: doctor.department_id,
    };
  });
});

const handleSubmitSchedule = async () => {
  try {
    // Validate form before submission
    const formValues = {
      type: formData.type,
      selectedTime: selectedTime.value,
      doctor_id: formData.doctor_id,
    };

    if (!validateForm(formValues)) {
      return;
    }

    isLoading.value = true;
    const startTime = roundToMinute(selectedTime.value as Date);
    const endTime = roundToMinute(
      new Date(startTime.getTime() + appointmentDuration.value * 60 * 1000),
    );

    // Convert times to UTC for comparison
    const normalizeDate = (dateStr: string) => new Date(dateStr).toISOString();

    const isTimeOrDoctorChanged =
      props.selectedItem &&
      (normalizeDate(startTime.toISOString()) !== normalizeDate(props.selectedItem.start_time) ||
        normalizeDate(endTime.toISOString()) !== normalizeDate(props.selectedItem.end_time) ||
        formData.doctor_id !== props.selectedItem.doctor?.id);

    const extraNotes = {
      ...(reminderFee.value > 0 && { reminder_fee: reminderFee.value }),
      ...(expectedTask.value.size > 0 && {
        expected_task: Array.from(expectedTask.value).join(";"),
      }),
      ...(expectedTaskOther.value.size > 0 && {
        expected_task_other: Array.from(expectedTaskOther.value).join(";"),
      }),
    };

    const appointmentData: AppointmentAddRequest = {
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      person_id: props.personId,
      doctor_id: formData.doctor_id || undefined,
      status: formData.status ?? 2,
      type: formData.type!, // Type is now required and validated
      notes: formData.notes,
      ...(Object.keys(extraNotes).length > 0 && {
        extra_notes: JSON.stringify(extraNotes),
      }),
    };

    if (props.selectedItem?.id) {
      const updateData = {
        id: props.selectedItem.id,
        ...appointmentData,
        ...(isTimeOrDoctorChanged && { reminder_status: AppointmentReminderStatus.OTHER }),
        modified: ["notes", "extra_notes"],
      };

      const res = await updateAppointment(updateData);
      if (res) {
        emits("updateAppointmentSuccess");
        emits("onClose");
      }
    } else {
      const res = await createAppointment(appointmentData);
      if (res) {
        emits("updateAppointmentSuccess");
        emits("onClose");
      }
    }
  } catch (error) {
    console.error("Error submitting appointment:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleTimeSelect = ({
  startTime,
  endTime,
  doctorId,
}: {
  startTime: string;
  endTime: string;
  doctorId: number;
}) => {
  selectedTime.value = new Date(startTime);
  const durationInMinutes = Math.round(
    (new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60),
  );
  appointmentDuration.value = durationInMinutes;
  formData.doctor_id = doctorId;
};

watch(reminderFee, (value) => {
  const reminderNote = value
    ? `Nhắc thanh toán ${formatNumberWithCommas(value.toString())} đồng.`
    : "";
  const existingNotes = formData.notes || "";
  formData.notes = existingNotes ? `${existingNotes} ${reminderNote}` : reminderNote;
});

watch(
  () => props.isOpen,
  async (isOpen) => {
    if (!isOpen) {
      // Clear errors when modal closes
      clearErrors();
      return;
    }
    try {
      // Clear errors when modal opens
      clearErrors();

      // Fetch expected tasks first
      resExpectTask = await fetchExpectTask({ id: props.personId });
      const value = props.selectedItem;
      // Reset all Sets
      configExpectTask.value.clear();
      expectedTask.value.clear();
      configExpectTaskOther.value.clear();
      expectedTaskOther.value.clear();
      // Handle date change and fetch schedule
      value?.start_time
        ? handleDateChange(new Date(value.start_time))
        : handleDateChange(new Date());

      if (value) {
        // Handle edit mode
        const {
          reminder_fee: dataFee,
          expected_task: dataTask,
          expected_task_other: dataTaskOther,
        } = parseExtraNotesV2(value.extra_notes);

        appointmentTime.value = new Date(value.start_time);
        selectedTime.value = appointmentTime.value;
        appointmentDuration.value = calculateTimeDifference(value.start_time, value.end_time);
        // Update form data
        Object.assign(formData, {
          notes: value.notes,
          status: value.status,
          date: appointmentTime.value,
          doctor_id: value.doctor?.id,
          type: value.type,
        });
        reminderFee.value = dataFee ? Number(dataFee) : 0;
        // Set tasks from existing appointment
        dataTask.forEach((task) => {
          configExpectTask.value.add(task);
          expectedTask.value.add(task);
        });
        dataTaskOther.forEach((task) => {
          configExpectTaskOther.value.add(task);
          expectedTaskOther.value.add(task);
        });
      } else {
        // Handle create mode
        Object.assign(formData, deepClone(formDataDefault));
        (formData as AppointmentAddRequest).person_id = props.personId; // Ensure person_id is set
        expectedTask.value = new Set<string>();
        expectedTaskOther.value = new Set<string>();
        configExpectTask.value = new Set<string>(resExpectTask);
        configExpectTaskOther.value = new Set<string>(resExpectTask);
        reminderFee.value = 0;
        selectedTime.value = null;
      }
    } catch (error) {
      console.error("Error initializing modal:", error);
    }
  },
  { immediate: true }, // Add immediate: true to run on component mount
);

// Cleanup khi modal unmount
onUnmounted(() => {
  clearAppointments();
  scheduleData.value = []; // clear schedule data
  selectedTime.value = null;
  appointmentTime.value = new Date();
  expectedTask.value.clear();
  expectedTaskOther.value.clear();
});
</script>

<template>
  <Dialog
    :visible="props.isOpen"
    modal
    fluid
    @update:visible="emits('onClose')"
    :style="{ width: '98vw', height: '!99vh' }"
    :pt="{
      header: { class: 'px-3 py-1' },
      content: { class: 'bg-slate-100 py-4' },
      footer: { class: 'px-3 py-1 flex justify-center' },
    }"
    closeOnEscape
    dismissableMask
    blockScroll
    destroyOnHide
  >
    <template #header>
      <div class="flex w-full items-center">
        <div class="flex items-center gap-4">
          <div class="text-xl font-semibold">Đặt lịch hẹn</div>
          <SelectButton
            v-model="selectedView"
            :options="viewOptions"
            optionLabel="value"
            optionValue="value"
          >
            <template #option="{ option }">
              <i :class="[option.icon, 'text-lg']"></i>
            </template>
          </SelectButton>
        </div>
      </div>
    </template>

    <div class="grid grid-cols-12 gap-2 overflow-y-auto xl:overflow-y-hidden">
      <div class="col-span-12 h-full xl:col-span-4 2xl:col-span-3">
        <div ref="formContainerRef" class="form-container h-full overflow-auto">
          <div class="box">
            <div class="p-2">
              <FormField label="Ngày hẹn">
                <DatePicker
                  v-model="appointmentTime"
                  :showIcon="true"
                  dateFormat="dd/mm/yy"
                  fluid
                  inline
                  @update:model-value="handleDateChange"
                  selectOtherMonths
                />
              </FormField>
              <FormField label="Giờ hẹn">
                <DateTimeInput
                  v-model="selectedTime"
                  :current-date="appointmentTime as Date | undefined"
                  timeOnly
                  :class="{ 'p-invalid': errors.selectedTime }"
                />
                <small v-if="errors.selectedTime" class="text-red-500">
                  {{ errors.selectedTime }}
                </small>
              </FormField>

              <FormField label="Hẹn trong (Phút)">
                <Select
                  v-model="appointmentDuration"
                  :options="APPOINTMENT_IN_OPTIONS"
                  optionLabel="label"
                  optionValue="value"
                  fluid
                />
              </FormField>

              <FormField label="Bác sĩ">
                <Select
                  v-model="formData.doctor_id"
                  :options="doctors"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Chọn bác sĩ"
                  fluid
                  :class="{ 'p-invalid': errors.doctor_id }"
                />
                <small v-if="errors.doctor_id" class="text-red-500">
                  {{ errors.doctor_id }}
                </small>
              </FormField>

              <FormField label="Loại hẹn">
                <Select
                  v-model="formData.type"
                  :options="appointmentTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Chọn loại hẹn"
                  fluid
                  :class="{ 'p-invalid': errors.type }"
                />
                <small v-if="errors.type" class="text-red-500">
                  {{ errors.type }}
                </small>
              </FormField>
            </div>
            <div class="border-y border-dashed border-primary/40 p-2">
              <FormField label="Nhắc phí">
                <InputNumber
                  v-model="reminderFee"
                  inputId="currency-us"
                  mode="currency"
                  currency="VND"
                  locale="vi-VN"
                  fluid
                />
              </FormField>

              <div class="mt-2">
                <FormLabel class="!mb-0">Công việc dự kiến</FormLabel>

                <div v-if="configExpectTask.size > 0">
                  <FormCheck v-for="task in configExpectTask" :key="task" class="mb-2 last:mb-0">
                    <FormCheck.Input
                      id="checkbox-switch-1"
                      v-model="expectedTask"
                      :value="task"
                      type="checkbox"
                    />
                    <FormCheck.Label html-for="checkbox-switch-1">{{ task }}</FormCheck.Label>
                  </FormCheck>
                </div>

                <div class="mt-2">
                  <InputGroup>
                    <FormInput
                      type="text"
                      placeholder="(Nhấn Enter để thêm CV khác)"
                      class="pr-20"
                      @keydown.enter="
                        (e) => {
                          const value = (e.target as HTMLInputElement).value;
                          if (value) {
                            (e.target as HTMLInputElement).value = '';
                            configExpectTaskOther.add(value);
                            expectedTaskOther.add(value);
                          }
                        }
                      "
                    />
                    <InputGroup.Text class="flex items-center">
                      <Lucide icon="CornerDownLeft" class="h-4 w-4 text-slate-500" />
                    </InputGroup.Text>
                  </InputGroup>
                </div>

                <div clas class="mt-2">
                  <FormCheck v-for="task in expectedTaskOther" :key="task" class="mb-2 last:mb-0">
                    <FormCheck.Input
                      id="checkbox-switch-1"
                      v-model="expectedTaskOther"
                      :value="task"
                      type="checkbox"
                    />
                    <FormCheck.Label html-for="checkbox-switch-1">{{ task }}</FormCheck.Label>
                  </FormCheck>
                </div>
              </div>
            </div>

            <div class="p-2">
              <FormField label="Ghi chú">
                <Textarea v-model="formData.notes" auto-resize rows="5" class="w-full" />
              </FormField>

              <FormField label="Trạng thái">
                <Select
                  v-model="formData.status"
                  :options="appointmentStatusOptions"
                  optionLabel="label"
                  optionValue="value"
                  fluid
                />
              </FormField>
            </div>
          </div>
        </div>
      </div>

      <div class="col-span-12 h-[calc(80vh-5rem)] xl:col-span-8 2xl:col-span-9">
        <DoctorCalendarView
          v-if="selectedView === 'calendar'"
          :schedule-data="scheduleData"
          :selected-date="appointmentTime"
          :is-loading="isLoadingSchedule"
          @on-time-select="handleTimeSelect"
        />
        <DoctorScheduleView
          v-else
          :schedule-data="scheduleData"
          :is-loading="isLoadingSchedule"
          @on-slot-select="handleSlotSelect"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-center">
        <Button label="Lưu" icon="pi pi-save" :loading="isLoading" @click="handleSubmitSchedule" />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.date-picker {
  :deep(.dp--tp-wrap) {
    display: none;
  }
  :deep(.dp__main) {
    justify-content: center;
  }
  :deep(.dp__month_year_row) {
    margin-bottom: 12px;
  }
  :deep(.dp__month_year_wrap) {
    button:nth-of-type(1) {
      margin: 0 auto;
    }
    button:nth-of-type(2) {
      display: none;
    }
  }
  :deep(.dp__today) {
    @apply border-primary bg-transparent;
  }
  :deep(.dp__active_date) {
    @apply border-primary bg-primary;
  }
  :deep(.dp__calendar_item) {
    font-size: 14px;
  }
  :deep(.dp__calendar_header_item) {
    font-size: 14px;
  }
  :deep(.dp__menu) {
    border: none;
  }
  :deep(.dp__outer_menu_wrap) {
    @apply w-full;
  }
}

.form-container {
  max-height: calc(80vh - 2.5rem); /* Accounting for padding */
  overflow-y: auto;
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

/* Hide scrollbar but keep functionality */
.form-container {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.form-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Optional: Add some padding to prevent content from touching the edges */
.box {
  margin-right: 0.5rem;
}
</style>
