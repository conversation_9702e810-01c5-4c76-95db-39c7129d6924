<template>
  <DataTable
    :columns="columns"
    :data="store.histories"
    :total-records="store.totalHistories || 0"
    :loading="store.loading || store.countLoading"
    :rows="store.pagination.pageSize"
    :rows-per-page-options="[30, 50, 100]"
    @page="handlePageChange"
    :custom-paginator="true"
    size="small"
    scrollable
    autoHeight
    class="mt-5 h-full"
    :sort-field="currentSortField"
    :sort-order="currentSortOrder"
    @sort="handleSort"
    removable-sort
    filterDisplay="row"
    show-header
    title="Deal Stage History"
    :custom-action-items="getHistoryActionItems"
    paginator
    :show-actions="{ custom: true }"
  >
    <template #changed_at="{ data }">
      <DateTime :time="data.changed_at" :showTime="true" size="sm" />
    </template>

    <template #person="{ data }">
      <PersonCard :person="data.person" submit-type="new-tab" size="small" />
    </template>

    <template #deal="{ data }">
      <DealSelectorPopover
        :deal="data.deal"
        :person-id="data.person?.id"
        :state="data.deal?.state"
        disabled
      />
    </template>

    <template #before="{ data }">
      <PipelineStageSelect
        :modelValue="data.before"
        placeholder="Stage cũ"
        selectionMode="single"
        :editable="false"
        :pipeline-id="2"
        v-if="data.before"
      />
      <span v-else class="italic text-gray-400">-</span>
    </template>

    <template #after="{ data }">
      <PipelineStageSelect
        :modelValue="data.after"
        placeholder="Stage mới"
        selectionMode="single"
        :editable="false"
        :pipeline-id="2"
        v-if="data.after"
      />
      <span v-else class="italic text-gray-400">-</span>
    </template>

    <template #tags="{ data }">
      <EntityTagManager
        v-if="data.deal && data.deal.id"
        :entity-id="data.deal.id"
        entity-type="deal"
        :initial-tags="data.deal.tags || []"
        size="xs"
      />
      <span v-else class="italic text-gray-400">-</span>
    </template>

    <template #related_users="{ data }">
      <div
        class="flex cursor-pointer items-center gap-1"
        @click.capture="(event) => toggleUserPopover(event, data)"
      >
        <UserAvatarGroup
          :users="combineUsers(data.sale_user, data.deal?.deal_assignment || [])"
          animationStyle="lift"
          size="small"
        />
      </div>
    </template>

    <template #changed_by="{ data }">
      <span v-if="data.changed_by">ID: {{ data.changed_by }}</span>
      <span v-else class="italic text-gray-400">Hệ thống</span>
    </template>

    <template #note="{ data }">
      <span>{{ data.note }}</span>
    </template>
    <template #reason="{ data }">
      <span>{{ data.reason }}</span>
    </template>

    <template #changed_at.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <DatePicker
          class="h-10 w-full"
          v-model="datePickerModel"
          showIcon
          selectionMode="range"
          manualInput
          dateFormat="dd/mm/yy"
          placeholder="Lọc theo ngày"
          @update:modelValue="store.applyFilters()"
          size="small"
          fluid
          showOtherMonths
          selectOtherMonths
          hideOnRangeSelection
        />
      </div>
    </template>

    <template #person.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <IconField class="h-10">
          <InputIcon class="pi pi-search" />
          <InputText
            v-model="store.filters.personSearch"
            type="text"
            class="h-full"
            placeholder="Tìm tên, SĐT, mã..."
            @update:modelValue="debouncedApplyFilters()"
            size="small"
            fluid
          />
        </IconField>
        <Button
          v-if="store.filters.personSearch"
          icon="pi pi-filter-slash"
          class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
          @click="store.clearFilter('personSearch')"
          aria-label="Clear Person Filter"
        />
      </div>
    </template>

    <template #before.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <PipelineStageSelect
          v-model="beforeStageFilterModel"
          placeholder="Lọc stage"
          selectionMode="multiple"
          @update:modelValue="store.applyFilters()"
          class="p-column-filter h-10"
          :pipeline-id="2"
        />
        <Button
          v-if="store.filters.beforeStages?.length"
          icon="pi pi-filter-slash"
          class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
          @click="store.clearFilter('beforeStages')"
          aria-label="Clear Before Stage Filter"
        />
      </div>
    </template>

    <template #after.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <PipelineStageSelect
          v-model="afterStageFilterModel"
          placeholder="Lọc stage"
          selectionMode="multiple"
          @update:modelValue="store.applyFilters()"
          class="p-column-filter h-10"
          :pipeline-id="2"
        />
        <Button
          v-if="store.filters.afterStages?.length"
          icon="pi pi-filter-slash"
          class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
          @click="store.clearFilter('afterStages')"
          aria-label="Clear After Stage Filter"
        />
      </div>
    </template>

    <template #tags.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <TagCategorySelect
          :modelValue="tagFilterModel"
          @update:modelValue="handleTagFilterChange"
          placeholder="Lọc thẻ"
          class="p-column-filter !h-10 w-full"
        />
        <Button
          v-if="store.filters.tags?.length"
          icon="pi pi-filter-slash"
          class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
          @click="store.clearFilter('tags')"
          aria-label="Clear Tags Filter"
        />
      </div>
    </template>

    <template #related_users.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <UserMultiAssign
          use-prime-vue-input
          v-model="userFilterModel"
          @update:modelValue="store.applyFilters()"
          show-inactive-users
          :max-display="1"
          placeholder="Chọn người liên quan"
          class="p-column-filter !h-10"
        />
        <Button
          v-if="store.filters.relatedUsers?.length"
          icon="pi pi-filter-slash"
          class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
          @click="store.clearFilter('relatedUsers')"
          aria-label="Clear Related Users Filter"
        />
      </div>
    </template>
  </DataTable>

  <!-- Rating Dialog Component -->
  <DealRatingDialog
    v-model:visible="isRatingDialogVisible"
    :deal="selectedDealForRating"
    @save="handleSaveRating"
  />

  <!-- Popover for UserAvatarGroup -->
  <Popover ref="op" :pt="{ content: { class: 'p-1 max-w-[400px]' } }">
    <TrackUserGroup
      v-if="selectedRowData"
      :sale-user="selectedRowData.sale_user"
      :deal-assignment="selectedRowData.deal?.deal_assignment"
    />
  </Popover>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import type { DataTableSortEvent } from "primevue/datatable";
import type { MenuItem } from "primevue/menuitem";
import type { SortOrder } from "@/hooks/useRawQuery-v2";
import type { DealResponse, UserShort } from "@/api/bcare-types-v2";
import { debounce } from "lodash";
import DatePicker from "primevue/datepicker";
import InputText from "primevue/inputtext";
import IconField from "primevue/iconfield";
import InputIcon from "primevue/inputicon";
import DateTime from "@/base-components/DateTime.vue";
import { DataTable } from "@/components/DataTable";
import { PersonCard } from "@/components/Person";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import DealSelectorPopover from "@/components/Deal/DealSelectorPopover.vue";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import UserMultiAssign from "@/components/User/UserMultiAssign.vue";
import EntityTagManager from "@/components/Tags/EntityTagManager.vue";
import TagCategorySelect from "@/components/Tags/TagCategorySelect.vue";
import { useDealStageHistoryStore } from "@/stores/deal-stage-history-store";
import { dealStageHistoryColumns } from "@/constants/columns/deal-stage-history-columns";
import Button from "primevue/button";
import DealRatingDialog from "../customer/components/DealsTab/DealRatingDialog.vue";
import useDeal, { RatingUpdatePayload } from "@/hooks/useDeal";
import Popover from "primevue/popover"; // Import Popover
import TrackUserGroup from "@/components/Track/TrackUserGroup.vue"; // Import TrackUserGroup

// Khởi tạo store
const store = useDealStageHistoryStore();
const { addDealUserRating, updateDealUserRating } = useDeal({ useStore: false });

// --- Define the default sort locally ---
const componentDefaultSort: SortOrder[] = [{ field: "dsh.changed_at", direction: "DESC" }];

// --- Sử dụng columns đã import ---
const columns = dealStageHistoryColumns;

// Rating Dialog State
const isRatingDialogVisible = ref(false);
const selectedDealForRating = ref<DealResponse | null>(null);

// Corrected Computed property for DatePicker v-model
const datePickerModel = computed<Date[] | undefined>({
  get: () => {
    const start = store.filters.changedAtRange?.start;
    const end = store.filters.changedAtRange?.end;
    // Ensure we return an array of two Dates or undefined
    const startDate = start ? new Date(start) : undefined;
    const endDate = end ? new Date(end) : undefined;
    // Return undefined if both are undefined, otherwise return the array
    return startDate || endDate ? ([startDate, endDate].filter(Boolean) as Date[]) : undefined;
  },
  set: (value: Date[] | undefined) => {
    store.filters.changedAtRange = {
      start: value?.[0] ?? null,
      end: value?.[1] ?? value?.[0] ?? null, // If only one date, use it for both start and end
    };
  },
});

// Corrected Computed property for Before Stage filter (PipelineStageSelect)
const beforeStageFilterModel = computed<number[] | undefined>({
  get: () => {
    // Directly use the store's array (or provide empty array if null)
    return store.filters.beforeStages ?? undefined;
  },
  set: (selectedIds: number[] | undefined) => {
    // Update the store directly with the number array (or null if empty)
    store.filters.beforeStages = selectedIds && selectedIds.length > 0 ? selectedIds : null;
  },
});

// Corrected Computed property for After Stage filter (PipelineStageSelect)
const afterStageFilterModel = computed<number[] | undefined>({
  get: () => {
    // Directly use the store's array (or provide empty array if null)
    return store.filters.afterStages ?? undefined;
  },
  set: (selectedIds: number[] | undefined) => {
    // Update the store directly with the number array (or null if empty)
    store.filters.afterStages = selectedIds && selectedIds.length > 0 ? selectedIds : null;
  },
});

// Corrected Computed property for User filter (UserMultiAssign)
const userFilterModel = computed<UserShort[]>({
  // Return UserShort[], never undefined
  get: () => {
    // Return the array from the store or an empty array if it's null/undefined
    return store.filters.relatedUsers ?? [];
  },
  set: (selectedUsers: UserShort[] | undefined) => {
    // Update the store. Set to null if the array is empty or undefined.
    store.filters.relatedUsers = selectedUsers && selectedUsers.length > 0 ? selectedUsers : null;
  },
});

// Tags Filter
const tagFilterModel = computed<number[] | null>({
  get: () => {
    return store.filters.tags ?? null;
  },
  set: (selectedTagIds: number[] | null | undefined) => {
    store.filters.tags = selectedTagIds && selectedTagIds.length > 0 ? selectedTagIds : null;
  },
});

// --- Debounced filter application for text input ---
const debouncedApplyFilters = debounce(store.applyFilters, 500);

// --- New Handler Function for Tag Filter ---
const handleTagFilterChange = (newValue: number[] | null) => {
  store.filters.tags = newValue && newValue.length > 0 ? newValue : null;
  store.applyFilters(); // Apply filters when the tag selection changes
};

// --- Pagination Handling ---
const handlePageChange = (event: { first: number; rows: number }) => {
  const newPage = Math.floor(event.first / event.rows) + 1;
  if (store.pagination.pageSize !== event.rows) {
    store.pagination.setPageSize(event.rows);
  } else if (store.pagination.page !== newPage) {
    store.pagination.goToPage(newPage);
  }
};

// --- Sorting Handling ---
const currentSortField = computed(() => store.sorting.orders[0]?.field);
const currentSortOrder = computed(() => (store.sorting.orders[0]?.direction === "ASC" ? 1 : -1));

const handleSort = (event: DataTableSortEvent) => {
  const sortField = event.sortField as string | null;

  const columnDefinition = columns.find((col) => col.field === sortField);
  const isSortable = columnDefinition?.sortable ?? false;

  if (sortField && isSortable) {
    const direction = event.sortOrder === 1 ? "ASC" : "DESC";
    store.sorting.setSort(sortField, direction);
    store.applyFilters();
  } else if (!sortField) {
    store.sorting.setOrders(componentDefaultSort);
    store.applyFilters();
  }
};

// --- Action Items Function ---
const getHistoryActionItems = (data: any): MenuItem[] => {
  const items: MenuItem[] = [];
  // Add Rating action if deal exists
  if (data.deal) {
    items.push({
      label: "Đánh giá",
      icon: "pi pi-star",
      command: () => openRatingDialog(data.deal),
    });
  }
  return items;
};

const openRatingDialog = (deal: DealResponse | undefined) => {
  console.log("Opening rating dialog for deal:", deal);
  if (deal) {
    selectedDealForRating.value = deal;
    isRatingDialogVisible.value = true;
  } else {
    console.warn("Cannot open rating dialog: Deal data is missing for this history entry.");
    // Optionally: Show a toast message to the user here
  }
};

// Handle Save Rating (Refactored)
const handleSaveRating = async (ratingsToSave: RatingUpdatePayload[]) => {
  if (!selectedDealForRating.value || !ratingsToSave || ratingsToSave.length === 0) {
    console.log("No ratings changes to save.");
    isRatingDialogVisible.value = false; // Close dialog even if nothing to save
    return;
  }
  console.log("Saving ratings:", ratingsToSave);

  try {
    const promises = ratingsToSave.map((ratingData) => {
      if (ratingData.existing_rating_id) {
        // Update existing rating
        console.log("Updating rating:", ratingData);
        return updateDealUserRating({
          id: ratingData.existing_rating_id,
          rating: ratingData.rating,
        });
      } else {
        // Add new rating
        console.log("Adding rating:", ratingData);
        return addDealUserRating({
          deal_user_id: ratingData.deal_user_id,
          category: ratingData.category,
          rating: ratingData.rating,
        });
      }
    });

    await Promise.all(promises);
    console.log("Ratings saved successfully.");
    // Reload data after saving to reflect changes in the TrackUserGroup
    await store.execute(); // Reload data using store's current filters/pagination
    // Optionally: Show success toast
  } catch (error) {
    console.error("Failed to save ratings:", error);
    // Optionally: Show error toast
  } finally {
    isRatingDialogVisible.value = false; // Close dialog
  }
};

// Load dữ liệu lần đầu khi component được mount (nếu chưa có)
onMounted(async () => {
  if (!store.histories || store.histories.length === 0) {
    store.loadInitialData();
  }
});

// Dọn dẹp store khi component bị unmount
onBeforeUnmount(() => {
  store.$dispose();
});

// Note: EntityTagManager handles its own optimistic updates, no need to provide reloadData

// Combine users for UserAvatarGroup
const combineUsers = (saleUser: any, dealAssignment: any[]) => {
  const users = [];
  if (saleUser) {
    users.push(saleUser);
  }
  if (Array.isArray(dealAssignment)) {
    users.push(...dealAssignment);
  }
  // Ensure uniqueness if needed, assuming users have an 'id' property
  const uniqueUsers = Array.from(new Map(users.map((user) => [user.id, user])).values());
  return uniqueUsers;
};

// Ref for Popover
const op = ref();

// Ref to hold data for the popover
const selectedRowData = ref<any>(null);

// Function to toggle Popover
const toggleUserPopover = (event: MouseEvent, rowData: any) => {
  selectedRowData.value = rowData;
  op.value.toggle(event);
};
</script>
