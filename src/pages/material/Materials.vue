<template>
  <MaterialFormDrawer
    v-model:visible="showMaterialForm"
    :material-id="selectedMaterialId"
    @success="handleFormSuccess"
  />

  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách vật tư"
      :columns="columns"
      :data="tableState.materials"
      :loading="false"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> <PERSON>h sách vật tư ({{ tableState.total }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button severity="primary" icon="pi pi-plus" label="Thêm vật tư" @click="handleCreate" />
      </template>

      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-box text-blue-500"></i>
          <div>
            <div class="font-medium">{{ data.name }}</div>
            <div v-if="data.code" class="text-xs">{{ data.code }}</div>
          </div>
        </div>
      </template>

      <template #cost_price="{ data }">
        <Money v-if="data.cost_price" :amount="data.cost_price" />
        <span v-else class="text-gray-400">-</span>
      </template>

      <template #status="{ data }">
        <div class="flex items-center gap-2">
          <i
            :class="[
              data.status === CommonStatus.ACTIVE
                ? 'pi pi-check-circle text-green-500'
                : 'pi pi-ban text-gray-500',
            ]"
          ></i>
          <span
            :class="[
              data.status === CommonStatus.ACTIVE ? 'text-green-600' : 'text-gray-600',
              'font-medium',
            ]"
          >
            {{ data.status === CommonStatus.ACTIVE ? "Active" : "Inactive" }}
          </span>
        </div>
      </template>

      <template #packaging_specification="{ data }">
        <span v-if="data.packaging_specification" class="text-sm">{{
          data.packaging_specification
        }}</span>
        <span v-else class="text-gray-400">-</span>
      </template>

      <template #description="{ data }">
        <span v-if="data.description" class="text-sm">{{ data.description }}</span>
        <span v-else class="text-gray-400">-</span>
      </template>
    </DataTable>
  </div>

  <ConfirmPopup />
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import Button from "primevue/button";
import Tag from "primevue/tag";
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, ref, watch } from "vue";

import type { MaterialListResponse, MaterialResponse as Material } from "@/api/bcare-types-v2";
import { CommonStatus } from "@/api/bcare-enum";
import Money from "@/base-components/Money.vue";
import { type ColumnDefinition, DataTable } from "@/components/DataTable";
import { useFilterList } from "@/hooks/useFilterList";
import useMaterial from "@/hooks/useMaterial";

import MaterialFormDrawer from "./components/MaterialFormDrawer.vue";

const { listMaterials, deleteMaterial } = useMaterial({ autoLoad: false });

// State
const tableState = ref<MaterialListResponse>({
  total: 0,
  total_page: 0,
  materials: [],
});
const perPage = ref<number>(10);
const currentPage = ref(1);

// Column definitions
const columns = computed<ColumnDefinition<Material>[]>(() => [
  {
    field: "name",
    header: "Tên vật tư",
    filterType: "text",
    filterPlaceholder: "Tìm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "cost_price",
    header: "Giá / đơn vị",
    showFilterMenu: false,
  },
  {
    field: "unit",
    header: "Đơn vị tính",
    filterType: "text",
    filterPlaceholder: "Tìm theo đơn vị",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "packaging_specification",
    header: "Quy cách đóng gói",
    showFilterMenu: false,
  },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Active", value: CommonStatus.ACTIVE },
      { title: "Inactive", value: CommonStatus.INACTIVE },
    ],
  },
  {
    field: "description",
    header: "Mô tả",
    showFilterMenu: false,
  },
]);

// Define loadList function first
const loadList = async (filters: Record<string, any>) => {
  try {
    const response = await listMaterials({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });
    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error loading materials:", error);
  }
};

// Filter configurations
const filterConfigs = {
  name: { field: "search", isFilter: false },
  unit: { field: "unit", isFilter: true },
  status: {
    field: "status",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
};

const defaultFilters = {
  page: 1,
  page_size: perPage.value,
};

// Filter handling
const { filters, currentFilterPayload } = useFilterList(
  (filters) => {
    loadList(filters);
  },
  filterConfigs,
  defaultFilters,
);

const handlePageChange = (event: any) => {
  currentPage.value = event.page + 1;
  loadList(currentFilterPayload.value);
};

const confirm = useConfirm();

const handleDelete = async (material: Material, e?: MouseEvent) => {
  confirm.require({
    target: e?.currentTarget as HTMLElement,
    message: `Bạn có chắc chắn muốn xóa vật tư "${material.name}"?`,
    header: "Xác nhận xóa vật tư",
    icon: "pi pi-exclamation-triangle",
    rejectProps: {
      label: "Hủy",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Xóa",
      severity: "danger",
    },
    accept: async () => {
      await deleteMaterial({ id: material.id, name: material.name });
      loadList(currentFilterPayload.value);
    },
    reject: () => {
      // Do nothing on reject
    },
  });
};

// Lifecycle
onMounted(() => {
  // Không cần gọi loadList ở đây
});

// Watchers
watch(perPage, (newPerPage) => {
  // Update default filters and reload
  defaultFilters.page_size = newPerPage;
  currentPage.value = 1;
  loadList(currentFilterPayload.value);
});

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

const showMaterialForm = ref(false);
const selectedMaterialId = ref<number>();

const handleCreate = () => {
  showMaterialForm.value = true;
};

const handleEdit = (material: Material) => {
  selectedMaterialId.value = material.id;
  showMaterialForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};
</script>
