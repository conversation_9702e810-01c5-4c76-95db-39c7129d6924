<template>
  <Drawer
    :closable="false"
    :modal="true"
    :style="{ width: '50rem' }"
    :visible="visible"
    blockScroll
    position="right"
    @hide="onClose"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <span class="text-lg font-medium">
        {{ isEdit ? "Cập nhật vật tư" : "Thêm vật tư" }}
      </span>
    </template>

    <form @submit.prevent="onSubmit">
      <div class="space-y-6">
        <BasicInformation v-model:formData="formData" :errors="errors" />
        <Divider class="!my-6" />
        <PricingInformation v-model:formData="formData" :errors="errors" />
      </div>
    </form>

    <template #footer>
      <Button icon="pi pi-times" label="Hủy" outlined severity="danger" @click="onClose" />
      <Button autofocus icon="pi pi-save" label="Lưu" @click="onSubmit" />
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import Divider from "primevue/divider";
import Drawer from "primevue/drawer";
import { computed, ref, watch } from "vue";

import { MaterialAddRequest } from "@/api/bcare-types-v2";
import { useFormValidation } from "@/composables/useFormValidation";
import useMaterial from "@/hooks/useMaterial";

import BasicInformation from "./MaterialFormBasicInfo.vue";
import PricingInformation from "./MaterialFormPricing.vue";
import { CommonStatus } from "@/api/bcare-enum";

interface Props {
  visible: boolean;
  materialId?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const { addMaterial, updateMaterial, getMaterial } = useMaterial();
const isEdit = computed(() => !!props.materialId);

const formData = ref<MaterialAddRequest>({
  name: "",
  code: "",
  unit: "",
  description: "",
  cost_price: 0,
  packaging_specification: "",
  status: 1, // Default to Active
});

const validationRules = {
  name: [
    {
      validate: (value: any) => !!value && value.trim().length > 0,
      message: "Tên nguyên liệu là bắt buộc",
    },
  ],
  unit: [
    {
      validate: (value: any) => !!value && value.trim().length > 0,
      message: "Đơn vị tính là bắt buộc",
    },
  ],
};

const { errors, validateForm, clearErrors } = useFormValidation(validationRules);

const resetForm = () => {
  formData.value = {
    name: "",
    code: "",
    unit: "",
    description: "",
    cost_price: 0,
    packaging_specification: "",
    status: CommonStatus.ACTIVE,
  };
  clearErrors();
};

const onClose = () => {
  emit("update:visible", false);
  resetForm();
};

const onSubmit = async () => {
  if (!validateForm(formData.value)) {
    return;
  }

  try {
    if (isEdit.value && props.materialId) {
      await updateMaterial({
        id: props.materialId,
        ...formData.value,
      });
    } else {
      await addMaterial(formData.value);
    }
    emit("success");
    onClose();
  } catch (error) {
    console.error("Error saving material:", error);
  }
};

watch(
  () => props.materialId,
  async (newValue) => {
    resetForm();
    if (newValue) {
      const material = await getMaterial({ id: newValue });
      if (material) {
        const { name, code, unit, description, cost_price, packaging_specification, status } =
          material;

        formData.value = {
          name,
          code: code || "",
          unit,
          description: description || "",
          cost_price: cost_price || 0,
          packaging_specification: packaging_specification || "",
          status,
        };
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.visible,
  (newValue) => {
    if (!newValue) {
      resetForm();
    }
  },
);
</script>
