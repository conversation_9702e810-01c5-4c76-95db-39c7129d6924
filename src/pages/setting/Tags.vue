<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { DataTable } from "@/components/DataTable";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import TagChip from "@/components/Tags/TagChip.vue";
import NewTagDialog from "@/components/Tags/NewTagDialog.vue";
import useTag from "@/hooks/useTag";
import type { TagResponse, TagUpdateRequest, TagDeleteRequest } from "@/api/bcare-types-v2";
import { getCategoryColorClasses } from "@/composables/useHashColor";
import { tagColumns } from "@/constants/columns/tag-columns";

import type { MenuItem } from "primevue/menuitem";

// Use Tag composable with store
const { tags, updateTag, deleteTag, isLoading, getCategories } = useTag({
  useStore: true,
  autoLoad: true,
});

// State for UI
const searchQuery = ref("");
const selectedCategory = ref("Tất cả");
const showNewTagModal = ref(false);

// Filter management for DataTable
const filters = ref({
  name: { value: "", matchMode: "contains" },
  description: { value: "", matchMode: "contains" },
  category: { value: "", matchMode: "contains" },
});

// Computed properties
const categoriesWithCounts = computed(() => {
  const allCount = tags.value.length;
  const categories = [{ name: "Tất cả", count: allCount }];

  const categoryGroups: Record<string, number> = {};
  tags.value.forEach((tag) => {
    const category = tag.category || "Chưa phân loại";
    categoryGroups[category] = (categoryGroups[category] || 0) + 1;
  });

  Object.entries(categoryGroups).forEach(([name, count]) => {
    categories.push({ name, count });
  });

  return categories;
});

// Computed property for filtered tags based on category selection
// DataTable will handle the text search filtering
const filteredTags = computed(() => {
  let filtered = tags.value;

  // Filter by category
  if (selectedCategory.value !== "Tất cả") {
    filtered = filtered.filter((tag) => {
      const category = tag.category || "Chưa phân loại";
      return category === selectedCategory.value;
    });
  }

  return filtered;
});

const existingCategories = computed(() => getCategories.value);

// Watch for changes in DataTable name filter and sync back to search query
watch(
  () => filters.value.name.value,
  (newValue) => {
    if (newValue !== searchQuery.value) {
      searchQuery.value = newValue || "";
    }
  },
);

// Responsive category management
const windowWidth = ref(window.innerWidth);
const maxVisibleCategories = computed(() => {
  // Responsive breakpoints for category buttons
  if (windowWidth.value >= 1536) return 8; // 2xl
  if (windowWidth.value >= 1280) return 7; // xl
  if (windowWidth.value >= 1024) return 6; // lg
  if (windowWidth.value >= 768) return 5; // md
  if (windowWidth.value >= 640) return 4; // sm
  return 3; // xs
});

const visibleCategories = computed(() =>
  categoriesWithCounts.value.slice(0, maxVisibleCategories.value),
);
const hiddenCategories = computed(() =>
  categoriesWithCounts.value.slice(maxVisibleCategories.value),
);
const hasHiddenCategories = computed(() => hiddenCategories.value.length > 0);

// Listen to window resize
const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth;
};

// Add event listener on mount
onMounted(() => {
  window.addEventListener("resize", updateWindowWidth);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWindowWidth);
});

// Menu items for dropdown
const menuRef = ref();
const dropdownMenuItems = computed<MenuItem[]>(() =>
  hiddenCategories.value.map((category) => ({
    label: `${category.name} (${category.count})`,
    command: () => {
      selectedCategory.value = category.name;
    },
    class: selectedCategory.value === category.name ? "bg-primary-50 text-primary-600" : "",
  })),
);

// Button props function following PersonFilterPreset pattern
const getCategoryButtonProps = (categoryName: string) => {
  const isSelected = selectedCategory.value === categoryName;
  if (isSelected) {
    return {
      severity: "primary" as const,
      outlined: false,
    };
  } else {
    return {
      severity: "secondary" as const,
      outlined: true,
    };
  }
};

// Event handlers
const handleSearch = () => {
  // Sync search query with DataTable name filter
  filters.value.name.value = searchQuery.value.trim() || "";
};

const handleTagUpdate = async (updatedTag: TagResponse | any) => {
  try {
    const updateRequest: TagUpdateRequest = {
      id: updatedTag.id,
      name: updatedTag.name,
      category: updatedTag.category,
      description: updatedTag.description,
    };
    await updateTag(updateRequest);
  } catch (err) {
    console.error("Failed to update tag:", err);
  }
};

const handleTagDelete = async (data: TagResponse) => {
  try {
    const deleteRequest: TagDeleteRequest = { id: data.id };
    await deleteTag(deleteRequest);
  } catch (err) {
    console.error("Failed to delete tag:", err);
  }
};

const handleTagCreated = () => {
  // The tag is automatically added to the store by the useTag composable
  // No need to manually update the UI as it's reactive
};
</script>

<template>
  <ContentWithFixedToolbar>
    <!-- Left Toolbar: New Tag Button + Search + Categories -->
    <template #left-toolbar>
      <div class="flex space-x-3 divide-x divide-solid">
        <!-- Add New Button -->
        <div class="flex flex-wrap gap-2">
          <Button
            label="Thêm mới"
            icon="pi pi-plus"
            @click="showNewTagModal = true"
            class="text-sm"
            severity="primary"
            fluid
          />
        </div>

        <!-- Search and Category Filters -->
        <div class="flex items-center gap-4 pl-3">
          <!-- Search -->
          <!-- <div class="relative">
            <IconField>
              <InputIcon class="pi pi-search" />
              <InputText
                v-model="searchQuery"
                placeholder="Tìm kiếm tag..."
                @input="handleSearch"
                fluid
                class="w-64 text-sm"
              />
            </IconField>
          </div> -->

          <!-- Category Filter Buttons -->
          <div class="flex items-center gap-2">
            <!-- Visible Category Buttons -->
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="category in visibleCategories"
                :key="category.name"
                :label="`${category.name} (${category.count})`"
                :icon="category.name === 'Tất cả' ? 'pi pi-list' : undefined"
                :severity="getCategoryButtonProps(category.name).severity"
                :outlined="getCategoryButtonProps(category.name).outlined"
                @click="selectedCategory = category.name"
                :class="{
                  'border border-gray-300 text-sm': true,
                  'border-0': selectedCategory === category.name,
                }"
              >
                <template #icon v-if="category.name !== 'Tất cả'">
                  <div
                    :class="[
                      'mr-2 h-3 w-3 rounded-full',
                      getCategoryColorClasses(category.name).dot,
                    ]"
                  ></div>
                </template>
              </Button>
            </div>

            <!-- More Categories Dropdown -->
            <div v-if="hasHiddenCategories" class="relative">
              <Button
                icon="pi pi-ellipsis-h"
                :label="`+${hiddenCategories.length}`"
                severity="secondary"
                outlined
                @click="menuRef?.toggle($event)"
                :class="{
                  'border border-gray-300 text-sm': true,
                }"
              />
              <Menu ref="menuRef" :model="dropdownMenuItems" :popup="true" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Right Toolbar: Empty -->
    <template #right-toolbar>
      <!-- Empty -->
    </template>

    <!-- Main Content: DataTable -->
    <DataTable
      :columns="tagColumns"
      :data="filteredTags"
      :loading="isLoading"
      :paginator="false"
      v-model:filters="filters"
      size="small"
      :show-actions="{ delete: true }"
      @on-delete="handleTagDelete"
      striped-rows
      :show-header="false"
      :showPaginator="false"
    >
      <!-- Tag Name Column with TagChip -->
      <template #name="{ data }">
        <TagChip
          :tag="data"
          :modifiable="true"
          :closable="false"
          size="sm"
          @update="handleTagUpdate"
        />
      </template>

      <!-- Category Column with Color Dot -->
      <template #category="{ data }">
        <div class="flex items-center gap-2">
          <div
            v-if="data.category"
            :class="['h-3 w-3 rounded-full', getCategoryColorClasses(data.category).dot]"
          ></div>
          <span class="font-medium">{{ data.category || "Chưa phân loại" }}</span>
        </div>
      </template>

      <!-- Description Column -->
      <template #description="{ data }">
        <span class="text-gray-600">{{ data.description || "-" }}</span>
      </template>
    </DataTable>

    <!-- New Tag Dialog -->
    <NewTagDialog
      v-model:visible="showNewTagModal"
      :existing-categories="existingCategories"
      @tag-created="handleTagCreated"
    />
  </ContentWithFixedToolbar>
</template>
