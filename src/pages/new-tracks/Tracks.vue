<template>
  <DataTable
    title="Khách hàng mới"
    :columns="trackColumns"
    :data="tracks"
    :loading="isLoading"
    :total-records="total"
    paginator
    :rows="10"
    v-model:filters="filters"
    @page="handlePageChange"
    size="small"
    class="mt-5"
    :show-actions="{ delete: true }"
    @on-delete="handleMarkAsVoid"
    :custom-action-items="getTrackActionItems"
  >
    <!-- Add header templates -->
    <template #left-header>
      <div class="flex items-center gap-2">
        <span class="text-base font-medium">Khách hàng mới ({{ total }})</span>
        <template v-if="selectedDateRange">
          <div class="flex items-center gap-1">
            <i class="pi pi-calendar text-yellow-500" />
            <span class="text-sm text-slate-600">
              {{ selectedDateRange.isOneDay ? `Ngày ` : `Từ ngày ` }}
              <span class="font-medium">{{ selectedDateRange.fromFormatted }}</span>
              {{ !selectedDateRange.isOneDay ? ` đến ` : `` }}
              <span v-if="!selectedDateRange.isOneDay" class="font-medium">
                {{ selectedDateRange.toFormatted }}
              </span>
            </span>
          </div>
        </template>
      </div>
    </template>

    <template #right-header>
      <Button
        icon="pi pi-file-excel"
        @click="() => handleExport()"
        :loading="isExporting"
        :disabled="isExporting"
        v-tooltip.focus="'Xuất file Excel'"
      />
    </template>

    <!-- Person column -->
    <template #person.full_name="{ data }">
      <PersonCard
        v-if="data.person"
        :person="data.person"
        @submitName="() => handleOpenPersonDetail(data.person?.id)"
        showCode
      />
    </template>

    <!-- Referrer column -->
    <template #referrer.full_name="{ data }">
      <PersonCard
        v-if="data.referrer"
        :person="data.referrer"
        @submitName="() => handleOpenPersonDetail(data.referrer?.id)"
        showCode
      />
      <span v-else class="italic text-gray-400">Không có người giới thiệu</span>
    </template>

    <!-- Users column (Replaced with new component) -->
    <template #user_id="{ data }">
      <TrackUserGroup
        :sale-user="data.sale_user"
        :deal-assignment="data.deal?.deal_assignment"
        @user-click="(user) => emit('user-click', user)"
      />
    </template>

    <!-- Begin date column -->
    <template #begin="{ data }">
      <DateTime :time="data.begin" showTime />
    </template>

    <!-- Pipeline/Stage column -->
    <template #deal_stage_id="{ data }">
      <PipelineStageSelect
        v-if="data.id"
        :modelValue="data.deal_stage_id"
        placeholder="Chọn stage"
        @update:modelValue="(newStageId) => handleStageChange(data, newStageId)"
        class="text-sm"
        icon-class="text-xs"
        label-class="max-w-[150px]"
        :pipeline-id="2"
      />
      <span v-else class="italic text-gray-400">Chưa có giai đoạn</span>
    </template>

    <!-- Deal column -->
    <template #deal_id="{ data }">
      <DealSelectorPopover
        :deal="data.deal"
        :state="data.state"
        :person-id="data.person_id"
        @update:deal="(newDeal) => handleDealUpdate(data, newDeal)"
      />
    </template>

    <!-- Tags column -->
    <template #deal.tags="{ data }">
      <EntityTagManager
        v-if="data.deal && data.deal.id"
        :entity-id="data.deal.id"
        entity-type="deal"
        :initial-tags="data.deal.tags || []"
        size="xs"
      />
    </template>

    <!-- *** REVISED: Custom Filter Slot for User ID *** -->
    <template #user_id.filter="{ filterModel, filterCallback }">
      <div class="p-column-filter flex items-center gap-2">
        <UserMultiAssign
          use-prime-vue-input
          :modelValue="filterModel.value || []"
          @update:modelValue="
            (selectedUsers) => {
              filterModel.value = selectedUsers;
              filterCallback();
            }
          "
          show-inactive-users
          :max-display="1"
          placeholder="Chọn người phụ trách"
        />
      </div>
    </template>

    <!-- Custom Filter Slot for Stage ID -->
    <template #deal_stage_id.filter="{ filterModel, filterCallback }">
      <PipelineStageSelect
        v-model="filterModel.value"
        :placeholder="'Chọn stage'"
        :pipeline-id="2"
        selectionMode="multiple"
        @update:modelValue="filterCallback()"
      />
    </template>

    <!-- *** NEW: Custom Filter Slot for Tags *** -->
    <template #deal.tags.filter="{ filterModel, filterCallback }">
      <TagCategorySelect
        :modelValue="filterModel.value"
        @update:modelValue="
          (newValue) => {
            filterModel.value = newValue;
            filterCallback();
          }
        "
        placeholder="Chọn thẻ"
        class="p-column-filter !h-10"
      />
    </template>
  </DataTable>

  <!-- Rating Dialog Component -->
  <DealRatingDialog
    v-model:visible="isRatingDialogVisible"
    :deal="selectedDealForRating"
    @save="handleSaveRating"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import Button from "primevue/button";
import type { MenuItem } from "primevue/menuitem";

import {
  Deal,
  DealResponse,
  NewTrackReportResponse,
  TrackUpdateRequest,
  Filter,
  TrackDynamicQuery,
} from "@/api/bcare-types-v2";
import type { RatingUpdatePayload } from "@/hooks/useDeal";
import DateTime from "@/base-components/DateTime.vue";
import { useTrackQuery } from "@/hooks/useTrackQuery";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import DataTable from "@/components/DataTable/DataTable.vue";
import PersonCard from "@/components/Person/PersonCard.vue";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import useTrack from "@/hooks/useTrack";
import DealSelectorPopover from "@/components/Deal/DealSelectorPopover.vue";
import DealRatingDialog from "@/pages/customer/components/DealsTab/DealRatingDialog.vue";
import TrackUserGroup from "@/components/Track/TrackUserGroup.vue";
import { extractDateRange, getDateStringForFilename } from "@/utils/time-helper";
import { handleDownload } from "@/utils/helper";
import EntityTagManager from "@/components/Tags/EntityTagManager.vue";
import TagCategorySelect from "@/components/Tags/TagCategorySelect.vue";
import UserMultiAssign from "@/components/User/UserMultiAssign.vue";

import { trackColumns, trackFilterConfigs } from "./tracks.columns";
import useDeal from "@/hooks/useDeal";

const emit = defineEmits<{
  (e: "edit", data: NewTrackReportResponse): void;
  (e: "delete", data: NewTrackReportResponse): void;
  (e: "user-click", user: any): void;
}>();

// Modal store for customer details
const modalStore = useModalCustomerStore();

// Current page tracking
const currentPage = ref(1);
const first = ref(0);

// Track query
const { tracks, total, fetchTracks, isLoading } = useTrackQuery();
const { updateTrack } = useTrack();
const { addDealUserRating, updateDealUserRating } = useDeal({ useStore: false });

// Rating Dialog State
const isRatingDialogVisible = ref(false);
const selectedDealForRating = ref<DealResponse | null>(null);

// Export State
const isExporting = ref(false);

// Computed for selected date range display
const selectedDateRange = computed(() => {
  const filters = currentFilterPayload.value?.filters;
  return extractDateRange(filters, "begin");
});

// Reset pagination when filters change
const resetPagination = () => {
  currentPage.value = 1;
  first.value = 0;
};

// Use filter query
const { filters, currentFilterPayload } = useFilterQuery(() => {
  resetPagination();
  loadTracks(true);
}, trackFilterConfigs);

// Add watcher for begin date filter to ensure it has a value
watch(
  () => filters.value.begin?.value,
  (newValue) => {
    if (!newValue || newValue.length === 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filters.value.begin = { value: [today, today] };
    }
  },
);

// Handle page change
const handlePageChange = (event: { first: number; rows: number }) => {
  first.value = event.first;
  const page = Math.floor(event.first / event.rows) + 1;
  currentPage.value = page;
  loadTracks(false);
};

// Load tracks data - SIMPLIFIED
const loadTracks = async (getCount: boolean = true) => {
  const pageToLoad = currentPage.value;
  const processedFilters = currentFilterPayload.value.filters?.map((filter: Filter) => {
    if (
      filter.field === "deal_stage_id" ||
      filter.field === "deal_assignment_user_ids" ||
      filter.field === "full_name"
    ) {
      return filter.value;
    }
    return filter;
  });

  // Pass the raw filter payload directly to fetchTracks
  const finalPayload: Partial<TrackDynamicQuery> = {
    offset: (pageToLoad - 1) * 10,
    limit: 10,
    ...currentFilterPayload.value,
    filters: processedFilters,
  };

  // fetchTracks will now handle the filter processing internally
  await fetchTracks(finalPayload, getCount);
};

// Open person detail modal
const handleOpenPersonDetail = (personId?: number) => {
  if (personId) {
    modalStore.openModal(personId.toString());
  }
};

// Renamed from handleDealSelected for clarity
const handleDealUpdate = async (trackData: NewTrackReportResponse, selectedDeal: Deal) => {
  if (!trackData.id || !selectedDeal || trackData.deal_id === selectedDeal.id) {
    console.log("No update needed or missing data.");
    return;
  }

  console.log(`Switching deal for track ${trackData.id} to deal ${selectedDeal.id}`);
  try {
    await updateTrack({
      id: trackData.id,
      deal_id: selectedDeal.id,
      deal_stage_id: selectedDeal.stage_id,
    });
    // Reload current page data without resetting count to reflect change
    await loadTracks(false);
  } catch (error) {
    console.error("Failed to switch deal:", error);
    // TODO: Add user feedback (e.g., toast notification) for error
  }
};

// Handle stage change
const handleStageChange = async (data: NewTrackReportResponse, stageId?: number) => {
  if (!data.id || stageId === undefined) {
    console.error("Missing track ID or stageId for update");
    return;
  }
  console.log(`Changing stage for track ${data.id} to stage ${stageId}`);
  try {
    const payload: TrackUpdateRequest = { id: data.id, deal_stage_id: stageId };
    await updateTrack(payload);
    // Reload current page data without resetting count
    await loadTracks(false);
  } catch (error) {
    console.error("Failed to update track stage:", error);
    // Optionally: Add user feedback (e.g., toast notification)
  }
};

// Function to generate custom action items for a track
const getTrackActionItems = (data: NewTrackReportResponse): MenuItem[] => {
  const items: MenuItem[] = [];

  if (data.deal) {
    items.push({
      label: "Đánh giá",
      icon: "pi pi-star",
      command: () => openRatingDialog(data.deal),
    });
  }

  return items;
};

// Mark track state as void (empty string) when delete action is triggered
const handleMarkAsVoid = async (data: NewTrackReportResponse) => {
  if (!data.id) {
    console.error("Missing track ID for update");
    return;
  }
  console.log(`Marking track ${data.id} state as void`);
  try {
    await updateTrack({ id: data.id, state: "", modified: ["state"] });
    await loadTracks(false); // Reload data after update
  } catch (error) {
    console.error("Failed to mark track state as void:", error);
  }
};

// Open Rating Dialog
const openRatingDialog = (deal: DealResponse | undefined) => {
  if (deal) {
    selectedDealForRating.value = deal;
    isRatingDialogVisible.value = true;
  } else {
    console.warn("Cannot open rating dialog: Deal data is missing for this track.");
    // Optionally: Show a toast message to the user
  }
};

// Handle Save Rating (Refactored)
const handleSaveRating = async (ratingsToSave: RatingUpdatePayload[]) => {
  if (!selectedDealForRating.value || !ratingsToSave || ratingsToSave.length === 0) {
    console.log("No ratings changes to save.");
    isRatingDialogVisible.value = false; // Close dialog even if nothing to save
    return;
  }
  console.log("Saving ratings:", ratingsToSave);

  try {
    const promises = ratingsToSave.map((ratingData) => {
      if (ratingData.existing_rating_id) {
        // Update existing rating
        console.log("Updating rating:", ratingData);
        return updateDealUserRating({
          id: ratingData.existing_rating_id,
          rating: ratingData.rating,
        });
      } else {
        // Add new rating
        console.log("Adding rating:", ratingData);
        return addDealUserRating({
          deal_user_id: ratingData.deal_user_id,
          category: ratingData.category,
          rating: ratingData.rating,
        });
      }
    });

    await Promise.all(promises);
    console.log("Ratings saved successfully.");
    // Reload data after saving to reflect changes in the TrackUserGroup
    await loadTracks(false);
    // Optionally: Show success toast
  } catch (error) {
    console.error("Failed to save ratings:", error);
    // Optionally: Show error toast
  } finally {
    isRatingDialogVisible.value = false; // Close dialog
  }
};

// Handle export function - SIMPLIFIED
const handleExport = async () => {
  if (isExporting.value) return;
  isExporting.value = true;

  try {
    // Prepare payload, let fetchTracks handle processing and export flag
    const exportPayload: Partial<TrackDynamicQuery> = {
      ...currentFilterPayload.value, // Pass the current filters and sorts
      limit: undefined, // Let fetchTracks handle removing these for export
      offset: undefined, // Let fetchTracks handle removing these for export
      // export: true, // fetchTracks handles this via the isExport flag
    };

    // Pass the payload and the isExport flag = true
    const res = await fetchTracks(exportPayload, false, true); // Pass export flag

    if (!res.data?.result?.file_url) {
      console.error("Export failed: No file URL returned from API.", res);
      throw new Error("No file URL returned");
    }

    // getDateStringForFilename needs the original filters before processing
    const dateStr = getDateStringForFilename(currentFilterPayload.value.filters, "begin");
    const filename = `khach_hang_moi${dateStr ? `_${dateStr}` : ""}.xlsx`;
    await handleDownload(res.data.result.file_url, filename);
  } catch (error) {
    console.error("Failed to export tracks:", error);
    // Add user feedback (e.g., toast notification)
  } finally {
    isExporting.value = false;
  }
};

// Initial load
onMounted(async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  filters.value = {
    ...filters.value,
    begin: { value: [today, today] },
  };
});

// Note: EntityTagManager handles its own optimistic updates, no need to provide reloadData
</script>
