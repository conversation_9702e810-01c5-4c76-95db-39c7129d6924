<template>
  <CashFlowLayout
    ref="layoutRef"
    @add-cashflow="handleAddCashFlow"
    @refresh-data="refreshData"
    @filters-changed="applyFilters"
    @filter-update="handleFilterUpdate"
  >
    <!-- Summary Section -->
    <template #summary>
      <CashFlowSummary
        :summary="summary"
        :total-transactions="getCashFlowCount"
        :pending-count="getPendingCashFlows.length"
        :approved-count="getApprovedCashFlows.length"
      />
    </template>

    <!-- Left Panel - Cash Flow List -->
    <template #left-panel>
      <div class="rounded-lg border bg-white">
        <div class="flex items-center justify-between gap-1 border-b border-gray-100 p-4">
          <div class="flex items-center gap-2">
            <h3 class="font-semibold text-gray-800">Giao dịch</h3>
            <span class="text-xs text-gray-500">
              <span>Tất cả giao dịch</span>
            </span>
          </div>
          <Badge
            :value="filteredCashFlows.length"
            class="size-6 rounded-full p-1 text-xs font-normal"
            severity="info"
          />
        </div>
        <div class="border-b p-4">
          <InputText v-model="searchQuery" size="small" fluid placeholder="Tìm kiếm..." />
        </div>

        <!-- Cash Flow List -->
        <div class="overflow-y-auto md:max-h-[35rem]">
          <CashFlowListItem
            v-for="(cashflow, index) in filteredCashFlows"
            :key="cashflow.id"
            :cashflow="cashflow"
            :is-selected="selectedCashFlow?.id === cashflow.id"
            :class="{ 'bg-gray-50': index % 2 === 0 }"
            @click="handleCashFlowSelect(cashflow)"
          />

          <!-- Empty State -->
          <div v-if="filteredCashFlows.length === 0" class="py-8 text-center">
            <Lucide icon="Inbox" class="mx-auto h-12 w-12 text-gray-400" />
            <p class="mt-2 text-gray-500">Chưa có giao dịch nào</p>
          </div>
        </div>

        <!-- Summary Stats -->
        <div class="border-t border-gray-100 p-4">
          <div class="flex flex-col gap-3">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Tổng thu</span>
              <Money :amount="summary.total_income" variant="success" size="small" />
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Tổng chi</span>
              <Money :amount="summary.total_expense" variant="danger" size="small" />
            </div>
            <div class="flex items-center justify-between border-t pt-3 font-medium">
              <div class="flex items-center gap-2">
                <span class="text-gray-900">Tồn quỹ</span>
                <button
                  @click="togglePaymentMethodPopover('net', $event)"
                  class="rounded p-1 text-gray-400 transition-colors hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                  :aria-label="'Xem chi tiết phương thức thanh toán cho tồn quỹ'"
                >
                  <Lucide icon="Info" class="h-4 w-4" />
                </button>
              </div>
              <Money
                :amount="summary.net_amount"
                :variant="summary.net_amount >= 0 ? 'success' : 'danger'"
                size="small"
              />
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Right Panel - Cash Flow Detail/Form -->
    <template #right-panel>
      <CashFlowFormPanel
        v-if="showCashFlowForm"
        :cashflow="editingCashFlow"
        @cash-flow-saved="handleCashFlowSaved"
        @cancel="handleFormCancel"
      />
      <CashFlowDetailPanel
        v-else-if="selectedCashFlow"
        :cashflowId="selectedCashFlow.id"
        @edit-cash-flow="handleEditCashFlow"
        @cash-flow-updated="handleCashFlowUpdated"
        @cash-flow-deleted="handleCashFlowDeletedComplete"
      />
      <div v-else class="flex h-full items-center justify-center rounded-lg bg-white shadow">
        <div class="text-center">
          <Lucide icon="FileText" class="mx-auto h-12 w-12 text-gray-400" />
          <p class="mt-2 text-gray-500">Chọn một giao dịch để xem chi tiết</p>
          <Button
            label="Tạo giao dịch mới"
            class="mt-4"
            severity="primary"
            @click="handleAddCashFlow"
          >
            <template #icon>
              <Lucide icon="Plus" class="h-4 w-4" />
            </template>
          </Button>
        </div>
      </div>
    </template>
  </CashFlowLayout>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import Lucide from "@/base-components/Lucide";
import Money from "@/base-components/Money.vue";
import useCashflow from "@/hooks/useCashflow";

import { useCashFlowFilters } from "@/composables/useCashFlowFilters";
import { useCashFlowNavigation } from "@/composables/useCashFlowNavigation";

import CashFlowLayout from "./components/CashFlowLayout.vue";
import CashFlowSummary from "./components/CashFlowSummary.vue";
import CashFlowListItem from "./components/CashFlowListItem.vue";
import CashFlowDetailPanel from "./components/CashFlowDetailPanel.vue";
import CashFlowFormPanel from "./components/CashFlowFormPanel.vue";

// Use the composable with store (real API)
const {
  cashflows,
  summary,
  getCashFlowCount,
  getPendingCashFlows,
  getApprovedCashFlows,
  fetchCashFlows,
  fetchCashFlowSummary,
} = useCashflow({ useStore: true });

// Use composables for extracted logic
const { buildFilters, updateTypeFilter, updateStateFilter } = useCashFlowFilters();
const {
  selectedCashFlow,
  showCashFlowForm,
  editingCashFlow,
  handleCashFlowSelect,
  handleAddCashFlow,
  handleEditCashFlow,
  handleFormCancel,
  handleCashFlowDeleted,
} = useCashFlowNavigation(cashflows);

// State
const searchQuery = ref("");
const layoutRef = ref();

// Filtered cash flows based on search
const filteredCashFlows = computed(() => {
  if (!searchQuery.value) {
    return cashflows.value.slice(0, 50); // Improved performance limit
  }

  const query = searchQuery.value.toLowerCase();
  return cashflows.value
    .filter(
      (cashflow) =>
        cashflow.description.toLowerCase().includes(query) ||
        cashflow.category?.name?.toLowerCase().includes(query) ||
        cashflow.creator?.name?.toLowerCase().includes(query),
    )
    .slice(0, 50);
});

// Current filter status is now handled by useCashFlowFilters composable

// Menu items are now handled by CashFlowMenuBar component

// Filter handlers
const handleFilterUpdate = (type: "income" | "expense" | null, state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null) => {
  updateTypeFilter(type);
  if (state !== undefined) {
    updateStateFilter(state);
  }
  applyFilters();
};

// Date range watching is now handled by CashFlowFilters component

const applyFilters = () => {
  const filters = buildFilters();
  const reportRequest = { ...filters };

  // Apply filters using the useCashflow hook
  Promise.all([
    fetchCashFlows({ filter: filters as any }),
    fetchCashFlowSummary(reportRequest)
  ]);
};

// Payment Method Popover methods - now handled by CashFlowPaymentMethodPopover component
const togglePaymentMethodPopover = (type: "income" | "expense" | "net", event: Event) => {
  layoutRef.value?.showPaymentMethodPopover(type, summary.value, event);
};

// Event handlers are now handled by useCashFlowNavigation composable

const handleCashFlowSaved = async () => {
  await refreshData();
  showCashFlowForm.value = false;
};

const handleCashFlowUpdated = async () => {
  await refreshData();
};

const handleCashFlowDeletedComplete = async () => {
  await refreshData();
  handleCashFlowDeleted();
};

// Export and print functionality are now handled by CashFlowMenuBar component

const refreshData = async () => {
  try {
    applyFilters();
  } catch (error) {
    console.error("Failed to refresh data:", error);
  }
};

// URL handling and selection logic are now handled by useCashFlowNavigation composable

// Initialize data
onMounted(() => {
  applyFilters();
});
</script>
