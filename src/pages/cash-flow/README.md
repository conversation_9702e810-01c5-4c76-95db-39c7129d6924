# Cash Flow Management Page

## 📋 Overview
Trang quản lý dòng tiền với các thống kê chi tiết và hiển thị giao dịch gần đây sử dụng mock data.

## 🎯 Features Implemented

### 1. **Statistic Cards Display**
- **Tổng quan tài chính**: 4 cards chính hiển thị thu nhập, chi phí, dòng tiền ròng, và giao dịch chờ phê duyệt
- **Thống kê hoạt động**: 4 cards bổ sung hiển thị tổng giao dịch, đã phê du<PERSON>, đang chờ, và tỷ lệ phê duyệt
- **Responsive design**: Tự động điều chỉnh layout trên mobile và desktop

### 2. **Transaction Analysis**
- **Phân loại giao dịch**: Breakdown theo loại (thu nhập vs chi phí)
- **Trạng thái giao dịch**: Phân tích theo state (PENDING, APPROVED, PAID, REJECTED)
- **Visual indicators**: Icons và colors phù hợp cho từng loại

### 3. **Recent Transactions Preview**
- **5 giao dịch gần nhất**: Hiển thị theo thời gian tạo
- **Quick view**: Thông tin cơ bản với icons và tags
- **Navigation ready**: Button "Xem tất cả" để chuyển đến danh sách đầy đủ

## 🏗️ Component Structure

```
src/pages/cash-flow/
├── index.vue                      # Main page component with integrated list view
├── components/
│   ├── CashFlowSummary.vue       # Reusable summary statistics cards
│   ├── CashFlowListItem.vue      # Individual transaction item component
│   ├── CashFlowDetailPanel.vue   # Transaction detail panel (right side)
│   └── CashFlowFormPanel.vue     # Add/Edit transaction form panel
└── README.md                     # This file
```

## 📊 Mock Data Usage

### Data Source
- **Store**: `src/stores/cash-flow-store.ts`
- **Composable**: `src/hooks/useCashflow.ts`
- **Mock transactions**: 12 detailed transactions with various states and types

### Statistics Calculated
- **Total Income**: Sum of all income transactions (PAID status)
- **Total Expense**: Sum of all expense transactions (PAID status)
- **Net Amount**: Income - Expense
- **Pending Count**: Number of PENDING transactions
- **Approval Rate**: Percentage of approved transactions

## 🎨 UI/UX Features

### Design Consistency
- **Colors**: Semantic colors following RefundTab patterns
  - Success (green): Income, positive values
  - Danger (red): Expenses, negative values
  - Warning (orange): Pending, neutral values
  - Info (blue): General information
- **Typography**: Consistent with existing financial modules
- **Spacing**: Standard padding and margins

### Interactive Elements
- **Hover effects**: Cards have subtle hover animations
- **Loading states**: Spinner for async operations
- **Empty states**: Friendly messages when no data
- **Responsive**: Mobile-first design approach

## 🚀 Navigation

### Route Information
- **Path**: `/cash-flow`
- **Name**: `top-menu-cash-flow`
- **Breadcrumb**: "Quản lý dòng tiền"

### Access
Navigate to the page using the router or direct URL: `http://localhost:3000/cash-flow`

## 🔧 Technical Implementation

### Components Used
- **StatisticCard**: Custom reusable component with PrimeVue icons
- **PrimeVue Components**: Badge, Tag, Button
- **Tailwind CSS**: Inline styling for consistency

### Data Flow
1. **useCashflow composable** provides mock data and computed statistics
2. **CashFlowSummary component** renders main and operational statistics
3. **Main page** displays summary, analysis, and recent transactions

### Performance
- **Computed properties**: Efficient reactive calculations
- **Component splitting**: Modular architecture for maintainability
- **Lazy loading**: Ready for future optimization

## 📱 Responsive Behavior

### Breakpoints
- **Mobile**: Single column layout
- **Tablet**: 2 columns for cards
- **Desktop**: 4 columns for optimal space usage

### Grid System
- **Statistics**: `grid-cols-1 sm:grid-cols-2 lg:grid-cols-4`
- **Analysis**: `grid-cols-1 lg:grid-cols-2`
- **Transactions**: Full width with responsive padding

## 🎯 Next Steps

### Phase 1 Completion
- [x] Mock data setup
- [x] Statistic cards implementation
- [x] Basic page layout
- [x] Responsive design

### Phase 2 Completion
- [x] Transaction list component (integrated in main page)
- [x] Transaction detail panel
- [x] Add/Edit transaction form panel
- [x] Real API integration with store

### Phase 3 Planning
- [ ] Advanced filtering implementation
- [ ] Export functionality
- [ ] Bulk operations
- [ ] Transaction categories management
- [ ] Payment method tracking

## 🔍 Testing

### Mock Data Verification
- Open browser console to see logged data
- Verify calculations match displayed statistics
- Test responsive behavior on different screen sizes

### Visual Testing
- Check color consistency with RefundTab
- Verify icon alignment and sizing
- Test hover states and animations
