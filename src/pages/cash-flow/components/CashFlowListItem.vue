<script setup lang="ts">
import { computed } from "vue";
import type { CashFlowWithRelations } from "@/api/bcare-types-v2";
import {
  getCashFlowDisplayLabel,
  getCashFlowDisplaySeverity,
  type CashFlowState,
  type CashFlowType,
} from "@/api/bcare-enum";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import { RichTextNote } from "@/components/WysiwgEditor";
import CashFlowNotePopover from "./CashFlowNotePopover.vue";

const props = defineProps<{
  cashflow: CashFlowWithRelations;
  isSelected: boolean;
}>();

defineEmits<{
  (e: "note-added"): void;
}>();

// Computed properties
const noteCount = computed(() => {
  return props.cashflow?.notes?.length || 0;
});

const getStateTag = (cashflow: CashFlowWithRelations) => {
  const state = cashflow.state as CashFlowState;
  const type = cashflow.type as CashFlowType;

  return {
    label: getCashFlowDisplayLabel(type, state),
    severity: getCashFlowDisplaySeverity(type, state),
  };
};
</script>

<template>
  <div
    :class="{
      'border-primary-500': isSelected,
      'border-transparent': !isSelected,
      'ring-1 ring-blue-200': noteCount > 0 && !isSelected,
    }"
    class="cursor-pointer border-l-4 p-3 transition-colors hover:bg-gray-50"
  >
    <!-- Main horizontal layout -->
    <div class="flex items-center justify-between gap-3">
      <!-- Left section: ID + Description -->
      <div class="min-w-0 flex-1">
        <div class="mb-1 flex min-w-0 max-w-full items-center gap-2">
          <Chip class="min-w-0 flex-1 overflow-hidden border py-0 pl-0 pr-2">
            <span
              class="flex size-5 flex-shrink-0 items-center justify-center rounded-full bg-primary text-xs text-primary-contrast"
            >
              {{ String(cashflow.id).slice(-2).padStart(2, "0") }}
            </span>
            <span class="ml-2 min-w-0 truncate text-xs font-medium text-gray-900">
              {{ cashflow.category?.name || `Danh mục ${cashflow.category_id}` }}
            </span>
          </Chip>
        </div>
        <div class="space-y-1">
          <div class="min-h-[1rem] text-xs text-gray-500">
            <RichTextNote
              v-if="cashflow.description"
              :content="cashflow.description"
              :wrapper-mode="false"
              :max-lines="2"
              size="xs"
              class="max-w-full"
            />
            <span v-else class="italic text-gray-400">Không có mô tả</span>
          </div>
          <!-- Payment DateTime -->
          <div v-if="cashflow.paid_at" class="flex items-center gap-1 text-xs text-gray-400">
            <DateTime :time="cashflow.paid_at" size="xs" />
          </div>
        </div>
      </div>

      <!-- Right section: Amount + Status + Actions -->
      <div class="flex flex-col items-end gap-1">
        <!-- Amount -->
        <div class="text-right">
          <Money
            :amount="cashflow.amount"
            :variant="cashflow.type === 'income' ? 'success' : 'danger'"
            size="small"
            custom-class="font-semibold"
          />
        </div>

        <!-- Actions and Status Tag -->
        <div class="flex items-center gap-2">
          <!-- Note Popover with badge count -->
          <CashFlowNotePopover :cash-flow-id="cashflow.id" :note-count="noteCount" @note-added="$emit('note-added')" />

          <Tag
            :severity="getStateTag(cashflow).severity"
            :value="getStateTag(cashflow).label"
            class="whitespace-nowrap text-xs font-medium"
          />
        </div>
      </div>
    </div>
  </div>
</template>
