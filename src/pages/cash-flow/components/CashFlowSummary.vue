<script setup lang="ts">
import { computed } from "vue";
import Tag from "primevue/tag";

import Lucide from "@/base-components/Lucide";
import Money from "@/base-components/Money.vue";
import type { CashFlowSummaryReport } from "@/api/bcare-types-v2";
import {
  getCashFlowDisplayLabel,
  getCashFlowDisplaySeverity,
  CASH_FLOW_TYPE,
  CASH_FLOW_STATE,
} from "@/api/bcare-enum";

interface Props {
  summary: CashFlowSummaryReport;
  totalTransactions: number;
  pendingCount: number;
  approvedCount: number;
}

const props = defineProps<Props>();

// Main financial statistics
const mainStats = computed(() => {
  // Calculate pending severity once for consistency
  const pendingSeverity = getCashFlowDisplaySeverity(
    CASH_FLOW_TYPE.INCOME,
    CASH_FLOW_STATE.PENDING,
  );

  return [
    {
      title: "Tổng thu",
      value: props.summary.total_income,
      icon: "TrendingUp" as const,
      theme: "success" as const,
      tagLabel: getCashFlowDisplayLabel(CASH_FLOW_TYPE.INCOME, CASH_FLOW_STATE.PAID),
      tagSeverity: getCashFlowDisplaySeverity(CASH_FLOW_TYPE.INCOME, CASH_FLOW_STATE.PAID),
      isMoneyValue: true,
      moneyVariant: "success" as const,
    },
    {
      title: "Tổng chi",
      value: props.summary.total_expense,
      icon: "TrendingDown" as const,
      theme: "danger" as const,
      tagLabel: getCashFlowDisplayLabel(CASH_FLOW_TYPE.EXPENSE, CASH_FLOW_STATE.PAID),
      tagSeverity: getCashFlowDisplaySeverity(CASH_FLOW_TYPE.EXPENSE, CASH_FLOW_STATE.PAID),
      isMoneyValue: true,
      moneyVariant: "danger" as const,
    },
    {
      title: "Dòng tiền ròng",
      value: props.summary.net_amount,
      icon: "BarChart3" as const,
      theme: props.summary.net_amount >= 0 ? ("success" as const) : ("warning" as const),
      tagLabel: props.summary.net_amount >= 0 ? "Tồn quỹ" : "Tồn quỹ",
      tagSeverity: props.summary.net_amount >= 0 ? "success" : "warn",
      isMoneyValue: true,
      moneyVariant: props.summary.net_amount >= 0 ? ("success" as const) : ("warning" as const),
    },
    {
      title: "Chờ phê duyệt",
      value: props.summary.pending_approval_count,
      icon: "Clock" as const,
      theme: "info",
      tagLabel: getCashFlowDisplayLabel(CASH_FLOW_TYPE.INCOME, CASH_FLOW_STATE.PENDING),
      tagSeverity: "info",
      isMoneyValue: false,
      pendingAmount: props.summary.pending_approval_amount,
    },
  ];
});
</script>

<template>
  <div class="space-y-8">
    <!-- Main Financial Statistics -->
    <div>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div
          v-for="(stat, index) in mainStats"
          :key="`main-${index}`"
          :class="[
            'relative animate-[zoomIn_0.5s_ease-out]',
            'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
          ]"
        >
          <div class="box p-4">
            <div class="flex items-center">
              <Lucide :icon="stat.icon" :class="`text-${stat.theme} size-6`" />
              <div class="ml-auto">
                <Tag :value="stat.tagLabel" :severity="stat.tagSeverity" />
              </div>
            </div>
            <div class="mt-4 text-lg font-medium leading-6">
              <Money
                v-if="stat.isMoneyValue"
                :amount="Math.abs(stat.value)"
                :variant="stat.moneyVariant"
                size="medium"
                custom-class="!text-lg !font-medium"
              />
              <span v-else :class="['text-lg font-medium', `text-${stat.tagSeverity}`]">
                {{ stat.value.toLocaleString() }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
