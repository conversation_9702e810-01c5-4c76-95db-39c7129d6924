<template>
  <div class="flex items-center gap-4">
    <!-- Date Range Picker -->
    <div class="flex-1">
      <DatePicker
        v-model="dateRange"
        selection-mode="range"
        date-format="dd/mm/yy"
        placeholder="<PERSON><PERSON>n kho<PERSON>ng thời gian"
        fluid
        class="w-full"
        size="small"
        select-other-months
        @update:model-value="handleDateRangeChange"
      />
    </div>

    <!-- Filter Status Display -->
    <div v-if="currentFilterStatus" class="flex items-center gap-2">
      <Badge :value="currentFilterStatus" class="text-xs" severity="info" />
      <Button
        icon="pi pi-times"
        size="small"
        severity="secondary"
        text
        @click="handleResetFilters"
        aria-label="Xóa bộ lọc"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { useCashFlowFilters } from "@/composables/useCashFlowFilters";

const emit = defineEmits<{
  (e: "filters-changed"): void;
}>();

const {
  dateRange,
  currentFilterStatus,
  resetFilters,
} = useCashFlowFilters();

const handleDateRangeChange = () => {
  emit("filters-changed");
};

const handleResetFilters = () => {
  resetFilters();
  emit("filters-changed");
};

// Watch for external filter changes
watch(
  [dateRange],
  () => {
    emit("filters-changed");
  },
  { deep: true }
);

defineExpose({
  resetFilters,
});
</script>