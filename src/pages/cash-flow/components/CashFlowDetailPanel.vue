<template>
  <div class="rounded-lg border bg-white shadow">
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <Lucide icon="Loader2" class="mr-2 h-6 w-6 animate-spin text-primary" />
      <span class="text-gray-600"><PERSON>ang tải...</span>
    </div>

    <div v-else-if="cashflow" class="flex h-full flex-col">
      <!-- Header -->
      <div class="border-b p-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold text-gray-900">Chi tiết giao dịch #{{ cashflow.id }}</h3>
            <p class="mt-1 text-sm text-gray-500">
              <DateTime :time="cashflow.created_at" show-time />
            </p>
          </div>
          <div class="flex items-center gap-3">
            <!-- State action buttons group -->
            <div class="flex items-center gap-2">
              <Button
                v-if="cashflow.state === 'PENDING' && canChangeState"
                label="Phê duyệt"
                icon="pi pi-check"
                severity="success"
                size="small"
                @click="handleStateChange('APPROVED')"
                :loading="isUpdating"
              />

              <Button
                v-if="cashflow.state === 'PENDING' && canChangeState"
                label="Từ chối"
                icon="pi pi-times"
                severity="danger"
                outlined
                size="small"
                @click="handleStateChange('REJECTED')"
                :loading="isUpdating"
              />

              <Button
                v-if="cashflow.state === 'APPROVED' && canChangeState"
                label="Đã trả"
                icon="pi pi-money-bill"
                severity="success"
                size="small"
                @click="handleStateChange('PAID')"
                :loading="isUpdating"
              />

              <!-- Delete button with permission check -->
              <Button
                v-if="canDelete"
                label="Xóa"
                icon="pi pi-trash"
                severity="danger"
                outlined
                size="small"
                @click="handleDeleteConfirm"
                :loading="isDeleting"
              />
            </div>

            <!-- Divider (only show if there are state action buttons) -->
            <template
              v-if="
                (cashflow.state === 'PENDING' && canChangeState) ||
                (cashflow.state === 'APPROVED' && canChangeState) ||
                canDelete
              "
            >
              <div class="h-6 w-px bg-gray-300"></div>
            </template>

            <!-- State tag -->
            <Tag
              :value="getStateLabel(cashflow.state)"
              :severity="getStateSeverity(cashflow.state)"
            />

            <!-- Divider before basic buttons -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Basic action buttons -->
            <div class="flex items-center gap-2">
              <Button
                v-if="canEdit"
                icon="pi pi-pencil"
                rounded
                severity="secondary"
                text
                @click="$emit('edit-cash-flow', cashflow)"
                v-tooltip="'Chỉnh sửa giao dịch'"
              />
              <Button
                v-if="canPrint"
                icon="pi pi-print"
                rounded
                severity="secondary"
                text
                @click="handlePrint"
                v-tooltip="'In giao dịch'"
              />

              <!-- Note Popover -->
              <CashFlowNotePopover :cash-flow-id="cashflow.id" @note-added="handleNoteAdded">
                <template #trigger="{ togglePopover }">
                  <div class="relative">
                    <Button
                      icon="pi pi-comment"
                      rounded
                      severity="secondary"
                      text
                      @click="togglePopover"
                      v-tooltip="'Ghi chú'"
                    />
                    <span
                      v-if="noteCount > 0"
                      class="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-white"
                    >
                      {{ noteCount > 9 ? "9+" : noteCount }}
                    </span>
                  </div>
                </template>
              </CashFlowNotePopover>
            </div>
          </div>
        </div>
      </div>

      <div class="p-4">
        <!-- Payment Methods Section -->
        <div class="mb-6">
          <h4 class="mb-3 font-medium text-gray-900">Phương thức thanh toán</h4>
          <div class="grid grid-cols-5 gap-4">
            <div v-for="method in paymentMethods" :key="method.key">
              <div class="rounded-lg bg-gray-50 p-3">
                <div class="text-sm text-gray-500">{{ method.label }}</div>
                <div class="mt-1 text-lg font-semibold">
                  <Money :amount="getPaymentMethodAmount(method.key)" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Nội dung giao dịch -->
        <div>
          <h4 class="mb-3 font-medium text-gray-900">Nội dung giao dịch</h4>
          <div class="space-y-2">
            <!-- Header -->
            <div
              class="grid grid-cols-12 gap-4 border-b-2 border-gray-200 pb-3 text-xs font-medium uppercase text-gray-500"
            >
              <div class="col-span-2">Danh mục</div>
              <div class="col-span-2">Thu/Chi</div>
              <div class="col-span-3">Người tạo/Duyệt</div>
              <div v-if="cashflow.type === 'expense'" class="col-span-2">Người chi/Nhận</div>
              <div :class="cashflow.type === 'income' ? 'col-span-5' : 'col-span-3'">Ghi chú</div>
            </div>

            <!-- Content -->
            <div class="grid grid-cols-12 items-start gap-4 py-3 text-sm text-gray-900">
              <!-- Category -->
              <div class="col-span-2">
                <div class="break-words font-medium leading-tight text-primary">
                  {{ cashflow.category?.name || "Không có danh mục" }}
                </div>
              </div>

              <!-- Amount -->
              <div class="col-span-2">
                <div class="flex items-center gap-1">
                  <Money
                    class="font-medium"
                    :amount="cashflow.amount"
                    :variant="cashflow.type === 'income' ? 'success' : 'danger'"
                  />
                </div>
              </div>

              <!-- Creator/Approver Column -->
              <div class="col-span-3">
                <div class="space-y-3">
                  <!-- Creator -->
                  <div v-if="cashflow.creator" class="flex items-start gap-2">
                    <UserAvatar
                      :user="cashflow.creator"
                      size="small"
                      show-name
                      additional-class="flex-1 min-w-0"
                    >
                      <template #subtitle>
                        <div class="flex flex-wrap items-center gap-1 text-xs text-gray-500">
                          <span class="whitespace-nowrap">Tạo:</span>
                          <DateTime
                            :time="cashflow.created_at"
                            :show-icon="false"
                            size="xs"
                            show-time
                          />
                        </div>
                      </template>
                    </UserAvatar>
                  </div>

                  <!-- Approver (when transaction is approved) -->
                  <div
                    v-if="
                      cashflow.approver &&
                      (cashflow.state === 'APPROVED' || cashflow.state === 'PAID')
                    "
                    class="flex items-start gap-2"
                  >
                    <UserAvatar
                      :user="cashflow.approver"
                      size="small"
                      show-name
                      additional-class="flex-1 min-w-0"
                    >
                      <template #subtitle>
                        <div class="flex flex-wrap items-center gap-1 text-xs text-gray-500">
                          <span class="whitespace-nowrap">Duyệt:</span>
                          <DateTime
                            :time="cashflow.updated_at"
                            :show-icon="false"
                            size="xs"
                            show-time
                          />
                        </div>
                      </template>
                    </UserAvatar>
                  </div>
                </div>
              </div>

              <!-- Payer/Recipient Column (only for expense) -->
              <div v-if="cashflow.type === 'expense'" class="col-span-2">
                <div class="space-y-3">
                  <!-- Counterpart (Main transaction party) -->
                  <div v-if="cashflow.counterpart" class="flex items-start gap-2">
                    <UserAvatar
                      :user="cashflow.counterpart"
                      size="small"
                      show-name
                      additional-class="flex-1 min-w-0"
                    >
                      <template #subtitle>
                        <div class="flex flex-wrap items-center gap-1 text-xs text-gray-500">
                          <template v-if="cashflow.paid_at">
                            <span class="whitespace-nowrap">Chi:</span>
                            <DateTime
                              :time="cashflow.paid_at"
                              :show-icon="false"
                              size="xs"
                              show-time
                            />
                          </template>
                          <template v-else>
                            <span class="whitespace-nowrap">Người chi</span>
                          </template>
                        </div>
                      </template>
                    </UserAvatar>
                  </div>

                  <!-- Recipient (always show for expense if exists) -->
                  <div v-if="cashflow.recipient" class="flex items-start gap-2">
                    <UserAvatar
                      :user="cashflow.recipient"
                      size="small"
                      show-name
                      additional-class="flex-1 min-w-0"
                    >
                      <template #subtitle>
                        <div class="flex flex-wrap items-center gap-1 text-xs text-gray-500">
                          <span class="whitespace-nowrap">Người nhận</span>
                        </div>
                      </template>
                    </UserAvatar>
                  </div>
                </div>
              </div>

              <!-- Notes -->
              <div :class="cashflow.type === 'income' ? 'col-span-5' : 'col-span-3'">
                <div class="min-h-[60px]">
                  <RichTextNote
                    v-if="cashflow.description"
                    :content="cashflow.description"
                    :wrapper-mode="true"
                    :max-height="100"
                    class="max-w-full leading-relaxed"
                  />
                  <span v-else class="text-sm italic text-gray-500">Không có mô tả</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="flex items-center justify-center py-8">
      <div class="text-center">
        <Lucide icon="AlertCircle" class="mx-auto h-12 w-12 text-gray-400" />
        <p class="mt-2 text-gray-500">Không tìm thấy giao dịch</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import Button from "primevue/button";
import Tag from "primevue/tag";

import type { CashFlowWithRelations } from "@/api/bcare-types-v2";
import {
  getCashFlowDisplayLabel,
  getCashFlowDisplaySeverity,
  type CashFlowState,
  type CashFlowType,
} from "@/api/bcare-enum";
import Lucide from "@/base-components/Lucide";
import Money from "@/base-components/Money.vue";
import { UserAvatar } from "@/components/User";
import DateTime from "@/base-components/DateTime.vue";
import { RichTextNote } from "@/components/WysiwgEditor";
import useCashflow from "@/hooks/useCashflow";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { usePrint } from "@/composables/usePrint";
import { usePermissions } from "@/composables/usePermissions";
import CashFlowNotePopover from "./CashFlowNotePopover.vue";

interface Props {
  cashflowId: number | null;
}

interface Emits {
  (e: "edit-cash-flow", cashflow: CashFlowWithRelations): void;
  (e: "cash-flow-updated"): void;
  (e: "cash-flow-deleted"): void;
  (e: "note-added"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const {
  currentCashFlow: cashflow,
  getCashFlow,
  updateCashFlowState,
  deleteCashFlow,
  isLoading,
} = useCashflow({ useStore: true });

const { confirm } = useConfirmTippy();
const { printCashFlow } = usePrint();
const { onlyAdmin } = usePermissions();

const isUpdating = ref(false);
const isDeleting = ref(false);

// Payment methods configuration
const paymentMethods = [
  { key: "cash", label: "Tiền mặt" },
  { key: "credit_card", label: "Thẻ tín dụng" },
  { key: "bank", label: "Chuyển khoản" },
  { key: "mpos", label: "mPOS" },
  { key: "momo", label: "MoMo" },
];

// Utility functions
const getStateLabel = (state: string) => {
  if (!cashflow.value) return state;
  const cashFlowState = state as CashFlowState;
  const cashFlowType = cashflow.value.type as CashFlowType;
  return getCashFlowDisplayLabel(cashFlowType, cashFlowState);
};

const getStateSeverity = (state: string) => {
  if (!cashflow.value) return "secondary";
  const cashFlowState = state as CashFlowState;
  const cashFlowType = cashflow.value.type as CashFlowType;
  return getCashFlowDisplaySeverity(cashFlowType, cashFlowState);
};

const getPaymentMethodAmount = (method: string) => {
  if (!cashflow.value) return 0;
  switch (method) {
    case "cash":
      return cashflow.value.cash || 0;
    case "credit_card":
      return cashflow.value.credit_card || 0;
    case "bank":
      return cashflow.value.bank || 0;
    case "mpos":
      return cashflow.value.mpos || 0;
    case "momo":
      return cashflow.value.momo || 0;
    default:
      return 0;
  }
};

// Computed properties
const noteCount = computed(() => {
  return cashflow.value?.notes?.length || 0;
});

const canPrint = computed(() => {
  if (!cashflow.value) return false;

  // For expense vouchers, only allow printing when APPROVED or PAID
  if (cashflow.value.type === "expense") {
    return ["APPROVED", "PAID"].includes(cashflow.value.state);
  }

  // For income vouchers, always allow printing
  return true;
});

// Permission-based computed properties
const canEdit = computed(() => {
  if (!cashflow.value) return false;

  // Admins can edit any transaction in any state
  if (onlyAdmin()) return true;

  // Non-admins can only edit PENDING transactions
  return cashflow.value.state === "PENDING";
});

const canDelete = computed(() => {
  if (!cashflow.value) return false;

  // For PAID transactions (completed transactions), only admins can delete
  if (cashflow.value.state === "PAID") {
    return onlyAdmin();
  }

  // For other states (PENDING, APPROVED, REJECTED), use existing logic
  // Admins can delete any transaction in any state
  if (onlyAdmin()) return true;

  // Non-admins can only delete PENDING transactions
  return cashflow.value.state === "PENDING";
});

const canChangeState = computed(() => {
  if (!cashflow.value) return false;

  // Admins can change state of any transaction
  if (onlyAdmin()) return true;

  // Non-admins can only change state of PENDING transactions (approve/reject)
  // or APPROVED transactions (mark as paid)
  return ["PENDING", "APPROVED"].includes(cashflow.value.state);
});

// Actions
const handleStateChange = async (newState: string) => {
  if (!cashflow.value) return;

  isUpdating.value = true;
  try {
    await updateCashFlowState({
      id: cashflow.value.id,
      state: newState as "APPROVED" | "REJECTED" | "PAID" | "CANCELED",
      comment: `Cập nhật trạng thái thành ${getStateLabel(newState)}`,
    });
    emit("cash-flow-updated");
  } catch (error) {
    console.error("Failed to update cash flow state:", error);
  } finally {
    isUpdating.value = false;
  }
};

const handleDeleteConfirm = (event: MouseEvent) => {
  if (!cashflow.value) return;

  confirm(event, {
    title: "Xác nhận xóa giao dịch",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: () => handleDelete(),
  });
};

const handleDelete = async () => {
  if (!cashflow.value) return;

  isDeleting.value = true;
  try {
    await deleteCashFlow({ id: cashflow.value.id });
    emit("cash-flow-deleted");
  } catch (error) {
    console.error("Failed to delete cash flow:", error);
  } finally {
    isDeleting.value = false;
  }
};

const handlePrint = () => {
  if (!cashflow.value) return;

  printCashFlow(cashflow.value.id, () => {
    console.log("CashFlow printed successfully");
  });
};

const handleNoteAdded = async () => {
  // Refresh the current cash flow to get updated notes
  if (cashflow.value?.id) {
    await getCashFlow(cashflow.value.id);
  }
  emit("note-added");
};

// Load cash flow data when ID changes
watch(
  () => props.cashflowId,
  async (newId) => {
    if (newId) {
      await getCashFlow(newId);
    }
  },
  { immediate: true },
);
</script>
