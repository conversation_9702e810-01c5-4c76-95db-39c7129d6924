<template>
  <Menubar :model="menuItems" class="px-1 py-1">
    <template #end>
      <div class="ml-auto">
        <slot name="filters" />
      </div>
    </template>
  </Menubar>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useCashFlowFilters } from "@/composables/useCashFlowFilters";
import { useCashFlowExport } from "@/composables/useCashFlowExport";

const emit = defineEmits<{
  (e: "add-cashflow"): void;
  (e: "refresh-data"): void;
  (e: "filter-update", type: "income" | "expense" | null, state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null): void;
}>();

const { buildFilters, dateRange, updateTypeFilter, updateStateFilter } = useCashFlowFilters();
const { handleExportReport, handlePrintReport, isExporting } = useCashFlowExport();

const handleFilterUpdate = (type: "income" | "expense" | null, state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null) => {
  updateTypeFilter(type);
  if (state !== undefined) {
    updateStateFilter(state);
  }
  emit("filter-update", type, state);
};

const handleExport = async () => {
  try {
    const filters = buildFilters();
    await handleExportReport(filters);
  } catch (error) {
    console.error("Export failed:", error);
  }
};

const handlePrint = () => {
  handlePrintReport(dateRange.value);
};

const menuItems = computed(() => [
  {
    label: "Tạo giao dịch",
    icon: "pi pi-plus",
    command: () => emit("add-cashflow"),
  },
  {
    label: "Làm mới",
    icon: "pi pi-refresh",
    command: () => emit("refresh-data"),
  },
  {
    label: "Xuất báo cáo",
    icon: "pi pi-download",
    disabled: isExporting.value,
    items: [
      {
        label: "Tải báo cáo Excel",
        icon: "pi pi-file-excel",
        command: () => handleExport(),
        disabled: isExporting.value,
      },
      {
        label: "In báo cáo",
        icon: "pi pi-print",
        command: () => handlePrint(),
      },
    ],
  },
  {
    separator: true,
  },
  {
    label: "Bộ lọc",
    icon: "pi pi-filter",
    items: [
      {
        label: "Tất cả",
        command: () => handleFilterUpdate(null),
      },
      {
        label: "Thu nhập",
        command: () => handleFilterUpdate("income"),
      },
      {
        label: "Chi phí",
        command: () => handleFilterUpdate("expense"),
      },
      {
        separator: true,
      },
      {
        label: "Chờ phê duyệt",
        command: () => handleFilterUpdate(null, "PENDING"),
      },
      {
        label: "Đã phê duyệt",
        command: () => handleFilterUpdate(null, "APPROVED"),
      },
      {
        label: "Đã thanh toán",
        command: () => handleFilterUpdate(null, "PAID"),
      },
    ],
  },
]);
</script>