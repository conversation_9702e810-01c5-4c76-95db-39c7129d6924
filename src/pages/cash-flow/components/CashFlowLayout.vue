<template>
  <div class="flex h-full flex-col">
    <!-- Header Section -->
    <div class="p-4 md:p-6">
      <CashFlowMenuBar
        @add-cashflow="$emit('add-cashflow')"
        @refresh-data="$emit('refresh-data')"
        @filter-update="handleFilterUpdate"
      >
        <template #filters>
          <CashFlowFilters @filters-changed="$emit('filters-changed')" />
        </template>
      </CashFlowMenuBar>
    </div>

    <!-- Summary Section -->
    <div class="px-4 pb-6 md:px-6">
      <slot name="summary" />
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-hidden px-4 md:px-6">
      <div class="grid grid-cols-12 gap-4">
        <!-- Left Panel -->
        <div class="col-span-12 md:col-span-3">
          <slot name="left-panel" />
        </div>

        <!-- Right Panel -->
        <div class="col-span-12 md:col-span-9">
          <slot name="right-panel" />
        </div>
      </div>
    </div>

    <!-- Payment Method Popover -->
    <CashFlowPaymentMethodPopover ref="paymentMethodPopoverRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import CashFlowMenuBar from "./CashFlowMenuBar.vue";
import CashFlowFilters from "./CashFlowFilters.vue";
import CashFlowPaymentMethodPopover from "./CashFlowPaymentMethodPopover.vue";
import type { CashFlowSummaryReport } from "@/api/bcare-types-v2";

const emit = defineEmits<{
  (e: "add-cashflow"): void;
  (e: "refresh-data"): void;
  (e: "filters-changed"): void;
  (e: "filter-update", type: "income" | "expense" | null, state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null): void;
}>();

const paymentMethodPopoverRef = ref();

const handleFilterUpdate = (type: "income" | "expense" | null, state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null) => {
  emit("filter-update", type, state);
};

const showPaymentMethodPopover = (
  type: "income" | "expense" | "net",
  summary: CashFlowSummaryReport,
  event: Event
) => {
  paymentMethodPopoverRef.value?.show(type, summary, event);
};

defineExpose({
  showPaymentMethodPopover,
});
</script>