<template>
  <Popover ref="popoverRef">
    <div v-if="popoverData" class="w-80 max-w-sm p-2">
      <!-- Summary totals -->
      <div class="mb-6">
        <div class="flex flex-col gap-3">
          <div
            v-for="item in popoverData.summaryTotals"
            :key="item.label"
            class="flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0"
          >
            <span class="text-sm font-medium text-gray-700">{{ item.label }}</span>
            <Money
              :amount="Math.abs(item.value)"
              :variant="item.isPositive ? 'success' : 'danger'"
              size="small"
              custom-class="!text-sm !font-semibold"
            />
          </div>
        </div>
      </div>

      <!-- Payment methods -->
      <div>
        <h4 class="mb-4 flex items-center gap-2 text-sm font-medium text-gray-600">
          <Lucide icon="CreditCard" class="h-4 w-4" />
          <PERSON><PERSON><PERSON><PERSON> thức thanh toán
        </h4>
        <div class="flex flex-col gap-2">
          <div
            v-for="method in popoverData.paymentMethods"
            :key="method.label"
            class="flex items-center justify-between rounded-lg bg-gray-50 px-3 py-3 transition-colors hover:bg-gray-100"
          >
            <div class="flex items-center gap-3">
              <div class="h-2 w-2 rounded-full" :class="getPaymentMethodColor(method.label)"></div>
              <span class="text-sm font-medium text-gray-700">{{ method.label }}</span>
            </div>
            <Money
              :amount="Math.abs(method.value)"
              variant="default"
              size="small"
              custom-class="!text-sm !font-medium"
            />
          </div>
        </div>
      </div>
    </div>
  </Popover>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import Lucide from "@/base-components/Lucide";
import Money from "@/base-components/Money.vue";
import type { CashFlowSummaryReport } from "@/api/bcare-types-v2";

interface PopoverData {
  title: string;
  summaryTotals: Array<{ label: string; value: number; isPositive: boolean }>;
  paymentMethods: Array<{ label: string; value: number }>;
}

const popoverRef = ref();
const popoverData = ref<PopoverData | null>(null);

const getPaymentMethodColor = (methodLabel: string) => {
  const colorMap: Record<string, string> = {
    "Tiền mặt": "bg-green-500",
    "Thẻ tín dụng": "bg-blue-500",
    mPOS: "bg-purple-500",
    "Chuyển khoản": "bg-orange-500",
    MoMo: "bg-pink-500",
  };
  return colorMap[methodLabel] || "bg-gray-500";
};

const show = (type: "income" | "expense" | "net", summary: CashFlowSummaryReport, event: Event) => {
  const paymentMethodData = [
    {
      label: "Tiền mặt",
      value: summary.total_cash_all,
    },
    {
      label: "Thẻ tín dụng",
      value: summary.total_credit_card_all,
    },
    {
      label: "mPOS",
      value: summary.total_mpos_all,
    },
    {
      label: "Chuyển khoản",
      value: summary.total_bank_all,
    },
    {
      label: "MoMo",
      value: summary.total_momo_all,
    },
  ];

  let title = "";
  let summaryTotals = [];

  switch (type) {
    case "income":
      title = "Chi tiết tổng thu";
      summaryTotals = [
        {
          label: "Tổng thu",
          value: summary.total_income,
          isPositive: true,
        },
      ];
      break;
    case "expense":
      title = "Chi tiết tổng chi";
      summaryTotals = [
        {
          label: "Tổng chi",
          value: summary.total_expense,
          isPositive: false,
        },
      ];
      break;
    case "net":
      title = "Chi tiết tồn quỹ";
      summaryTotals = [
        {
          label: "Tổng thu",
          value: summary.total_income,
          isPositive: true,
        },
        {
          label: "Tổng chi",
          value: summary.total_expense,
          isPositive: false,
        },
        {
          label: "Tồn quỹ",
          value: summary.net_amount,
          isPositive: summary.net_amount >= 0,
        },
      ];
      break;
  }

  popoverData.value = {
    title,
    summaryTotals,
    paymentMethods: paymentMethodData,
  };

  popoverRef.value.toggle(event);
};

const hide = () => {
  popoverRef.value.hide();
};

defineExpose({
  show,
  hide,
});
</script>