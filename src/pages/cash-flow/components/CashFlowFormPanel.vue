<template>
  <div class="rounded-lg border bg-white shadow">
    <!-- Header -->
    <div class="border-b p-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">
            {{ isEdit ? "Chỉnh sửa giao dịch" : "Tạo giao dịch mới" }}
          </h3>
          <p class="mt-1 text-sm text-gray-500">
            Ngày tạo <DateTime :time="new Date().toISOString()" show-time />
          </p>
        </div>
        <div class="flex items-center gap-2">
          <Button label="Hủy" severity="secondary" outlined text @click="handleCancel" />
          <Button
            label="Clear"
            outlined
            severity="info"
            :disabled="isFormDisabled"
            @click="handleClear"
          />
          <Button
            :label="isEdit ? 'Cập nhật' : 'Lưu'"
            severity="primary"
            @click="handleSubmit"
            :loading="isSubmitting"
            :disabled="!isFormValid || isFormDisabled"
          >
            <template #icon>
              <Lucide icon="Save" class="h-4 w-4" />
            </template>
          </Button>
        </div>
      </div>
    </div>

    <!-- Form Content -->
    <div class="space-y-6 p-4">
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Type Selection -->
        <FormField label="Loại giao dịch *">
          <div class="flex gap-3">
            <div
              :class="[
                'flex items-center gap-2 rounded-lg border px-4 py-2 transition-all',
                isFormDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                formData.type === 'income'
                  ? 'border-success bg-success/10 text-success shadow-sm'
                  : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50',
              ]"
              @click="!isFormDisabled && (formData.type = 'income')"
            >
              <Lucide icon="Plus" class="h-4 w-4" />
              <span class="text-sm font-medium">{{ CASH_FLOW_TYPE_LABELS.income }}</span>
            </div>
            <div
              :class="[
                'flex items-center gap-2 rounded-lg border px-4 py-2 transition-all',
                isFormDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                formData.type === 'expense'
                  ? 'border-danger bg-danger/10 text-danger shadow-sm'
                  : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50',
              ]"
              @click="!isFormDisabled && (formData.type = 'expense')"
            >
              <Lucide icon="Minus" class="h-4 w-4" />
              <span class="text-sm font-medium">{{ CASH_FLOW_TYPE_LABELS.expense }}</span>
            </div>
          </div>
          <small v-if="errors.type" class="text-xs text-red-500">{{ errors.type }}</small>
        </FormField>

        <!-- Payment Methods Section -->
        <FormField label="Phương thức thanh toán">
          <div class="flex flex-wrap gap-4">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              class="min-w-[calc((100%/2)-0.5rem)] flex-1 rounded-lg bg-gray-50 p-3 sm:min-w-[calc((100%/3)-0.75rem)] lg:min-w-[calc((100%/5)-0.8rem)]"
            >
              <div class="flex items-center justify-between text-sm text-gray-500">
                <span>{{ method.label }}</span>
                <Button
                  v-tooltip.bottom="method.tooltip"
                  class="p-1 transition-transform hover:scale-110"
                  text
                  @click="handlePayAll(method.id)"
                >
                  <Lucide :icon="method.icon as any" class="h-4 w-4" />
                </Button>
              </div>
              <div class="mt-1">
                <InputNumber
                  :modelValue="getPaymentMethodValue(method.id)"
                  @update:modelValue="(value) => handlePaymentMethodChange(method.id, value ?? 0)"
                  :min="0"
                  mode="currency"
                  currency="VND"
                  locale="vi-VN"
                  fluid
                />
              </div>
            </div>
          </div>
        </FormField>

        <!-- Category -->
        <FormField label="Danh mục">
          <Select
            v-model="formData.category_id"
            :options="categoryOptions"
            optionLabel="name"
            optionValue="id"
            :disabled="isFormDisabled"
            fluid
            placeholder="Chọn danh mục"
            filter
            showClear
            :loading="isLoadingCategories"
            class="flex h-10 items-center justify-center"
          />
        </FormField>

        <!-- User Information -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <!-- Partner (Đối tác) -->
          <FormField :label="counterpartLabel">
            <div
              :class="[
                'p-inputtext p-component flex !h-10 items-center justify-center px-3 py-0 hover:border-primary/50',
                isFormDisabled ? 'opacity-50 pointer-events-none' : ''
              ]"
            >
              <UserAssignPromax
                v-model="selectedCounterpart"
                :use-prime-vue-input="true"
                class="w-full"
                :placeholder="`Chọn ${counterpartLabel.toLowerCase()}`"
                global-unique-name="cash_flow_counterpart"
                @update:model-value="onCounterpartSelect"
              />
            </div>
          </FormField>

          <!-- Recipient (Người nhận) -->
          <FormField label="Người nhận">
            <div
              :class="[
                'p-inputtext p-component flex !h-10 items-center justify-center px-3 py-0 hover:border-primary/50',
                isFormDisabled ? 'opacity-50 pointer-events-none' : ''
              ]"
            >
              <UserAssignPromax
                v-model="selectedRecipient"
                :use-prime-vue-input="true"
                class="w-full"
                placeholder="Chọn người nhận"
                global-unique-name="cash_flow_recipient"
                @update:model-value="onRecipientSelect"
              />
            </div>
          </FormField>
        </div>

        <!-- Description -->
        <FormField label="Ghi chú">
          <NoteEditor
            v-model="formData.description"
            :always-editing="true"
            size="xs"
            placeholder="Nhập mô tả chi tiết cho giao dịch..."
          />
        </FormField>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";

import type { CashFlowWithRelations } from "@/api/bcare-types-v2";
import { CASH_FLOW_TYPE_LABELS } from "@/api/bcare-enum";
import DateTime from "@/base-components/DateTime.vue";
import Lucide from "@/base-components/Lucide";
import FormField from "@/components/Form/FormField.vue";
import UserAssignPromax from "@/components/User/UserAssignPromax.vue";
import NoteEditor from "@/components/WysiwgEditor/NoteEditor.vue";
import useCashflow from "@/hooks/useCashflow";
import useTerm from "@/hooks/useTerm";
import { useModifiedFields } from "@/composables/useModifiedFields";
import { usePermissions } from "@/composables/usePermissions";

interface Props {
  cashflow?: CashFlowWithRelations | null;
}

interface Emits {
  (e: "cash-flow-saved"): void;
  (e: "cancel"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { addCashFlow, updateCashFlow } = useCashflow({ useStore: true });
const { getBundleTerms } = useTerm({ autoLoad: true });
const { onlyAdmin } = usePermissions();

// Payment method types
type PaymentMethodId = "cash" | "credit_card" | "bank" | "mpos" | "momo";

interface PaymentMethod {
  id: PaymentMethodId;
  label: string;
  icon: string;
  tooltip: string;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "cash",
    label: "Tiền mặt",
    icon: "Wallet",
    tooltip: "Thanh toán tiền mặt",
  },
  {
    id: "credit_card",
    label: "Thẻ tín dụng",
    icon: "CreditCard",
    tooltip: "Thanh toán qua thẻ tín dụng",
  },
  {
    id: "bank",
    label: "Chuyển khoản",
    icon: "Send",
    tooltip: "Thanh toán qua chuyển khoản",
  },
  {
    id: "mpos",
    label: "mPOS",
    icon: "Smartphone",
    tooltip: "Thanh toán qua mPOS",
  },
  {
    id: "momo",
    label: "MoMo",
    icon: "Smartphone",
    tooltip: "Thanh toán qua MoMo",
  },
];

// Form state
const formData = reactive({
  type: "expense" as "income" | "expense",
  amount: 0,
  description: "",
  category_id: null as number | null,
  counterpart_id: null as number | null,
  recipient_id: null as number | null,
  // Payment methods
  cash: 0,
  credit_card: 0,
  bank: 0,
  mpos: 0,
  momo: 0,
});

// Track modified fields
const { modified, track, reset } = useModifiedFields(formData, [
  "amount",
  "cash",
  "credit_card",
  "mpos",
  "bank",
  "momo",
  "description",
  "category_id",
  "counterpart_id",
  "recipient_id",
]);

const errors = reactive({
  type: "",
  amount: "",
});

const isSubmitting = ref(false);
const isLoadingCategories = ref(false);

// Categories - dynamic based on transaction type
const categoryOptions = computed(() => {
  try {
    const bundle = formData.type === "income" ? "danh_muc_thu" : "danh_muc_chi";
    return getBundleTerms(bundle as any);
  } catch {
    return [];
  }
});

// User selections - UserAssignPromax expects number | number[] | undefined
const selectedCounterpart = ref<number | undefined>(undefined);
const selectedRecipient = ref<number | undefined>(undefined);

// Computed
const isEdit = computed(() => !!props.cashflow);

const counterpartLabel = computed(() => {
  return formData.type === "income" ? "Người thu" : "Người chi";
});

const totalPaymentAmount = computed(() => {
  return formData.cash + formData.credit_card + formData.bank + formData.mpos + formData.momo;
});

const isFormValid = computed(() => {
  return formData.type && (formData.amount > 0 || totalPaymentAmount.value > 0);
});

// Permission-based computed properties
const canEditForm = computed(() => {
  // For new cash flows, always allow editing
  if (!isEdit.value) return true;

  // For existing cash flows, check admin permissions and state
  if (onlyAdmin()) return true;

  // Non-admins can only edit PENDING transactions
  return props.cashflow?.state === 'PENDING';
});

const isFormDisabled = computed(() => {
  return !canEditForm.value;
});

// Payment method handlers
const getPaymentMethodValue = (methodId: PaymentMethodId): number => {
  return formData[methodId];
};

const handlePaymentMethodChange = (methodId: PaymentMethodId, value: number) => {
  formData[methodId] = Math.abs(value);
  // Auto-update total amount if payment methods are used
  if (totalPaymentAmount.value > 0) {
    formData.amount = totalPaymentAmount.value;
  }
};

const handlePayAll = (methodId: PaymentMethodId) => {
  const totalAmount = formData.amount || totalPaymentAmount.value;

  // Clear all methods first
  formData.cash = 0;
  formData.credit_card = 0;
  formData.bank = 0;
  formData.mpos = 0;
  formData.momo = 0;

  // Set the selected method to total amount
  formData[methodId] = totalAmount;
};

const handleClear = () => {
  formData.cash = 0;
  formData.credit_card = 0;
  formData.bank = 0;
  formData.mpos = 0;
  formData.momo = 0;
  formData.amount = 0;
};

// Methods
const onCounterpartSelect = (userId: number | number[] | undefined) => {
  if (typeof userId === "number") {
    selectedCounterpart.value = userId;
    formData.counterpart_id = userId;
  } else {
    selectedCounterpart.value = undefined;
    formData.counterpart_id = null;
  }
};

const onRecipientSelect = (userId: number | number[] | undefined) => {
  if (typeof userId === "number") {
    selectedRecipient.value = userId;
    formData.recipient_id = userId;
  } else {
    selectedRecipient.value = undefined;
    formData.recipient_id = null;
  }
};

const validateForm = () => {
  // Reset errors
  errors.type = "";
  errors.amount = "";

  let isValid = true;

  if (!formData.type) {
    errors.type = "Vui lòng chọn loại giao dịch";
    isValid = false;
  }

  const finalAmount = totalPaymentAmount.value || formData.amount;
  if (!finalAmount || finalAmount <= 0) {
    errors.amount = "Số tiền phải lớn hơn 0";
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  isSubmitting.value = true;
  try {
    const finalAmount = totalPaymentAmount.value || formData.amount;

    if (isEdit.value && props.cashflow) {
      // Update existing cash flow
      const updatePayload = {
        id: props.cashflow.id,
        amount: finalAmount,
        description: formData.description,
        category_id: formData.category_id!,
        counterpart_id: formData.counterpart_id || undefined,
        recipient_id: formData.recipient_id || undefined,
        cash: formData.cash,
        credit_card: formData.credit_card,
        mpos: formData.mpos,
        bank: formData.bank,
        momo: formData.momo,
        modified: modified.value.length > 0 ? modified.value : ["amount"],
      };

      await updateCashFlow(updatePayload);
    } else {
      // Create new cash flow
      const createPayload: any = {
        type: formData.type,
        amount: finalAmount,
        description: formData.description,
        category_id: formData.category_id!,
        cash: formData.cash,
        credit_card: formData.credit_card,
        mpos: formData.mpos,
        bank: formData.bank,
        momo: formData.momo,
      };

      // Add optional fields only if they have values
      if (formData.counterpart_id) {
        createPayload.counterpart_id = formData.counterpart_id;
      }
      if (formData.recipient_id) {
        createPayload.recipient_id = formData.recipient_id;
      }

      await addCashFlow(createPayload);
    }

    emit("cash-flow-saved");
    resetForm();
  } catch (error) {
    console.error("Failed to save cash flow:", error);
  } finally {
    isSubmitting.value = false;
  }
};

const handleCancel = () => {
  emit("cancel");
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    type: "expense",
    amount: 0,
    description: "",
    category_id: null,
    counterpart_id: null,
    recipient_id: null,
    cash: 0,
    credit_card: 0,
    bank: 0,
    mpos: 0,
    momo: 0,
  });

  selectedCounterpart.value = undefined;
  selectedRecipient.value = undefined;

  errors.type = "";
  errors.amount = "";

  // Reset modified fields tracking
  reset();
};

// Reset category when transaction type changes
watch(
  () => formData.type,
  () => {
    // Reset category when type changes since different types have different category options
    formData.category_id = null;
  },
);

// Load form data when editing
watch(
  () => props.cashflow,
  (cashflow) => {
    if (cashflow && isEdit.value) {
      Object.assign(formData, {
        type: cashflow.type,
        amount: cashflow.amount,
        description: cashflow.description,
        category_id: cashflow.category_id,
        counterpart_id: cashflow.counterpart_id,
        recipient_id: cashflow.recipient_id,
        // Load payment method values from API
        cash: cashflow.cash || 0,
        credit_card: cashflow.credit_card || 0,
        bank: cashflow.bank || 0,
        mpos: cashflow.mpos || 0,
        momo: cashflow.momo || 0,
      });

      selectedCounterpart.value = cashflow.counterpart?.id || undefined;
      selectedRecipient.value = cashflow.recipient?.id || undefined;

      // Start tracking changes after loading data
      reset();
      track();
    } else {
      resetForm();
      track();
    }
  },
  { immediate: true },
);
</script>
