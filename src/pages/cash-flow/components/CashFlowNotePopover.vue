<template>
  <div class="inline-flex">
    <!-- Scoped Slot for Button/Icon -->
    <slot name="trigger" :togglePopover="togglePopover">
      <!-- Note Indicator Style Button -->
      <div v-if="noteCount && noteCount > 0" class="flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 cursor-pointer hover:bg-blue-200 transition-colors" @click.stop="togglePopover" v-tooltip.top="'Ghi chú'">
        <i class="pi pi-comment text-xs text-blue-600" />
        <span class="text-xs font-medium text-blue-600">{{ noteCount }}</span>
      </div>
      <!-- Default Icon when no notes -->
      <i
        v-else
        class="pi pi-comment cursor-pointer text-surface-500 transition-colors duration-150 hover:text-surface-700"
        @click.stop="togglePopover"
        v-tooltip.top="'Ghi chú'"
        aria-label="Notes"
      />
    </slot>

    <!-- Popover -->
    <Popover
      ref="popoverRef"
      :pt="{
        content: {
          class: 'p-0 surface-0',
        },
      }"
      @show="handlePopoverShow"
      @hide="handlePopoverHide"
    >
      <div class="flex max-h-[28rem] w-96 flex-col">
        <!-- Header -->
        <div class="flex items-center justify-between border-b border-surface-200 p-3">
          <div class="flex items-center gap-2">
            <h3 class="text-sm font-medium text-surface-800">Ghi chú giao dịch</h3>
            <span v-if="notes.length > 0" class="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-white">
              {{ notes.length }}
            </span>
          </div>
          <span class="text-xs text-surface-500">#{{ cashFlowId }}</span>
        </div>

        <!-- Note History -->
        <div class="flex-1 overflow-y-auto">
          <div v-if="isLoading" class="flex items-center justify-center py-8">
            <Lucide icon="Loader2" class="mr-2 h-4 w-4 animate-spin text-primary" />
            <span class="text-sm text-surface-600">Đang tải...</span>
          </div>

          <div v-else-if="notes.length > 0" class="space-y-3 p-3">
            <div v-for="note in notes" :key="note.id" class="flex gap-3">
              <UserAvatar
                :user="{
                  ...note.creator,
                  id: note.user_id,
                  username: note.creator.username,
                  name: note.creator.name,
                }"
                class="h-8 w-8 flex-shrink-0"
                show-name
              >
                <template #subtitle>
                  <span class="text-xs text-surface-500">
                    {{ formatRelativeTime(note.created_at) }}
                  </span>
                </template>
              </UserAvatar>
              <div class="min-w-0 flex-1">
                <div class="max-w-full">
                  <div
                    class="inline-block max-w-full rounded-3xl rounded-tl-none border border-surface-200 bg-white px-3.5 py-2"
                  >
                    <RichTextNote
                      :content="note.body"
                      :wrapper-mode="false"
                      size="sm"
                      class="max-w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex items-center justify-center py-8 text-center">
            <div>
              <div class="mb-2 text-4xl text-surface-300">
                <i class="pi pi-comments"></i>
              </div>
              <p class="text-sm text-surface-500">Chưa có ghi chú nào!</p>
            </div>
          </div>
        </div>

        <!-- Note Editor Footer -->
        <div class="border-t border-surface-200 p-3">
          <NoteEditor
            v-model="newNoteContent"
            placeholder="Thêm ghi chú..."
            size="sm"
            always-editing
            @save="handleAddNote"
            class="w-full"
          />
          <div class="mt-2 flex justify-end gap-2">
            <Button label="Hủy" text size="small" @click="cancelNote" :disabled="isSubmitting" />
            <Button
              label="Gửi"
              size="small"
              @click="handleAddNote"
              :loading="isSubmitting"
              :disabled="!newNoteContent.trim()"
            />
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import Button from "primevue/button";
import Popover from "primevue/popover";

import type { CashFlowNoteResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { UserAvatar } from "@/components/User";
import { NoteEditor, RichTextNote } from "@/components/WysiwgEditor";
import useCashflow from "@/hooks/useCashflow";
import { formatRelativeTime } from "@/utils/time-helper";

interface Props {
  cashFlowId: number;
  noteCount?: number;
}

interface Emits {
  (e: "note-added"): void;
  (e: "note-updated"): void;
  (e: "note-deleted"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Composables
const { fetchCashFlowNotes, addCashFlowNote } = useCashflow({ useStore: false });

// Refs
const popoverRef = ref();
const notes = ref<CashFlowNoteResponse[]>([]);
const newNoteContent = ref("");
const isLoading = ref(false);
const isSubmitting = ref(false);

// Methods
const togglePopover = (event: Event) => {
  popoverRef.value.toggle(event);
};

const handlePopoverShow = async () => {
  await loadNotes();
};

const handlePopoverHide = () => {
  // Reset form when popover closes
  newNoteContent.value = "";
};

const loadNotes = async () => {
  if (!props.cashFlowId) return;

  isLoading.value = true;
  try {
    const response = await fetchCashFlowNotes({
      filter: { cashflow_id: props.cashFlowId },
      page: 1,
      page_size: 50,
      order_by: "created_at ASC",
    });

    if (response.data) {
      notes.value = response.data.notes;
    }
  } catch (error) {
    console.error("Failed to load notes:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleAddNote = async () => {
  if (!newNoteContent.value.trim() || !props.cashFlowId) return;

  isSubmitting.value = true;
  try {
    const response = await addCashFlowNote({
      cashflow_id: props.cashFlowId,
      body: newNoteContent.value.trim(),
    });

    if (response.data) {
      // Reload notes to get the latest data
      await loadNotes();
      newNoteContent.value = "";
      emit("note-added");
    }
  } catch (error) {
    console.error("Failed to add note:", error);
  } finally {
    isSubmitting.value = false;
  }
};

const cancelNote = () => {
  newNoteContent.value = "";
};

// Watch for cashFlowId changes
watch(
  () => props.cashFlowId,
  () => {
    notes.value = [];
  },
  { immediate: true },
);
</script>
