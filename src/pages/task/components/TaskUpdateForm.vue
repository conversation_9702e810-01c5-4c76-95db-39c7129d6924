<template>
  <Drawer
    v-model:visible="formStore.isVisible"
    :style="{ width: formStore.isExpanded ? '70vw' : '35vw' }"
    @hide="handleClose"
    position="right"
    blockScroll
    :showCloseIcon="false"
    class="flex flex-col"
  >
    <template #container>
      <!-- Header -->
      <div
        v-if="formStore.taskData"
        class="flex w-full items-center justify-between gap-4 border-b border-gray-200 p-3"
      >
        <div class="flex-1">
          <InputText
            v-model="formStore.formData.title"
            class="w-full border-0 border-b-2 border-dotted border-gray-300 rounded-none pb-0 px-0 text-md shadow-none font-semibold focus:border-blue-500 focus:border-solid focus:ring-0"
            placeholder="Nhập tiêu đề công việc"
            :disabled="!formStore.canEditTaskDetails"
            tabindex="-1"
          />
        </div>
        <div class="flex items-center gap-2">
          <StateSelectBtn
            v-model:state="formStore.formData.state"
            @update:state="handleStateChange"
            editable
            :taskOwnerId="formStore.formData.creator_id"
            :primaryAssigneeIds="taskStore.getPrimaryUserIdsForTask(formStore.taskData)"
          />
        </div>
      </div>

      <!-- Expand/Collapse Button -->
      <Button
        v-if="formStore.taskData && !formStore.isLoading"
        :icon="formStore.isExpanded ? 'pi pi-chevron-right' : 'pi pi-chevron-left'"
        v-tooltip.right="formStore.isExpanded ? 'Thu gọn' : 'Mở rộng'"
        @click="formStore.toggleExpanded"
        class="absolute left-0 top-[58px] -translate-y-1/2 -translate-x-1/2 z-10 h-8 w-8 rounded-full bg-white border border-gray-200 text-gray-600 hover:text-blue-600 hover:border-blue-300"
        text
      />

      <!-- Main content -->
      <div class="flex flex-grow overflow-hidden">
        <!-- Loading state -->
        <div v-if="formStore.isLoading" class="flex flex-grow items-center justify-center">
          <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        </div>

        <!-- Content when task data is available -->
        <div v-else-if="formStore.taskData" class="flex flex-grow overflow-hidden">
          <!-- Expanded Mode: Two Column Layout -->
          <div v-if="formStore.isExpanded" class="flex flex-grow overflow-hidden">
            <!-- Left Column: Task Details -->
            <div class="flex-1 overflow-y-auto p-4 pt-2 border-r border-gray-200">
              <div class="space-y-3">
                <div v-if="formStore.errors.title" class="text-red-500 text-sm">
                  {{ formStore.errors.title }}
                </div>

                <FormField
                  label="Người nhận việc"
                  icon="pi pi-user-plus"
                  size="sm"
                  :readonly="!formStore.canEditTaskDetails"
                >
                  <UserMultiAssign v-model="formStore.primary" usePrimeVueInput only-current-department />
                  <small class="text-red-500" v-if="formStore.errors.primary">
                    {{ formStore.errors.primary }}
                  </small>
                </FormField>

                <FormField label="Khách hàng" icon="pi pi-user" size="sm" :readonly="!formStore.canEditTaskDetails">
                  <SearchPeople v-model="formStore.formData.person_id" placeholder="Tìm kiếm khách hàng" />
                </FormField>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Loại công việc"
                    icon="pi pi-tag"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <TaskTypeSelect v-model="formStore.formData.type" />
                  </FormField>

                  <FormField
                    label="Độ ưu tiên"
                    icon="pi pi-flag"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <PrioritySelect v-model:modelValue="formStore.formData.priority" />
                  </FormField>
                </div>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Ngày bắt đầu"
                    icon="pi pi-calendar-plus"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <DatePicker
                      v-model="startDate"
                      showTime
                      hourFormat="24"
                      placeholder="Bắt đầu"
                      date-format="dd/mm/yy"
                      size="small"
                      fluid
                      :minDate="new Date()"
                      @update:model-value="handleStartDateUpdate"
                    />
                  </FormField>
                  <FormField
                    label="Ngày kết thúc"
                    icon="pi pi-calendar-times"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <DatePicker
                      v-model="dueDate"
                      showTime
                      hourFormat="24"
                      placeholder="Kết thúc"
                      date-format="dd/mm/yy"
                      size="small"
                      fluid
                      :minDate="new Date()"
                      @update:model-value="handleDueDateUpdate"
                    />
                    <small class="text-red-500" v-if="formStore.errors.due_date">
                      {{ formStore.errors.due_date }}
                    </small>
                  </FormField>
                </div>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Người liên quan"
                    icon="pi pi-users"
                    class="flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <UserMultiAssign :max-display="4" v-model="formStore.contributor" usePrimeVueInput />
                  </FormField>

                  <FormField
                    label="Người duyệt"
                    icon="pi pi-user-edit"
                    class="flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <UserMultiAssign :max-display="4" v-model="formStore.reviewer" usePrimeVueInput />
                  </FormField>
                </div>

                <FormField label="Nội dung công việc" icon="pi pi-clipboard" size="sm">
                  <NoteEditor v-model="formStore.formData.note" placeholder="Thêm nội dung" />
                </FormField>
              </div>
            </div>

            <!-- Right Column: Task Notes -->
            <div class="flex-1 overflow-hidden">
              <TaskNoteForm
                :task-id="formStore.taskData?.id"
                :history="formStore.formData.history"
                :creator-id="formStore.taskData?.creator_id"
                :created-at="formStore.taskData?.created_at"
              />
            </div>
          </div>

          <!-- Normal Mode: Tab-based Layout -->
          <div v-else class="flex flex-grow overflow-hidden">
            <!-- Left content area -->
            <div class="flex-grow overflow-y-auto" :class="{ 'p-4 pt-2': formStore.activeMenuItem !== 'notes' }">
              <!-- Task Details -->
              <div v-if="formStore.activeMenuItem === 'details'" class="space-y-3">
                <div v-if="formStore.errors.title" class="text-red-500 text-sm">
                  {{ formStore.errors.title }}
                </div>

                <FormField
                  label="Người nhận việc"
                  icon="pi pi-user-plus"
                  size="sm"
                  :readonly="!formStore.canEditTaskDetails"
                >
                  <UserMultiAssign v-model="formStore.primary" usePrimeVueInput only-current-department />
                  <small class="text-red-500" v-if="formStore.errors.primary">
                    {{ formStore.errors.primary }}
                  </small>
                </FormField>

                <FormField label="Khách hàng" icon="pi pi-user" size="sm" :readonly="!formStore.canEditTaskDetails">
                  <SearchPeople v-model="formStore.formData.person_id" placeholder="Tìm kiếm khách hàng" />
                </FormField>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Loại công việc"
                    icon="pi pi-tag"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <TaskTypeSelect v-model="formStore.formData.type" />
                  </FormField>

                  <FormField
                    label="Độ ưu tiên"
                    icon="pi pi-flag"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <PrioritySelect v-model:modelValue="formStore.formData.priority" />
                  </FormField>
                </div>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Ngày bắt đầu"
                    icon="pi pi-calendar-plus"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <DatePicker
                      v-model="startDate"
                      showTime
                      hourFormat="24"
                      placeholder="Bắt đầu"
                      date-format="dd/mm/yy"
                      size="small"
                      fluid
                      :minDate="new Date()"
                      @update:model-value="handleStartDateUpdate"
                    />
                  </FormField>
                  <FormField
                    label="Ngày kết thúc"
                    icon="pi pi-calendar-times"
                    class="min-w-[200px] flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <DatePicker
                      v-model="dueDate"
                      showTime
                      hourFormat="24"
                      placeholder="Kết thúc"
                      date-format="dd/mm/yy"
                      size="small"
                      fluid
                      :minDate="new Date()"
                      @update:model-value="handleDueDateUpdate"
                    />
                    <small class="text-red-500" v-if="formStore.errors.due_date">
                      {{ formStore.errors.due_date }}
                    </small>
                  </FormField>
                </div>

                <div class="flex flex-wrap gap-4">
                  <FormField
                    label="Người liên quan"
                    icon="pi pi-users"
                    class="flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <UserMultiAssign :max-display="4" v-model="formStore.contributor" usePrimeVueInput />
                  </FormField>

                  <FormField
                    label="Người duyệt"
                    icon="pi pi-user-edit"
                    class="flex-1"
                    size="sm"
                    :readonly="!formStore.canEditTaskDetails"
                  >
                    <UserMultiAssign :max-display="4" v-model="formStore.reviewer" usePrimeVueInput />
                  </FormField>
                </div>

                <FormField label="Nội dung công việc" icon="pi pi-clipboard" size="sm">
                  <NoteEditor v-model="formStore.formData.note" placeholder="Thêm nội dung" />
                </FormField>
              </div>

              <!-- Task Notes -->
              <div v-else-if="formStore.activeMenuItem === 'notes'" class="h-full">
                <TaskNoteForm
                  :task-id="formStore.taskData?.id"
                  :history="formStore.formData.history"
                  :creator-id="formStore.taskData?.creator_id"
                  :created-at="formStore.taskData?.created_at"
                />
              </div>
            </div>

            <!-- Right sidebar menu -->
            <div class="w-12 flex-shrink-0 border-l border-gray-200">
              <ul class="flex flex-col items-center py-1">
                <li v-for="item in menuItems" :key="item.key" class="mb-2 flex w-full justify-center">
                  <Button
                    v-tooltip.left="item.label"
                    :icon="item.icon"
                    :class="[
                      'flex h-10 w-10 items-center justify-center transition-colors duration-200',
                      formStore.activeMenuItem === item.key
                        ? 'bg-blue-50 text-blue-500 hover:bg-blue-100'
                        : 'text-gray-400 hover:bg-gray-100',
                    ]"
                    :aria-label="item.label"
                    @click="formStore.activeMenuItem = item.key"
                    text
                  />
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Error state when task data cannot be loaded -->
        <div v-else class="flex flex-grow items-center justify-center">
          <div class="text-center">
            <i class="pi pi-exclamation-triangle mb-4 text-4xl text-red-500"></i>
            <p class="text-gray-600">Không thể tải thông tin công việc</p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div
        class="flex justify-end gap-2 border-t border-gray-200 p-3"
        v-if="(formStore.isExpanded || formStore.activeMenuItem === 'details') && formStore.taskData && !formStore.isLoading"
      >
        <Button label="Hủy" icon="pi pi-times" @click="handleClose" severity="danger" outlined />
        <Button label="Lưu" icon="pi pi-save" @click="handleSubmit" autofocus />
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { TaskStateEnum } from "@/api/bcare-enum";
import { FormField } from "@/components/Form";
import SearchPeople from "@/components/Person/SearchPeople.vue";
import { PrioritySelect, StateSelectBtn } from "@/components/Select";
import { UserMultiAssign } from "@/components/User";
import { NoteEditor } from "@/components/WysiwgEditor";
import { useDatePicker } from "@/composables/useDatePicker";
import { useTaskStore } from "@/stores/task-store-v2";

import TaskNoteForm from "./TaskNoteForm.vue";
import TaskTypeSelect from "./TaskTypeSelect.vue";
import { useTaskUpdateFormStore } from "./stores/useTaskUpdateFormStore";

const props = defineProps<{
  taskId: number;
}>();

const emit = defineEmits<{
  (e: "reload-data"): void;
  (e: "close"): void;
  (e: "task-completed", data: { taskId: number; serial?: number }): void;
}>();

const taskStore = useTaskStore();
const formStore = useTaskUpdateFormStore();

// Clean up store when component unmounts
onUnmounted(() => {
  formStore.$reset();
});

// Load task data when taskId changes
watchEffect(async () => {
  if (props.taskId) {
    await formStore.loadTask(props.taskId);
  }
});

// Menu items
const menuItems = [
  { key: "details", label: "Chi tiết", icon: "pi pi-list" },
  { key: "notes", label: "Hoạt động", icon: "pi pi-comments" },
];

// Date pickers with proper binding to store
const { dateValue: startDate, handleDateUpdate: handleStartDateUpdate } = useDatePicker(
  () => formStore.formData.start_date,
  (value) => {
    formStore.formData.start_date = value ?? "";
    formStore.startDate = value ? new Date(value) : null;
  },
);

const { dateValue: dueDate, handleDateUpdate: handleDueDateUpdate } = useDatePicker(
  () => formStore.formData.due_date,
  (value) => {
    formStore.formData.due_date = value ?? "";
    formStore.dueDate = value ? new Date(value) : null;
  },
);

// Sync date values from store
watch(() => formStore.startDate, (value) => {
  if (value) startDate.value = value;
});

watch(() => formStore.dueDate, (value) => {
  if (value) dueDate.value = value;
});

// Handle form submission
async function handleSubmit() {
  const result = await formStore.submitForm();

  if (result.success) {
    handleClose();
    emit("reload-data");

    if (result.shouldShowTemplateSelector && formStore.taskData) {
      emit("task-completed", {
        taskId: formStore.taskData.id,
        serial: formStore.taskData.serial
      });
    }
  }
}

// Handle close
function handleClose() {
  formStore.resetForm();
  emit("close");
}

// Handle state change
function handleStateChange(newState: TaskStateEnum) {
  formStore.updateState(newState);
}
</script>
