<template>
  <div class="flex h-full flex-col">
    <!-- Message List Container -->
    <div class="flex-grow relative bg-gray-50 overflow-hidden">
      <!-- Scrollable Content Wrapper -->
      <div
        class="absolute bottom-0 left-0 right-0 top-0 flex flex-col-reverse overflow-y-auto"
      >
        <div class="p-4 flex flex-col gap-4">
          <!-- Combined Timeline Section -->
          <template v-if="combinedActivities.length > 0">
            <template v-for="activity in combinedActivities" :key="activity.timestamp">
          <!-- History Item -->
          <template v-if="activity.type === 'history'">
            <HistoryTimeline
              v-if="activity && activity.changes && Array.isArray(activity.changes)"
              :history="[activity]"
              used-at-component="task"
            />
          </template>

          <!-- Message Item -->
          <template v-else>
            <div class="flex gap-3">
              <UserAvatar
                :user="{
                  ...activity.creator,
                  id: activity.user_id,
                  username: activity.creator.username,
                  name: activity.creator.name,
                }"
                class="h-8 w-8 flex-shrink-0"
                show-name
              >
                <template #subtitle>
                  <span class="text-xs text-gray-500">{{ formatRelativeTime(activity.created_at) }}</span>
                </template>
              </UserAvatar>
              <div class="flex-1 min-w-0">
                <div class="max-w-full">
                  <div
                    class="inline-block max-w-full rounded-3xl rounded-tl-none bg-white px-3.5 py-2 border"
                  >
                    <div
                      class="prose relative w-full max-w-none break-words text-sm prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:max-w-1/2 prose-img:rounded-md"
                      v-lightbox
                    >
                      {{ activity.body }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>
          </template>

          <!-- Empty State -->
          <div v-else class="flex items-center justify-center text-center py-8">
            <div>
              <div class="mb-2 text-6xl text-blue-300">
                <i class="pi pi-comments"></i>
              </div>
              <p class="text-sm text-gray-500">Chưa có hoạt động nào!</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Fixed Message Input Form -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white p-2 sm:p-3">
      <div class="flex w-full items-center gap-2">
        <InputText
          v-model="newMessage"
          placeholder="Nhập nội dung..."
          @keyup.enter="handleSendMessage"
          class="min-w-0 flex-grow px-2 py-1 text-sm sm:px-3 sm:py-2 sm:text-base"
        />
        <Button
          type="submit"
          icon="pi pi-send"
          class="p-button-rounded h-8 w-8 flex-shrink-0 sm:h-10 sm:w-10"
          @click="handleSendMessage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import { computed, onMounted, ref, watch } from "vue";

import {
  HistoryEntry,
  TaskNoteAddRequest,
  TaskNoteListRequest,
  TaskNoteResponse,
} from "@/api/bcare-types-v2";
import HistoryTimeline from "@/components/Timeline/HistoryTimeline.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useTaskStore } from "@/stores/task-store";
import { formatRelativeTime } from "@/utils/time-helper";

const props = defineProps<{
  taskId?: number;
  history?: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
}>();

const emit = defineEmits<{
  (e: "messageSent"): void;
}>();

const taskStore = useTaskStore();
const messages = ref<TaskNoteResponse[]>([]);
const newMessage = ref("");

const fetchMessages = async () => {
  if (!props.taskId) return;

  const query: TaskNoteListRequest = {
    page_size: 100,
    filter: {
      task_id: props.taskId,
    },
  };

  try {
    const response = await taskStore.listTaskNotes(query);
    messages.value = response?.notes || [];
  } catch (error) {
    console.error("Failed to fetch task notes:", error);
  }
};

const handleSendMessage = async () => {
  if (!props.taskId || !newMessage.value.trim()) {
    return;
  }

  const request: TaskNoteAddRequest = {
    task_id: props.taskId,
    body: newMessage.value.trim(),
  };

  try {
    await taskStore.addTaskNote(request);
    newMessage.value = "";
    emit("messageSent");
    await fetchMessages(); // Refresh the messages list
  } catch (error) {
    console.error("Failed to add note:", error);
  }
};

const combinedActivities = computed(() => {
  const activities = [
    // Transform history entries
    ...(props.history?.map((entry) => ({
      ...entry,
      type: "history" as const,
      timestamp: entry.timestamp,
    })) || []),
    // Transform messages
    ...(messages.value.map((message) => ({
      ...message,
      type: "message" as const,
      timestamp: message.created_at,
    })) || []),
  ];

  // Add creation event if creatorId and createdAt exist
  if (props.creatorId && props.createdAt) {
    activities.push({
      type: "history" as const,
      timestamp: props.createdAt,
      user_id: props.creatorId,
      changes: [
        {
          field: "created_at",
          old_value: "",
          new_value: "",
        },
      ],
    });
  }

  return activities.sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
  );
});


onMounted(() => {
  fetchMessages();
});

watch(() => props.taskId, () => {
  fetchMessages();
});
</script>
