<template>
  <Dialog
    v-model:visible="visible"
    :style="{ width: '50vw' }"
    :modal="true"
    closable
    dismissableMask
    :header="'Giao công việc'"
  >
    <div class="space-y-4">
      <FormField label="Người nhận việc" icon="pi pi-user-plus">
        <UserMultiAssign v-model="primary" usePrimeVueInput onlyCurrentDepartment />
      </FormField>

      <div class="flex flex-wrap gap-4">
        <FormField label="Người liên quan" icon="pi pi-users" class="flex-1">
          <UserMultiAssign v-model="contributor" usePrimeVueInput :max-display="3" />
        </FormField>
        <FormField label="Người duyệt" icon="pi pi-user-edit" class="flex-1">
          <UserMultiAssign v-model="reviewer" usePrimeVueInput :max-display="3" />
        </FormField>
      </div>
    </div>

    <template #footer>
      <Button label="Hủy" icon="pi pi-times" severity="danger" @click="close" outlined />
      <Button label="Cập nhật" icon="pi pi-save" @click="updateAssignments" autofocus />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import Dialog from "primevue/dialog";
import { ref } from "vue";

import { UserResponse, UserShort } from "@/api/bcare-types-v2";
import { FormField } from "@/components/Form";
import { UserMultiAssign } from "@/components/User";
import { useToastStore } from "@/stores/toast-store";
import { useTaskStore } from "@/stores/task-store";

const props = defineProps<{
  taskIds: number[];
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "updated"): void;
}>();

const visible = ref(false);
const primary = ref<UserResponse[] | UserShort[]>([]);
const contributor = ref<UserResponse[] | UserShort[]>([]);
const reviewer = ref<UserResponse[] | UserShort[]>([]);

const taskStore = useTaskStore();
const { success } = useToastStore();

const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
  primary.value = [];
  contributor.value = [];
  reviewer.value = [];
  emit("close");
};

const updateAssignments = async () => {
  try {
    await taskStore.bulkUpdateTaskAssignments({
      id_list: props.taskIds,
      users: [
        ...primary.value.map((user) => ({ user_id: user.id, role: "primary" as const })),
        ...contributor.value.map((user) => ({ user_id: user.id, role: "contributor" as const })),
        ...reviewer.value.map((user) => ({ user_id: user.id, role: "reviewer" as const })),
      ],
    });
    success({ message: "Cập nhật người nhận việc thành công" });
    emit("updated");
    close();
  } catch (error) {
    console.error("Failed to update assignments:", error);
  }
};

defineExpose({ open });
</script>
