import { defineStore } from 'pinia';
import { computed, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash';

import { TaskStateEnum } from '@/api/bcare-enum';
import {
  DepartmentResponse,
  TaskResponse,
  TaskUpdateRequest,
  UserResponse,
  UserShort,
} from '@/api/bcare-types-v2';
import { rules, useFormValidation } from '@/composables/useFormValidation';
import { useModifiedFields } from '@/composables/useModifiedFields';
import { useTaskStore } from '@/stores/task-store-v2';
import { useComponentSetting } from '@/hooks/useComponentSetting';

interface TaskFormValidation {
  title: string;
  primary: UserShort[];
}

export const useTaskUpdateFormStore = defineStore('taskUpdateForm', () => {
  const taskStore = useTaskStore();
  
  // Component setting for persistent expanded state
  const { currentSettingValue, updateCurrentSetting } = useComponentSetting('task-update-form-expanded', {
    useLocal: true,
    defaultValue: { isExpanded: false }
  });
  
  // State
  const taskData = ref<TaskResponse | null>(null);
  const isLoading = ref(false);
  const isVisible = ref(true);
  const activeMenuItem = ref('details');
  const isExpanded = computed({
    get: () => currentSettingValue.value?.isExpanded ?? false,
    set: (value: boolean) => {
      updateCurrentSetting({ isExpanded: value });
    }
  });
  
  // Form data
  const formData = reactive<TaskResponse>({} as TaskResponse);
  const primary = ref<UserResponse[] | UserShort[]>([]);
  const contributor = ref<UserResponse[] | UserShort[]>([]);
  const reviewer = ref<UserResponse[] | UserShort[]>([]);
  const selectedDepartments = ref<DepartmentResponse[]>([]);
  const startDate = ref<Date | null>(null);
  const dueDate = ref<Date | null>(null);
  
  // Modified fields tracking
  const { modified, track, reset: resetModified } = useModifiedFields(formData);
  
  // Validation
  const validationRules = {
    title: [rules.required('Vui lòng nhập tiêu đề')],
    primary: [rules.required('Vui lòng chọn người nhận việc')],
  };
  
  const { errors, validateForm, clearErrors } = useFormValidation<TaskFormValidation>(validationRules);
  
  // Computed
  const canEditTaskDetails = computed(() => {
    return taskStore.hasAdministrativePrivileges() || taskStore.isTaskCreator(formData);
  });
  
  // Actions
  async function loadTask(taskId: number) {
    isLoading.value = true;
    try {
      taskData.value = await taskStore.getTask(taskId);
      initializeFormData();
    } catch (error) {
      console.error('Failed to fetch task:', error);
      taskData.value = null;
    } finally {
      isLoading.value = false;
    }
  }
  
  function initializeFormData() {
    if (!taskData.value) return;
    
    // Clear existing data and assign new data
    Object.keys(formData).forEach(key => delete (formData as any)[key]);
    Object.assign(formData, cloneDeep({
      ...taskData.value,
      person_id: taskData.value.person_id ?? null,
      priority: Number(taskData.value.priority),
      note: taskData.value.note || '',
    }));
    
    // Initialize user assignments
    primary.value = getUsersByRole('primary');
    contributor.value = getUsersByRole('contributor');
    reviewer.value = getUsersByRole('reviewer');
    
    // Initialize departments
    selectedDepartments.value = taskData.value.department_assignments
      ?.map((assignment) => assignment.department)
      .filter((dept): dept is DepartmentResponse => dept !== undefined) || [];
    
    // Initialize dates
    if (taskData.value.start_date) {
      startDate.value = new Date(taskData.value.start_date);
    }
    if (taskData.value.due_date) {
      dueDate.value = new Date(taskData.value.due_date);
    }
    
    track();
  }
  
  function getUsersByRole(role: string): UserResponse[] | UserShort[] {
    if (!taskData.value?.assignments) return [];
    return taskData.value.assignments
      .filter((a) => a.role === role)
      .map((a) => a.user)
      .filter((user): user is UserResponse | UserShort => user !== undefined);
  }
  
  async function submitForm(): Promise<{ success: boolean; shouldShowTemplateSelector: boolean }> {
    const formValues = {
      title: formData.title,
      primary: primary.value,
    };
    
    // Validate dates
    const dateValidation = rules.dateComparison(
      startDate.value || new Date(),
      dueDate.value || new Date(),
      'Thời gian kết thúc phải sau thời gian bắt đầu',
    );
    
    if (!validateForm(formValues) || !dateValidation.validate()) {
      if (!dateValidation.validate()) {
        errors.value.due_date = dateValidation.message;
      }
      return { success: false, shouldShowTemplateSelector: false };
    }
    
    const payload: TaskUpdateRequest = {
      ...formData,
      users: [
        ...primary.value.map((user) => ({ user_id: user.id, role: 'primary' as const })),
        ...contributor.value.map((user) => ({ user_id: user.id, role: 'contributor' as const })),
        ...reviewer.value.map((user) => ({ user_id: user.id, role: 'reviewer' as const })),
      ],
      departments: selectedDepartments.value.map((dept) => ({
        department_id: dept.id,
        role: 'primary',
      })),
      start_date: startDate.value?.toISOString() || '',
      due_date: dueDate.value?.toISOString() || '',
      end_date: dueDate.value?.toISOString() || '',
      modified: modified.value,
    };
    
    const response = await taskStore.updateTask(payload);
    
    if (response && taskData.value) {
      const originalState = taskData.value.state;
      const newState = formData.state;
      const shouldShowTemplateSelector =
        originalState !== newState &&
        (newState === TaskStateEnum.COMPLETED || newState === TaskStateEnum.COMPLETED_EARLY);
      
      return { success: true, shouldShowTemplateSelector };
    }
    
    return { success: false, shouldShowTemplateSelector: false };
  }
  
  function updateState(newState: TaskStateEnum) {
    formData.state = newState;
  }

  function toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }
  
  function resetForm() {
    clearErrors();
    resetModified();
    primary.value = [];
    contributor.value = [];
    reviewer.value = [];
    selectedDepartments.value = [];
    startDate.value = null;
    dueDate.value = null;
    activeMenuItem.value = 'details';
  }
  
  // Store reset function
  function $reset() {
    taskData.value = null;
    isLoading.value = false;
    isVisible.value = true;
    activeMenuItem.value = 'details';
    Object.keys(formData).forEach(key => delete (formData as any)[key]);
    resetForm();
  }
  
  return {
    // State
    taskData: computed(() => taskData.value),
    isLoading: computed(() => isLoading.value),
    isVisible,
    activeMenuItem,
    isExpanded,
    formData,
    primary,
    contributor,
    reviewer,
    selectedDepartments,
    startDate,
    dueDate,
    
    // Computed
    canEditTaskDetails,
    errors,
    
    // Actions
    loadTask,
    submitForm,
    updateState,
    toggleExpanded,
    resetForm,
    $reset,
  };
});