import { reactive, toRefs } from "vue";

import {
  pipelineAdd,
  pipelineDelete,
  pipelineGet,
  pipelineList,
  pipelineUpdate,
} from "@/api/bcare";
import {
  PipelineAddRequest,
  PipelineDeleteRequest,
  PipelineGetRequest,
  PipelineListRequest,
  PipelineResponse,
  PipelineUpdateRequest,
} from "@/api/bcare-types";
import { useToastStore } from "@/stores/toast-store";

export default function usePipeline() {
  const toastStore = useToastStore();
  const state = reactive<{ pipelines: PipelineResponse[]; pipelineItem: any }>({
    pipelineItem: {},
    pipelines: [],
  });

  const fetchPipelineList = async (request: PipelineListRequest) => {
    try {
      const response = await pipelineList(request);
      if (response.code === 0) {
        state.pipelines = response.data?.pipelines ?? [];
      } else {
        state.pipelines = [];
      }
      return response.data?.pipelines;
    } catch (error) {
      return [];
    }
  };

  const fetchPipeline = async (request: PipelineGetRequest) => {
    try {
      const response = await pipelineGet(request);
      if (response.code === 0) {
        state.pipelineItem = response.data;
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  const onAddPipeline = async (request: PipelineAddRequest) => {
    try {
      const response = await pipelineAdd(request);
      if (response.code === 0) {
        state.pipelineItem = response.data;
        toastStore.success({
          title: "Thêm mới quy trình thành công",
          message: "",
        });
      }
      return response.code;
    } catch (error) {
      //
    }
  };

  const onUpdatePipeLine = async (request: PipelineUpdateRequest) => {
    try {
      const response = await pipelineUpdate(request);
      if (response.code === 0) {
        toastStore.success({
          title: "Chỉnh sửa pipeline thành công",
          message: "",
        });
      }
      return response.code;
    } catch (error) {
      //
    }
  };
  const onDeletePipeline = async (request: PipelineDeleteRequest) => {
    try {
      const response = await pipelineDelete(request);
      if (response.code === 0) {
        toastStore.success({
          title: "Xóa quy trình thành công",
          message: "",
        });
      }
    } catch (error) {
      //
    }
  };

  return {
    ...toRefs(state),
    onAddPipeline,
    fetchPipeline,
    fetchPipelineList,
    onUpdatePipeLine,
    onDeletePipeline,
  };
}
