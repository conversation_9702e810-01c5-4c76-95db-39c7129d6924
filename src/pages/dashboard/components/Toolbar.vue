<script setup lang="ts">
// Vue imports
import { computed, reactive, ref } from "vue";
import { useRouter } from "vue-router";

import { PersonResponse } from "@/api/bcare-types-v2";
// Component imports
import Button from "@/base-components/Button";
import { FormInput } from "@/base-components/Form";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import PersonDealsPopover from "@/components/Deal/PersonDealsPopover.vue";
import SearchPerson from "@/components/Person/SearchPerson.vue";
import PopSetting from "@/components/Settings/PopSetting.vue";
import { useModalControl } from "@/composables/useModalControl";
import ContentWithToolbar from "@/layouts/ContentWithToolbar.vue";
import PersonAddModal from "@/components/Person/PersonFormModal.vue";
import DealCardViewModeSelector, {
  type ToggleState,
} from "@/pages/dashboard/components/ToggleModeSelector.vue";
// Store imports
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import { useToastStore } from "@/stores/toast-store";
import { usePipelineStore } from "@/stores/pipeline-store";

const props = withDefaults(
  defineProps<{
    search: string;
    dealCardViewMode: ToggleState;
  }>(),
  {
    search: "",
    dealCardViewMode: () => ({
      note: false,
      user: false,
      comment: false,
    }),
  },
);

const emit = defineEmits(["update-check-in-person", "update:search", "update:dealCardViewMode"]);

// Create a computed property to manage v-model
const internalDealCardViewMode = computed({
  get() {
    return props.dealCardViewMode;
  },
  set(value) {
    emit("update:dealCardViewMode", value);
  },
});
const internalSearch = computed({
  get: () => props.search,
  set: (value) => emit("update:search", value),
});

// Store initializations
const toastStore = useToastStore();
const modalStore = useModalCustomerStore();

const pipelineStore = usePipelineStore();
const PIPELINE_ID = 1;

// Hook initializations
const router = useRouter();

//TODO get pipeline id from url

const pipeline = computed(() => pipelineStore.getPipelineById(PIPELINE_ID));
const { modalRef: personFormModalRef, openModal: openPersonFormModal } = useModalControl();

const usePersonHandling = () => {
  const flagGetDetail = ref(0);
  const initPersonAddFormValue = reactive<{ full_name: string; phone: string }>({
    full_name: "",
    phone: "",
  });

  const handleAddPerson = async () => {
    openPersonFormModal();
  };

  const handleSearchEnterPressed = (search: string) => {
    const containsNumber = /\d/.test(search);
    if (containsNumber) {
      initPersonAddFormValue.phone = search;
      initPersonAddFormValue.full_name = "";
    } else {
      initPersonAddFormValue.full_name = search;
      initPersonAddFormValue.phone = "";
    }

    handleAddPerson();
  };

  const handlePersonAdded = (person: PersonResponse, buttonPressed: "left" | "right") => {
    if (buttonPressed === "right") {
      toastStore.success({ title: "Success", message: "Thêm khách hàng thành công" });
    } else if (buttonPressed === "left") {
      emit("update-check-in-person", person.id);
    }
  };

  return {
    flagGetDetail,
    initPersonAddFormValue,
    handleAddPerson,
    handleSearchEnterPressed,
    handlePersonAdded,
  };
};

const { initPersonAddFormValue, handleAddPerson, handleSearchEnterPressed, handlePersonAdded } =
  usePersonHandling();

const handleClick = (personId: number) => {
  emit("update-check-in-person", personId);
};
</script>

<template>
  <ContentWithToolbar>
    <template #left-toolbar>
      <div class="flex space-x-3 divide-x divide-solid" role="toolbar" aria-label="Left Toolbar">
        <!-- Deal Card View Mode Selector -->
        <DealCardViewModeSelector v-model="internalDealCardViewMode" />

        <!-- Search Person -->
        <div class="pl-3" role="search">
          <SearchPerson
            class="w-64"
            @person-selected="(p) => modalStore.openModal(p.id)"
            @action-clicked="(p) => handleClick(p.id)"
            @enter-pressed="handleSearchEnterPressed"
          />
        </div>

        <!-- Add Person Button -->
        <div class="pl-3">
          <Button variant="primary" class="w-32 font-medium" @click="handleAddPerson">
            <Lucide icon="Plus" class="mr-2 h-5 w-5" />
            Thêm mới
          </Button>
        </div>
      </div>
    </template>

    <template #right-toolbar>
      <div class="flex w-full items-center">
        <Menu class="relative z-[51] ml-auto">
          <Menu.Button
            as="Button"
            variant="soft-secondary"
            class="inline-flex cursor-pointer items-center justify-center rounded-none rounded-l-md border border-r-transparent bg-white bg-opacity-20 px-3 py-2 font-medium text-slate-600 shadow-sm transition duration-200 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-70 dark:border-darkmode-100/30 dark:bg-darkmode-100/20 dark:text-slate-300 dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:border-opacity-90 [&:not(button)]:text-center"
          >
            <Lucide icon="Trello" class="mr-2 h-4 w-4" />
            {{ pipeline?.name ?? "Pipeline" }}
            <Lucide icon="ChevronDown" class="ml-3 hidden h-4 w-4 font-semibold sm:block" />
          </Menu.Button>
          <Menu.Items placement="bottom-end" class="w-64">
            <Menu.Item v-for="(item, key) in pipelineStore.getPipelines" :key="key">
              <Lucide icon="Trello" class="mr-2 h-4 w-4" />
              <span class="truncate">
                {{ item.name }}
              </span>
              <Lucide icon="Check" class="ml-auto h-4 w-4 text-success" />
            </Menu.Item>
            <Menu.Item>
              <Lucide icon="Eye" class="mr-2 h-4 w-4" />
              <span class="truncate"> Vận hành </span>
            </Menu.Item>
            <Menu.Item @click="router.push({ name: 'top-menu-pipeline-add' })">
              <Lucide icon="Plus" class="mr-2 h-4 w-4" />
              <span class="truncate"> Thêm Pipeline </span>
            </Menu.Item>
          </Menu.Items>
        </Menu>
        <Button class="h-full rounded-none rounded-r-md bg-white dark:bg-slate-700 md:mr-1">
          <Lucide icon="Edit3" class="h-4 w-4" />
        </Button>
      </div>

      <div class="flex flex-col-reverse items-center sm:flex-row md:ml-3">
        <div class="relative mr-3 mt-3 w-full sm:mt-0 sm:w-auto">
          <Lucide
            icon="Search"
            class="absolute inset-y-0 left-0 z-10 my-auto ml-3 h-4 w-4 text-slate-500"
          />
          <FormInput
            v-model="internalSearch"
            type="text"
            class="!box w-full px-10 sm:w-64"
            placeholder="Highlight khách hàng"
          />
          <Menu class="absolute inset-y-0 right-0 mr-3 flex items-center">
            <Menu.Button as="a" role="button" class="block h-4 w-4" href="#">
              <Lucide icon="ChevronDown" class="h-4 w-4 cursor-pointer text-slate-500" />
            </Menu.Button>
            <Menu.Items placement="bottom-end">
              <Menu.Item>
                <Lucide icon="Home" class="mr-2 h-4 w-4" />
                <span class="truncate"> Updental Bình Thạnh </span>
              </Menu.Item>
            </Menu.Items>
          </Menu>
        </div>
        <PopSetting title="Tuỳ chỉnh" setting-key="dashboard" />
      </div>
    </template>

    <slot></slot>
    <PersonAddModal
      ref="personFormModalRef"
      :left-button="true"
      :initial-values="initPersonAddFormValue"
      @person-added="handlePersonAdded"
    />

    <PersonDealsPopover />
  </ContentWithToolbar>
</template>
