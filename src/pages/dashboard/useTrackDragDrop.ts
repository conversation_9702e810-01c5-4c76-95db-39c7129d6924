import { onUnmounted, reactive, Ref, ref, toValue, watch } from "vue";

import type { DealResponse, StageResponse, TrackResponse } from "@/api/bcare-types-v2";
import { trackUpdate } from "@/api/bcare-v2";
import { useToastStore } from "@/stores/toast-store";
import { useWsStore } from "@/stores/ws-store";

export default function useTrackDragDrop(
  trackList: Ref<TrackResponse[]>,
  WS_ROOM: string,
  options?: {
    onRefetchTracks?: () => Promise<void>;
  },
) {
  const wsStore = useWsStore();
  const { realtime } = wsStore;
  const draggingTracks = ref<Set<number>>(new Set());
  const trackId = ref<number>(0);
  const dragStageId = ref<number>(0);
  const stageDragId = ref<number>(0);
  const drag = ref(false);
  const toastStore = useToastStore();
  let previousConnectionState = false;

  const tracksByStageId = reactive<{ [key: number]: TrackResponse[] }>({});

  const initializeTracksByStageId = (stages: StageResponse[]) => {
    if (!stages || !Array.isArray(stages)) {
      stages = toValue(stages);
    }
    const tempTracksByStageId: { [key: number]: TrackResponse[] } = {};
    stages.forEach((stage) => {
      tempTracksByStageId[stage.id] = [];
    });

    trackList.value.forEach((track) => {
      const stageId = track.stage_id;
      if (!tempTracksByStageId[stageId]) {
        tempTracksByStageId[stageId] = [];
      }
      tempTracksByStageId[stageId].push(track);
    });

    Object.assign(tracksByStageId, tempTracksByStageId);
  };

  const insertTrack = (track: TrackResponse) => {
    const stageId = track.stage_id;
    if (!tracksByStageId[stageId]) {
      tracksByStageId[stageId] = [];
    }
    tracksByStageId[stageId].unshift(track);
    if (!trackList.value.some((t) => t.id === track.id)) {
      trackList.value.push(track);
    }
  };

  const removeTrackFromStage = (trackId: number, stageId: number) => {
    const stageTracks = tracksByStageId[stageId];
    if (Array.isArray(stageTracks)) {
      const index = stageTracks.findIndex((track) => track.id === trackId);
      if (index !== -1) {
        stageTracks.splice(index, 1);
      }
    }
  };

  const addTrackToStage = (track: TrackResponse, stageId: number, position: number) => {
    if (!(stageId in tracksByStageId) || !Array.isArray(tracksByStageId[stageId])) {
      tracksByStageId[stageId] = [];
    }

    const existingIndex = tracksByStageId[stageId].findIndex((t) => t.id === track.id);
    if (existingIndex !== -1) {
      tracksByStageId[stageId].splice(existingIndex, 1);
      if (existingIndex < position) {
        position--;
      }
    }

    position = Math.max(0, Math.min(position, tracksByStageId[stageId].length));
    tracksByStageId[stageId].splice(position, 0, track);
  };

  const switchDeal = (trackId: number, oldDealId: number, newDeal: DealResponse) => {
    handleDealSwitch(trackId, oldDealId, newDeal);
    broadcastDealSwitchInTrack(trackId, oldDealId, newDeal);
  };

  const handleDealSwitch = (trackId: number, oldDealId: number, newDeal: DealResponse) => {
    const trackIndex = trackList.value.findIndex((t) => t.id === trackId);
    if (trackIndex !== -1) {
      const updatedTrack = {
        ...trackList.value[trackIndex],
        deal: newDeal,
        deal_id: newDeal.id,
      };
      trackList.value.splice(trackIndex, 1, updatedTrack);
    }

    Object.values(tracksByStageId).forEach((stageTracks) => {
      const trackIndexInStage = stageTracks.findIndex((t) => t.id === trackId);
      if (trackIndexInStage !== -1) {
        const updatedTrack = {
          ...stageTracks[trackIndexInStage],
          deal: newDeal,
          deal_id: newDeal.id,
        };
        stageTracks.splice(trackIndexInStage, 1, updatedTrack);
      }
    });
  };

  const onStart = (evt: any) => {
    const { clone } = evt;
    const trackID = +clone.dataset.trackid;
    const fromStageId = +clone.dataset.stageid;
    stageDragId.value = fromStageId || 0;
    drag.value = true;
    broadcastTrackDragStart(trackID);
    return { trackID, fromStageId };
  };

  const onEnd = async (evt: any) => {
    const { clone, to, newIndex } = evt;
    const trackID = +clone.dataset.trackid;
    const dropStageId = +to.dataset.stageid;
    const fromStageId = +clone.dataset.stageid;
    drag.value = false;
    broadcastTrackDragEnd(trackID, dropStageId, newIndex);

    if (fromStageId !== dropStageId) {
      const track = trackList.value.find((t) => t.id === trackID);
      if (track) {
        track.stage_id = dropStageId;
        broadcastTrackUpdate(trackID, dropStageId, fromStageId, newIndex);
        const res = await trackUpdate({
          id: track.id,
          stage_id: dropStageId,
          weight: newIndex,
        });
        if (res.code === 0 && res.data) {
          //TODO Trigger reload
        }
      }
    }
  };

  const onMove = (evt: any) => {
    trackId.value = evt.draggedContext.element.id;
    dragStageId.value = evt.from.dataset.stageid;
    return true;
  };

  const handleChangeTrackStage = async (
    trackId: number,
    newStageId: number,
    oldStageId: number,
    newIndex: number,
  ) => {
    const track = trackList.value.find((t) => t.id === trackId);
    if (track) {
      broadcastTrackUpdate(trackId, newStageId, oldStageId, newIndex);
      const res = await trackUpdate({
        id: track.id,
        stage_id: newStageId,
        weight: newIndex,
      });
      if (res.code === 0 && res.data) {
        removeTrackFromStage(track.id, oldStageId);
        addTrackToStage(res.data, newStageId, newIndex);
        toastStore.success({ message: "Track stage updated successfully." });
        return res.data;
      } else {
        toastStore.error({ message: "Failed to update track stage." });
      }
    }
    return null;
  };

  // WebSocket broadcast functions
  const broadcastTrackDragStart = (trackID: number) => {
    realtime.broadcast.to(WS_ROOM).emit("track_drag_start", trackID);
  };

  const broadcastTrackDragEnd = (trackID: number, dropStageId: number, newIndex: number) => {
    realtime.broadcast.to(WS_ROOM).emit("track_drag_end", {
      trackID,
      dropStageId,
      newIndex,
    });
  };

  const broadcastTrackUpdate = (
    trackId: number,
    newStageId: number,
    oldStageId: number,
    newIndex: number,
  ) => {
    realtime.broadcast.to(WS_ROOM).emit("track_update", {
      trackId,
      newStageId,
      oldStageId,
      newIndex,
    });
  };

  const broadcastTrackAdd = (newTrack: TrackResponse) => {
    realtime.broadcast.to(WS_ROOM).emit("track_add", newTrack);
  };

  const broadcastDealSwitchInTrack = (
    trackId: number,
    oldDealId: number,
    newDeal: DealResponse,
  ) => {
    realtime.broadcast.to(WS_ROOM).emit("track_deal_switch", {
      trackId,
      oldDealId,
      newDeal,
    });
  };

  const setupTrackWebSocketListeners = async () => {
    try {
      await realtime.on("track_drag_start", (msg) => {
        draggingTracks.value.add(+msg.data);
      });

      await realtime.on("track_update", (msg) => {
        let { trackId, newStageId, oldStageId, newIndex } = msg.data;
        const track = trackList.value.find((t) => t.id === trackId);
        if (oldStageId == 0) {
          oldStageId = track?.stage_id;
        }
        if (track) {
          if (track.stage_id !== newStageId) {
            track.stage_id = newStageId;
            removeTrackFromStage(track.id, oldStageId);
            if (newStageId != 0) {
              addTrackToStage(track, newStageId, newIndex);
            }
          }
        }
      });

      await realtime.on("track_drag_end", (msg) => {
        const { trackID } = msg.data;
        draggingTracks.value.delete(trackID);
      });

      await realtime.on("track_add", (msg) => {
        const newTrack = msg.data;
        insertTrack(newTrack);
      });

      await realtime.on("track_deal_switch", (msg) => {
        const { trackId, oldDealId, newDeal } = msg.data;
        handleDealSwitch(trackId, oldDealId, newDeal);
      });
    } catch (error) {
      console.error("Failed to setup WebSocket listeners:", error);
    }
  };

  // Setup WebSocket listeners when ready
  watch(
    () => wsStore.isReady,
    async (isReady) => {
      if (isReady) {
        if (!previousConnectionState) {
          await options?.onRefetchTracks?.();
        }
        try {
          await realtime.join(WS_ROOM);
          await setupTrackWebSocketListeners();
        } catch (error) {
          console.error("Failed to join WebSocket room or setup listeners:", error);
        }
      } else if (previousConnectionState) {
        await options?.onRefetchTracks?.();
      }

      previousConnectionState = isReady;
    },
    { immediate: true },
  );

  // Cleanup khi unmount
  onUnmounted(() => {
    realtime.leave(WS_ROOM).catch(console.error);
  });

  return {
    drag,
    draggingTracks,
    onStart,
    onEnd,
    onMove,
    handleChangeTrackStage,
    addTrackToStage,
    broadcastTrackAdd,
    broadcastTrackUpdate,
    tracksByStageId,
    initializeTracksByStageId,
    insertTrack,
    switchDeal,
    removeTrackFromStage,
    handleDealSwitch,
  };
}
