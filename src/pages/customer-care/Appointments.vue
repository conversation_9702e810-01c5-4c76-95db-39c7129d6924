<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { useDateFormat } from "@vueuse/core";
import Chip from "primevue/chip";
import Badge from "primevue/badge";
import type { PropType } from "vue";
import { computed, onMounted, ref, watch } from "vue";

import { AppointmentReminderStatus, FilterOperator } from "@/api/bcare-enum";
import { AppointmentResponse, Filter } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { ColumnDefinition, DataTable } from "@/components/DataTable";
import { NoteInfo } from "@/components/InfoText";
import { PersonCard } from "@/components/Person";
import SearchPerson from "@/components/Person/SearchPerson.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useAppointment } from "@/hooks/useAppointment";
import { useAppointmentQuery } from "@/hooks/useAppointmentQuery";
import useConstant from "@/hooks/useConstant";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import { handleDownload } from "@/utils/helper";
import useColorHash from "@/utils/use-tag";
import { isRecordRecentlyCreated, getHoursSinceCreation, getCreationTimeInfo } from "@/utils/time-helper";

import AppointmentDateFilter from "./components/AppointmentDateFilter.vue";

const { getConstants } = useConstant();

const props = defineProps({
  hasDoctor: {
    type: String as PropType<"yes" | "no">,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  useDoctor: {
    type: Boolean,
    default: false,
  },
  customColumns: {
    type: Array as PropType<ColumnDefinition<AppointmentResponse>[]>,
    default: () => [],
  },
  customFilterConfigs: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
});

const { appointments, total, fetchAppointments, fetchDoctorAppointments, isLoading } =
  useAppointmentQuery({
    hasDoctor: props.hasDoctor,
  });

const { updateAppointment, getSortedAppointmentTypes } = useAppointment();
const modalStore = useModalCustomerStore();

const showFilter = ref(false);

const dateFilter = ref<Filter[]>([]);

const hashColor = useColorHash();

// Thêm biến để theo dõi trang hiện tại
const currentPage = ref(1);

// Add date formatters
const timeFormat = (date: string) => useDateFormat(date, "HH:mm").value;
const dateFormat = (date: string) => useDateFormat(date, "DD/MM/YYYY").value;

// Add a ref to track when filters change
const paginationKey = ref(0);

// Add a ref to track the "first" value for pagination
const first = ref(0);

// Column definitions
const columns = computed<ColumnDefinition<AppointmentResponse>[]>(() => {
  //  custom columns
  if (props.customColumns.length > 0) {
    return props.customColumns;
  }

  // default column logic
  const baseColumns: ColumnDefinition<AppointmentResponse>[] = [
    {
      field: "person.full_name" as const,
      header: "Khách hàng",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "text",
      filterPlaceholder: "Tìm kiếm khách hàng",
      filterMatchMode: FilterMatchMode.CONTAINS,
    },
    {
      field: "arrived_at",
      header: "Thời gian",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "select",
      filterPlaceholder: "Trạng thái đến",
      filterMatchMode: FilterMatchMode.EQUALS,
      filterOptions: [
        { title: "Chưa đến", value: "no" },
        { title: "Đã đến", value: "yes" },
      ],
    },
    {
      field: "extra_notes",
      header: "Công việc",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "text",
      filterPlaceholder: "Tìm kiếm công việc",
      filterMatchMode: FilterMatchMode.CONTAINS,
    },
    {
      field: "notes",
      header: "Ghi chú",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "text",
      filterPlaceholder: "Tìm kiếm ghi chú",
      filterMatchMode: FilterMatchMode.CONTAINS,
    },
  ];

  // Thêm cột reminder_status nếu không phải useDoctor
  if (!props.useDoctor) {
    baseColumns.push({
      field: "reminder_status",
      header: "Nhắc lịch",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "select",
      filterPlaceholder: "Trạng thái nhắc",
      filterMatchMode: FilterMatchMode.EQUALS,
      filterOptions: [
        { title: "Đã nhắc", value: `${AppointmentReminderStatus.REMINDED}` },
        { title: "Chưa nhắc", value: `${AppointmentReminderStatus.OTHER}` },
      ],
    });
  }

  // Thêm các cột chỉ dành cho hasDoctor="yes" và không phải useDoctor
  if (props.hasDoctor === "yes" && !props.useDoctor) {
    baseColumns.splice(
      1,
      0,
      {
        field: "doctor.name" as const,
        header: "Bác sĩ",
        sortable: false,
        showFilterMenu: showFilter.value,
        filterType: "text",
        filterPlaceholder: "Tìm kiếm bác sĩ",
        filterMatchMode: FilterMatchMode.CONTAINS,
      },
      {
        field: "type" as const,
        header: "Loại hẹn",
        sortable: false,
        showFilterMenu: showFilter.value,
        filterType: "custom",
        filterPlaceholder: "Chọn loại hẹn",
        filterMatchMode: FilterMatchMode.EQUALS,
        filterOptions: getSortedAppointmentTypes(
          getConstants?.value?.appointment_type,
          "filter",
        ) as { title: string; value: string }[],
      },
    );
  } else if (props.hasDoctor === "yes" && props.useDoctor) {
    // Chỉ thêm cột type khi useDoctor=true
    baseColumns.splice(1, 0, {
      field: "type" as const,
      header: "Loại hẹn",
      sortable: false,
      showFilterMenu: showFilter.value,
      filterType: "custom",
      filterPlaceholder: "Chọn loại hẹn",
      filterMatchMode: FilterMatchMode.EQUALS,
      filterOptions: getSortedAppointmentTypes(getConstants?.value?.appointment_type, "filter") as {
        title: string;
        value: string;
      }[],
    });
  }

  return baseColumns;
});

// Filter configurations
const defaultFilterConfigs = {
  "person.full_name": {
    field: "person",
    isPayload: true,
  },
  "doctor.name": {
    field: "doctor",
    isPayload: true,
  },
  type: {
    field: "type",
    operator: FilterOperator.IN,
    isMultiselect: true,
  },
  arrived_at: {
    field: "arrived",
    operator: FilterOperator.EQ,
    isPayload: true,
  },
  extra_notes: {
    field: "extra_notes",
    operator: FilterOperator.LIKE,
  },
  notes: {
    field: "note",
    operator: FilterOperator.LIKE,
    isPayload: true,
  },
  reminder_status: {
    field: "reminder_status",
    operator: FilterOperator.EQ,
  },
};

// Merge default filter configs with custom ones
const filterConfigs = computed(() => ({
  ...defaultFilterConfigs,
  ...props.customFilterConfigs,
}));

// Modify the resetPagination function
const resetPagination = () => {
  currentPage.value = 1;
  first.value = 0; // Reset to first page (0-based index)
};

const { filters, currentFilterPayload } = useFilterQuery(() => {
  // Reset pagination when filters change
  resetPagination();
  loadAppointments();
}, filterConfigs.value);

// Update handlePageChange to keep first in sync
const handlePageChange = (event: { first: number; rows: number }) => {
  first.value = event.first;
  const page = Math.floor(event.first / event.rows) + 1;
  currentPage.value = page;
  loadAppointments(false, page);
};

const loadAppointments = async (getCount: boolean = true, page?: number) => {
  const pageToLoad = page || currentPage.value;
  const fetchFn = props.useDoctor ? fetchDoctorAppointments : fetchAppointments;

  await fetchFn(
    {
      offset: (pageToLoad - 1) * 10,
      limit: 10,
      ...currentFilterPayload.value,
      filters: [...(currentFilterPayload.value?.filters || []), ...dateFilter.value],
    },
    getCount,
  );
};

onMounted(() => {
  currentPage.value = 1;
});

const isPastAppointment = (startTime: string) => {
  const appointmentDate = new Date(startTime);
  const today = new Date();
  // Reset time to start of day (00:00:00)
  appointmentDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);

  return appointmentDate < today;
};

// Add update notes handler
const handleUpdateNotes = async (data: AppointmentResponse, notes: string) => {
  if (data) {
    const success = await updateAppointment({
      ...data,
      notes,
      modified: ["notes"],
    });
    if (success) {
      await loadAppointments(true);
    }
  }
};

// Thêm computed để lấy range date từ dateFilter
const selectedDateRange = computed(() => {
  if (!dateFilter.value?.length) return null;

  const fromFilter = dateFilter.value.find((f) => f.operator === "GTE")?.value;
  const toFilter = dateFilter.value.find((f) => f.operator === "LT")?.value;

  // Extract dates using single regex match
  const [fromDate, toDate] = [fromFilter, toFilter].map((filter) => {
    const match = filter?.match(/time\((.*?)Z\)/)?.[1];
    return match ? new Date(match) : null;
  });

  if (
    !fromDate ||
    !toDate ||
    fromDate.toString() === "Invalid Date" ||
    toDate.toString() === "Invalid Date"
  )
    return null;

  // Check if same day and time range is 00:00 to 23:59
  const isOneDay =
    fromDate.toISOString().slice(0, 10) === toDate.toISOString().slice(0, 10) &&
    fromDate.toISOString().endsWith("00:00:00.000Z") &&
    toDate.toISOString().endsWith("23:59:59.000Z");

  const fromFormatted = useDateFormat(fromDate, "DD/MM/YYYY").value;

  return {
    fromFormatted,
    toFormatted: isOneDay ? fromFormatted : useDateFormat(toDate, "DD/MM/YYYY").value,
    isOneDay,
  };
});

const { confirm } = useConfirmTippy();

const handleToggleChange = async (item: AppointmentResponse, checked: boolean, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận thay đổi trạng thái nhắc hẹn",
    icon: "pi pi-info-circle",
    acceptLabel: "Đồng ý",
    rejectLabel: "Hủy",
    onAccept: async () => {
      const success = await updateAppointment({
        ...item,
        reminder_status: checked
          ? AppointmentReminderStatus.REMINDED
          : AppointmentReminderStatus.OTHER,
      });
      if (success) {
        await loadAppointments(true);
      }
    },
    onReject: () => {
      // Reload để reset lại trạng thái toggle nếu user cancel
      loadAppointments(true);
    },
  });
};

const handleOpenModal = (personId: number) => {
  modalStore.openModal(personId.toString());
};

// Add loading state
const isExporting = ref(false);

const handleExport = async () => {
  if (isExporting.value) return;
  isExporting.value = true;

  try {
    const res = await fetchAppointments(
      {
        ...currentFilterPayload.value,
        filters: [...(currentFilterPayload.value?.filters || []), ...dateFilter.value],
      },
      false,
      true,
    );

    if (!res.data?.result?.file_url) throw new Error("No file URL returned");

    // Get current date and format it
    const today = new Date();
    const day = today.getDate().toString().padStart(2, "0");
    const month = (today.getMonth() + 1).toString().padStart(2, "0");
    const year = today.getFullYear();

    // Create filename with date format
    const filename = `lich_hen_${day}_${month}_${year}.xlsx`;

    // Open in new tab
    await handleDownload(res.data.result.file_url, filename);
  } finally {
    isExporting.value = false;
  }
};

// Add watcher for modal visibility
watch(
  () => modalStore.visible,
  (newValue) => {
    if (!newValue) {
      // When modal closes
      loadAppointments(true, currentPage.value);
    }
  },
);

defineExpose({
  loadAppointments,
  currentPage,
  currentFilterPayload,
  dateFilter,
});
</script>

<template>
  <div class="mt-5 flex flex-wrap gap-5">
    <!-- Sidebar -->
    <div class="w-full shrink-0 xl:w-[250px] 2xl:w-[300px]">
      <AppointmentDateFilter
        field="start_time"
        v-model:filters="dateFilter"
        @update:filters="
          () => {
            resetPagination();
            loadAppointments();
          }
        "
      />
      <SearchPerson @person-selected="(p) => handleOpenModal(p.id)" class="mt-2" />
    </div>

    <!-- Main content -->
    <div class="min-w-0 flex-1">
      <DataTable
        :key="paginationKey"
        :title="title"
        :columns="columns"
        :data="appointments"
        :loading="isLoading"
        :total-records="total"
        paginator
        :rows="10"
        :first="first"
        v-model:filters="filters"
        @page="handlePageChange"
        size="small"
      >
        <!-- Thêm template cho left-header -->
        <template #left-header>
          <div class="flex items-center gap-2">
            <span class="text-base font-medium"> {{ title }} ({{ total }}) </span>
            <template v-if="selectedDateRange">
              <div class="flex items-center gap-1">
                <i class="pi pi-calendar text-yellow-500" />
                <span class="text-sm text-slate-600">
                  {{ selectedDateRange.isOneDay ? `Ngày ` : `Từ ngày ` }}
                  <span class="font-medium">{{ selectedDateRange.fromFormatted }}</span>
                  {{ !selectedDateRange.isOneDay ? ` đến ` : `` }}
                  <span v-if="!selectedDateRange.isOneDay" class="font-medium">{{
                    selectedDateRange.toFormatted
                  }}</span>
                </span>
              </div>
            </template>
          </div>
        </template>

        <!-- Add export button -->
        <template #right-header v-if="!useDoctor">
          <Button
            icon="pi pi-file-excel"
            @click="handleExport"
            :loading="isExporting"
            :disabled="isExporting"
            v-tooltip="'Xuất excel'"
          />
        </template>

        <template #person.full_name="{ data }">
          <PersonCard
            :person="data.person"
            @submitName="() => handleOpenModal(data.person.id)"
            showCode
          />
        </template>

        <template #doctor.name="{ data }">
          <div class="flex items-center gap-2">
            <UserAvatar :user="data.doctor" />
            <span class="whitespace-nowrap font-medium">
              {{ data.doctor?.name }}
            </span>
          </div>
        </template>

        <template #type="{ data }">
          <div class="flex items-center gap-2">
            <span
              v-if="getConstants?.appointment_type?.[data.type]"
              class="py-0.2 ml-1 inline-block whitespace-nowrap rounded-md px-1.5 text-xs text-white"
              :style="{
                background: hashColor(
                  getConstants?.appointment_type?.[data.type]?.toString() ?? '1',
                  'source',
                ),
              }"
            >
              {{ getConstants?.appointment_type?.[data.type] }}
            </span>
            <span v-else>Chưa xác định</span>
            <span
              v-if="isRecordRecentlyCreated(data.created_at)"
              class="inline-flex items-center rounded-md bg-yellow-50 px-1.5 py-0.5 text-xs font-medium text-yellow-600"
              v-tooltip="getCreationTimeInfo(data.created_at)"
            >
              {{ getHoursSinceCreation(data.created_at) }}
            </span>
          </div>
        </template>

        <template #type.filter="{ filterModel, filterCallback }">
          <MultiSelect
            v-model="filterModel.value"
            :options="getSortedAppointmentTypes(getConstants?.appointment_type, 'filter')"
            optionLabel="title"
            filter
            optionValue="value"
            placeholder="Chọn loại hẹn"
            class="p-column-filter flex h-10 items-center"
            @change="filterCallback()"
            size="small"
            fluid
            :maxSelectedLabels="1"
            :selectedItemsLabel="`{0} mục đã chọn`"
          />
        </template>

        <template #arrived_at="{ data }">
          <div class="flex flex-col">
            <div class="mt-1.5 flex items-center">
              <Lucide icon="CalendarDays" class="mr-1 h-4 w-4 text-warning" />
              <span class="text-sm">
                {{ dateFormat(data.end_time) }}
              </span>
            </div>

            <div class="flex w-max items-center">
              <Lucide icon="Clock" class="mr-1 h-4 w-4 text-yellow-500" />
              <span class="text-sm">
                {{ timeFormat(data.start_time) }} -
                {{ timeFormat(data.end_time) }}
              </span>
            </div>
            <div class="mt-1.5 flex items-center" v-if="data.status === 3">
              <Chip class="rounded-full bg-success text-white">
                <Lucide icon="Check" class="h-4 w-4" />
                <span class="text-sm">
                  {{ timeFormat(data.arrived_at) }} - {{ dateFormat(data.arrived_at) }}
                </span>
              </Chip>
            </div>
          </div>
        </template>

        <template #extra_notes="{ data }">
          <NoteInfo :extra_notes="data.extra_notes" mode="list" />
        </template>

        <template #notes="{ data }">
          <NoteInfo
            :notes="data.notes"
            mode="tag"
            @update:notes="(newNotes) => handleUpdateNotes(data, newNotes)"
            :editable="!isPastAppointment(data.start_time)"
          />
        </template>

        <template #reminder_status="{ data }">
          <ToggleSwitch
            :modelValue="data.reminder_status === AppointmentReminderStatus.REMINDED"
            @click="
              (e: MouseEvent) =>
                handleToggleChange(
                  data,
                  data.reminder_status !== AppointmentReminderStatus.REMINDED,
                  e,
                )
            "
            @update:modelValue="() => {}"
          />
        </template>

        <!-- Dynamic slot handling for custom columns -->
        <template v-for="(_, name) in $slots" :key="name" v-slot:[name]="slotData">
          <slot :name="name" v-bind="slotData"></slot>
        </template>
      </DataTable>
    </div>
  </div>
</template>
