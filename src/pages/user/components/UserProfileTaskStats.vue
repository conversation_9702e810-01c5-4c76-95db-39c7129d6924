<script setup lang="ts">
import { onMounted } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import Lucide from "@/base-components/Lucide";
import useUserDashboard from "@/hooks/useUserDashboard";
import TaskItem from "@/pages/customer/components/ActivityTab/components/TaskItem.vue";
import router from "@/router";

const props = defineProps<{
  userId: number;
}>();

const { taskStats, fetchTaskStats, isLoading } = useUserDashboard({
  useStore: true,
  autoLoad: false,
  userId: props.userId,
});

// Format helpers
const stateMap = {
  [TaskStateEnum.NEW_TASK]: { class: "bg-yellow-500", text: "Mới tạo" },
  [TaskStateEnum.IN_PROGRESS]: { class: "bg-green-500", text: "Đang thực hiện" },
  [TaskStateEnum.AWAITING_APPROVAL]: { class: "bg-purple-400", text: "Chờ phê duyệt" },
  [TaskStateEnum.COMPLETED]: { class: "bg-blue-500", text: "Hoàn thành" },
  [TaskStateEnum.CANCELLED]: { class: "bg-red-500", text: "Đã hủy" },
  [TaskStateEnum.COMPLETED_EARLY]: { class: "bg-teal-500", text: "Hoàn thành sớm" },
};

const formatStatus = (status: string) => {
  return stateMap[status as TaskStateEnum]?.text || "Không xác định";
};

const getStatusColor = (status: string) => {
  const baseColor = stateMap[status as TaskStateEnum]?.class || "bg-gray-500";
  // Chuyển từ class Tailwind sang mã màu HEX
  const colorMap = {
    "bg-yellow-500": "#EAB308",
    "bg-green-500": "#22C55E",
    "bg-purple-400": "#A78BFA",
    "bg-blue-500": "#3B82F6",
    "bg-red-500": "#EF4444",
    "bg-teal-500": "#14B8A6",
    "bg-gray-500": "#6B7280",
  };
  return colorMap[baseColor as keyof typeof colorMap] || "#6B7280";
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

onMounted(async () => {
  await fetchTaskStats(props.userId);
});


const handleTaskClick = () => {
  router.push(`/task/`);
};
</script>

<template>
  <div>
    <template v-if="taskStats">
      <!-- Header -->
      <div class="intro-y flex h-10 items-center">
        <h2 class="mr-5 truncate text-lg font-medium">Công việc</h2>
      </div>

      <div class="mt-5">
        <!-- Overview Cards -->
        <div class="grid grid-cols-2 gap-4 lg:grid-cols-4">
          <div
            :class="[
              'zoom-in relative',
              'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
            ]"
          >
            <div class="box p-4">
              <div class="flex items-center">
                <Lucide icon="List" class="h-6 w-6 text-primary" />
                <div class="ml-auto">
                  <div class="rounded-full bg-success/20 px-2 py-1 text-xs font-medium text-success">
                    Total
                  </div>
                </div>
              </div>
              <div class="mt-4 text-2xl font-medium leading-8">{{ taskStats.overview.total_tasks }}</div>
              <div class="mt-1 text-sm text-slate-500">Tổng công việc</div>
            </div>
          </div>

          <div
            :class="[
              'zoom-in relative',
              'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
            ]"
          >
            <div class="box p-4">
              <div class="flex items-center">
                <Lucide icon="CheckCircle" class="h-6 w-6 text-success" />
                <div class="ml-auto">
                  <div class="rounded-full bg-success/20 px-2 py-1 text-xs font-medium text-success">
                    {{ Math.round(taskStats.overview.completion_rate) }}%
                  </div>
                </div>
              </div>
              <div class="mt-4 text-2xl font-medium leading-8">{{ taskStats.overview.completed_tasks }}</div>
              <div class="mt-1 text-sm text-slate-500">Đã hoàn thành</div>
            </div>
          </div>

          <div
            :class="[
              'zoom-in relative',
              'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
            ]"
          >
            <div class="box p-4">
              <div class="flex items-center">
                <Lucide icon="Clock" class="h-6 w-6 text-warning" />
                <div class="ml-auto">
                  <div class="rounded-full bg-warning/20 px-2 py-1 text-xs font-medium text-warning">
                    Pending
                  </div>
                </div>
              </div>
              <div class="mt-4 text-2xl font-medium leading-8">{{ taskStats.overview.remaining_tasks }}</div>
              <div class="mt-1 text-sm text-slate-500">Còn lại</div>
            </div>
          </div>

          <div
            :class="[
              'zoom-in relative',
              'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
            ]"
          >
            <div class="box p-4">
              <div class="flex items-center">
                <Lucide icon="TrendingUp" class="h-6 w-6 text-primary" />
                <div class="ml-auto">
                  <div class="rounded-full bg-primary/20 px-2 py-1 text-xs font-medium text-primary">
                    Rate
                  </div>
                </div>
              </div>
              <div class="mt-4 text-2xl font-medium leading-8">{{ Math.round(taskStats.overview.completion_rate) }}%</div>
              <div class="mt-1 text-sm text-slate-500">Tỷ lệ hoàn thành</div>
            </div>
          </div>
        </div>

        <!-- Status Breakdown & Upcoming Tasks -->
        <div class="mt-6 grid gap-6 lg:grid-cols-2">
          <!-- Status Breakdown -->
          <div class="box p-5">
            <h4 class="mb-4 text-base font-medium">Trạng thái công việc</h4>
            <div v-if="taskStats?.status_breakdown?.length" class="space-y-3">
              <div
                v-for="status in taskStats.status_breakdown"
                :key="status.status"
                class="flex items-center"
              >
                <div class="flex-1">
                  <div class="mb-1 flex items-center justify-between">
                    <span class="text-sm text-slate-600">
                      {{ formatStatus(status.status) }}
                    </span>
                    <span class="text-sm font-medium text-slate-800">
                      {{ status.count }}
                    </span>
                  </div>
                  <div class="h-2 overflow-hidden rounded-full bg-slate-100">
                    <div
                      class="h-full rounded-full transition-all duration-500"
                      :style="{
                        width: `${Math.round((status.count / taskStats.overview.total_tasks) * 100)}%`,
                        backgroundColor: getStatusColor(status.status),
                      }"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="flex h-16 items-center justify-center text-slate-500">
              Không có dữ liệu
            </div>
          </div>

          <!-- Upcoming Deadlines -->
          <div class="box p-5">
            <h4 class="mb-4 text-base font-medium">Công việc sắp đến hạn</h4>
            <div v-if="taskStats?.upcoming_deadlines?.length" class="space-y-2">
              <TaskItem
                v-for="task in taskStats.upcoming_deadlines"
                :key="task.id"
                :task="task"
                :show-actions="false"
                :show-checkbox="false"
                class="!p-3 transition-all duration-200 hover:bg-slate-50"
                @click="handleTaskClick()"
              />
            </div>
            <div v-else class="flex h-16 items-center justify-center text-slate-500">
              Không có dữ liệu
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- No Data State -->
    <div
      v-else
      class="flex min-h-[200px] items-center justify-center"
    >
      <div class="text-center text-slate-500">Không có dữ liệu</div>
    </div>
  </div>
</template>
