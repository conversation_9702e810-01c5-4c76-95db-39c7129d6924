<script setup lang="ts">
import Color from "color";
import { computed, ref } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { UserAvatar } from "@/components/User";
import { useHashColor } from "@/composables/useHashColor";
import useDepartment from "@/hooks/useDepartment";
import { useUpload } from "@/hooks/useUpload";
import useUser from "@/hooks/useUser";

import UserFormDrawer from "./UserFormDrawer.vue";

interface Props {
  user?: UserResponse;
  showUploadButton?: boolean;
  showRedirectButton?: boolean;
  showEditButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showUploadButton: true,
  showRedirectButton: false,
  showEditButton: true,
});
const { updateUser } = useUser();
const { getDepartmentNameById } = useDepartment();

const fileInput = ref<HTMLInputElement | null>(null);
const isEditDrawerVisible = ref(false);
const emit = defineEmits<{
  (e: "update"): void;
}>();

// Generate background color based on user name
const backgroundColor = computed(() => {
  if (!props.user?.name) return "#4F46E5"; // Default color
  return useHashColor(props.user.name, "UserAvatar");
});


const { uploadFile } = useUpload({
  maxFileSize: 5,
  allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  entity: props.user?.id
    ? {
        id: props.user.id,
        type: "user",
        usageType: "avatar",
      }
    : undefined,
});

const handleAvatarClick = () => {
  fileInput.value?.click();
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (!file || !props.user?.id) return;

  try {
    const fileResponse = await uploadFile(file);
    await updateUser({
      id: props.user.id,
      profile_image: fileResponse.path,
    });
  } catch (error) {
    console.error("Error uploading avatar:", error);
  }

  if (input) input.value = "";
};

const openEditDrawer = () => {
  isEditDrawerVisible.value = true;
};

const onEditSuccess = () => {
  emit("update");
};

// Add user fields for information section
const userFields = computed(() => [
  { label: "Họ và tên", value: props.user?.name, icon: "User" as const },
  { label: "Username", value: props.user?.username, icon: "AtSign" as const },
  { label: "Số điện thoại", value: props.user?.phone, icon: "Phone" as const },
  { label: "Email", value: props.user?.email, icon: "Mail" as const },
]);
</script>

<template>
  <div v-if="user">
    <!-- Header -->
    <div class="flex h-10 items-center">
      <h2 class="mr-5 truncate text-lg font-medium">Thông tin người dùng</h2>
    </div>

    <!-- Profile Card -->
    <div class="box mt-5">
      <!-- Cover Section -->
    <div
      class="relative h-32 rounded-t-md p-5"
      :style="{
        backgroundColor: backgroundColor,
        background: `linear-gradient(to right, ${backgroundColor}, ${Color(backgroundColor).lighten(0.2).hex()})`,
      }"
    >
      <!-- Edit button -->
      <div class="absolute right-3 top-3">
        <Button
          v-if="showEditButton"
          severity="secondary"
          variant="text"
          @click="openEditDrawer"
        >
          <Lucide icon="Edit" class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- Avatar Section -->
    <div class="relative px-5">
      <div class="absolute -translate-y-1/2">
        <div class="relative">
          <UserAvatar :user="user" class="size-16 !text-xl ring-4 ring-white" />
          <input
            v-if="showUploadButton"
            ref="fileInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleFileChange"
          />
          <button
            v-if="showUploadButton"
            class="absolute bottom-0 right-0 flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white shadow-md hover:bg-primary/90"
            @click="handleAvatarClick"
          >
            <Lucide icon="Camera" class="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>

    <!-- Info Section -->
    <div class="px-5 pb-5 pt-10">
      <div class="flex items-start justify-between">
        <!-- Left -->
        <div class="flex-1">
          <h3 class="text-lg font-medium text-slate-800">{{ user.name }}</h3>
          <p class="text-sm text-slate-500">@{{ user.username }}</p>

          <!-- Department and Roles -->
          <div class="mt-3 space-y-2">
            <div v-if="user?.department_id" class="flex items-center">
              <div class="mr-2 h-2 w-2 rounded-full bg-primary"></div>
              <span class="text-sm text-slate-600">
                {{ getDepartmentNameById(user.department_id) }}
              </span>
            </div>
            <div v-if="user?.roles" class="flex flex-wrap gap-1">
              <span
                v-for="role in user.roles"
                :key="role"
                class="rounded-full bg-slate-100 px-2 py-1 text-xs text-slate-600 dark:bg-darkmode-400 dark:text-slate-300"
              >
                {{ role }}
              </span>
            </div>
          </div>

          <!-- Personal Information -->
          <div class="mt-4 border-t border-slate-200/60 pt-4">
            <div class="space-y-3">
              <div
                v-for="field in userFields"
                :key="field.label"
                class="flex items-center"
              >
                <Lucide :icon="field.icon" class="h-4 w-4 text-slate-400" />
                <span class="ml-2 text-sm text-slate-500">{{ field.label }}:</span>
                <span class="ml-2 text-sm font-medium text-slate-700">
                  {{ field.value || "Không có" }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right -->
        <div v-if="showRedirectButton" class="ml-4">
          <Button
            variant="outlined"
            size="small"
            class="whitespace-nowrap"
          >
            Xem thông tin
            <Lucide icon="ArrowRight" class="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    </div>
  </div>

  <UserFormDrawer
    v-model:visible="isEditDrawerVisible"
    :user-id="user?.id"
    @success="onEditSuccess"
  />
</template>
