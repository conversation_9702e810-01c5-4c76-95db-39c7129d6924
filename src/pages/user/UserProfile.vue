<script setup lang="ts">
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";

import useUser from "@/hooks/useUser";
import { useAuthStore } from "@/stores/auth-store";

import UserProfileHeader from "./components/UserProfileHeader.vue";
import NotificationList from "@/components/Notification/NotificationList.vue";
import UserProfileTaskStats from "./components/UserProfileTaskStats.vue";

const route = useRoute();
const authStore = useAuthStore();
const currentUser = authStore.currentUser;

const { getUserById } = useUser({ autoLoad: true });

// Kiểm tra route để xác định context
const userId = computed(() => {
  const routeId = Number(route.params.id);
  return routeId || currentUser?.id || 0;
});

const user = computed(() => getUserById(userId.value));
</script>

<template>
  <div class="grid grid-cols-12 gap-6" :key="route.fullPath">
    <!-- Left Column - User Profile Card -->
    <div class="col-span-12 md:col-span-3 xl:col-span-2">
      <!-- User Profile Header with Information -->
      <div class="mt-8">
        <UserProfileHeader :user="user" />
      </div>
    </div>

    <!-- Right Column - Performance Chart -->
    <div class="col-span-12 md:col-span-9 xl:col-span-10">
      <div class="mt-8">
        <div class="flex h-10 items-center">
          <h2 class="mr-5 truncate text-lg font-medium">Thông báo</h2>
        </div>
        <Card v-if="user?.id" class="mt-5">
          <template #content>
            <NotificationList
              hide-title
              max-height="500px"
              :embedded="true"
              :pagination-mode="'paginator'"
              :page-size="10"
            />
          </template>
        </Card>
      </div>
    </div>

    <!-- Task Stats Section - Full Width -->
    <div class="col-span-12 my-8">
      <UserProfileTaskStats :user-id="user?.id" v-if="user?.id" />
    </div>
  </div>
</template>
