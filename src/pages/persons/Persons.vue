<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <div class="flex space-x-3 divide-x divide-solid">
        <Button
          severity="primary"
          class="text-sm"
          icon="pi pi-plus"
          label="Thêm mới"
          @click="handleOpenPersonFormModal"
        />
        <PersonFilterPresetComp v-model="currentPreset" class="pl-3" />
      </div>
    </template>

    <template #right-toolbar>
      <div class="flex items-center gap-2">
        <Button
          icon="pi pi-file-excel"
          v-tooltip.left="'Xuất dữ liệu'"
          :loading="personStore.exportLoading"
          @click="() => personStore.triggerPersonExport()"
        />
        <PopSetting title="Tuỳ chỉnh" setting-key="persons" />
      </div>
    </template>

    <template #footer>
      <div class="relative flex h-[40px] w-full items-center justify-center">
        <div class="flex items-center gap-4">
          <span class="whitespace-nowrap text-sm font-normal">
            {{ paginatorText }}
          </span>
          <Paginator
            :first="(personStore.pagination.page - 1) * personStore.pagination.pageSize"
            :rows="personStore.pagination.pageSize"
            :totalRecords="personStore.totalPersons"
            :rowsPerPageOptions="[30, 50, 100]"
            @page="handlePageChange"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
            :pt="{
              root: { class: '!bg-transparent !p-0' },
              pcCurrentPageReport: { root: { class: '!text-sm !font-normal !px-2' } },
              pcRowsPerPageDropdown: { root: { class: 'h-8' }, input: { class: '!text-sm' } },
              pcFirstPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcPrevPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcNextPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcLastPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
            }"
          />
        </div>
      </div>
    </template>

    <DataTable
      :columns="columns"
      :data="personStore.persons"
      :total-records="personStore.totalPersons || 0"
      :loading="personStore.loading"
      :paginator="false"
      :show-header="false"
      :hide-header-row="true"
      filterDisplay="row"
      size="small"
      :rows="personStore.pagination.pageSize"
      :filters="dataTableFilters"
      @update:filters="onFilterUpdate"
      :show-actions="{ custom: true, edit: true, delete: true }"
      :custom-action-items="getPersonActionItems"
      @on-edit="handleOpenPersonFormModal"
      @on-delete="handleDelete"
    >
      <!-- Body Slots for Data Display -->
      <template #created_at="{ data }">
        <DateTime v-if="data.created_at" :time="data.created_at" size="sm" />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #deal_name="{ data }">
        <div class="flex items-center gap-1">
          <i class="pi pi-shopping-cart text-lime-500" />
          <span
            v-if="data.deal?.name"
            class="whitespace-normal break-words border-none p-0 shadow-none"
            >{{ data.deal?.name }}</span
          >
          <span v-else class="italic text-gray-500">Deal chưa đặt tên</span>
          <State v-if="data.deal?.state" :state="data.deal?.state" size="sm" :shine="false" />
        </div>
      </template>

      <template #track_begin="{ data }">
        <DateTime v-if="data.track_begin" :time="data.track_begin" :showTime="false" size="sm" />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #full_name="{ data }">
        <PersonCard
          :person="data"
          submit-type="new-tab"
          show-code
          show-gender
          :query-params="{ tab: 'deals' }"
        />
      </template>

      <template #person_source="{ data }">
        <TermInfo v-if="data.source_id" termType="nguon" :termId="data.source_id" />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #stage_name="{ data }">
        <StageInfo v-if="data.stage_name" :text="data.stage_name" />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #tags="{ data }">
        <EntityTagManager
          v-if="data.deal?.tags"
          :entity-id="data.deal.id"
          entity-type="deal"
          :initial-tags="data.deal.tags || []"
          size="xs"
        />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #related_users="{ data }">
        <div
          class="flex cursor-pointer items-center gap-1"
          @click.capture="(event: MouseEvent) => toggleUserPopover(event, data)"
        >
          <UserAvatarGroup
            :users="combineUsers(data.deal?.deal_assignment || [])"
            animationStyle="lift"
            size="small"
          />
          <span
            v-if="!data.deal?.deal_assignment || data.deal.deal_assignment.length === 0"
            class="italic text-gray-400"
            >-</span
          >
        </div>
      </template>

      <!-- Filter Slots for Custom Filter Types -->
      <template #person_source.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <!-- Use Select component for source filtering -->
          <Select
            :key="'person-filter-source-select'"
            v-model="sourceFilterModel"
            :options="getBundleTerms('nguon')"
            optionValue="id"
            optionLabel="name"
            placeholder="Chọn nguồn KH"
            @update:modelValue="debouncedApplyFilters()"
            filter
            fluid
            class="p-column-filter flex h-10 items-center"
          />
          <!-- Optional: Keep clear button or rely on Select's showClear -->
          <Button
            v-if="personStore.filters.sourceId"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="personStore.clearFilterByKey('person_source')"
            aria-label="Clear Source Filter"
          />
        </div>
      </template>

      <!-- <template #stage_name.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <PipelineStageSelect
            :key="'person-filter-stage'"
            v-model="stageFilterModel"
            placeholder="Lọc stage"
            selectionMode="single"
            @update:modelValue="debouncedApplyFilters()"
            class="p-column-filter h-10 w-full flex-grow"
            :pipeline-id="2"
            clearable
          />
          <Button
            v-if="personStore.filters.stageId"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="personStore.clearFilterByKey('stage_name')"
            aria-label="Clear Stage Filter"
          />
        </div>
      </template> -->

      <template #tags.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <TagCategorySelect
            :key="'person-filter-tags'"
            v-model="tagFilterModel"
            @update:modelValue="debouncedApplyFilters()"
            placeholder="Lọc tag"
            class="p-column-filter !h-10 w-full flex-grow"
          />
          <Button
            v-if="personStore.filters.tagIds && personStore.filters.tagIds.length > 0"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="personStore.clearFilterByKey('tags')"
            aria-label="Clear Tags Filter"
          />
        </div>
      </template>

      <template #related_users.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <UserMultiAssign
            :key="'person-filter-users'"
            use-prime-vue-input
            v-model="userFilterModel"
            @update:modelValue="debouncedApplyFilters()"
            show-inactive-users
            :max-display="1"
            placeholder="Chọn người liên quan"
            class="p-column-filter !h-10 w-full flex-grow"
          />
          <Button
            v-if="personStore.filters.relatedUsers && personStore.filters.relatedUsers.length > 0"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="personStore.clearFilterByKey('related_users')"
            aria-label="Clear Related Users Filter"
          />
        </div>
      </template>
    </DataTable>

    <!-- Popover for User Details -->
    <Popover ref="op" :pt="{ content: { class: 'p-1 max-w-[400px]' } }">
      <TrackUserGroup
        v-if="selectedRowData"
        :deal-assignment="selectedRowData.deal?.deal_assignment"
      />
    </Popover>

    <!-- Person Add/Edit Modal Instance -->
    <PersonAddModal
      ref="personFormModalRef"
      :person-id="personIdToEdit"
      @person-added="refreshPersons"
      @person-updated="refreshPersons"
    />

    <!-- Deal Rating Dialog Instance -->
    <DealRatingDialog
      v-model:visible="isRatingDialogVisible"
      :deal="selectedDealForRating"
      @save="handleSaveRating"
    />
  </ContentWithFixedToolbar>
</template>

<script setup lang="ts">
import { useDebounceFn } from "@vueuse/core";
import type { MenuItem } from "primevue/menuitem";
import { PageState } from "primevue/paginator";
import {
  computed,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch,
  defineAsyncComponent,
  nextTick,
} from "vue";

import type { DealResponse, PersonResponse, UserShort } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { DataTable } from "@/components/DataTable";
import { PersonCard } from "@/components/Person";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import PopSetting from "@/components/Settings/PopSetting.vue";
import TagCategorySelect from "@/components/Tags/TagCategorySelect.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import UserMultiAssign from "@/components/User/UserMultiAssign.vue";
import { personColumns, createDefaultPersonFilters } from "@/constants/columns/person-columns";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import { PersonFilterPreset, usePersonDatatableStore } from "@/stores/person-datatable-store";
import EntityTagManager from "@/components/Tags/EntityTagManager.vue";
import { PERSON_FILTER_PRESETS } from "@/stores/person-datatable-store";
import PersonFilterPresetComp from "@/components/Person/PersonFilterPreset.vue";
import TermInfo from "@/components/InfoText/TermInfo.vue";
import useTerm from "@/hooks/useTerm";
import StageInfo from "@/components/InfoText/StageInfo.vue";
import State from "@/base-components/State.vue";
import { useModalControl } from "@/composables/useModalControl";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import usePerson from "@/hooks/usePerson";
import useDeal, { RatingUpdatePayload } from "@/hooks/useDeal";
import { useToastStore } from "@/stores/toast-store";

// Async load the modal component
const PersonAddModal = defineAsyncComponent(
  () => import("@/components/Person/PersonFormModal.vue"),
);
const TrackUserGroup = defineAsyncComponent(() => import("@/components/Track/TrackUserGroup.vue"));
const DealRatingDialog = defineAsyncComponent(
  () => import("@/pages/customer/components/DealsTab/DealRatingDialog.vue"),
);

const personStore = usePersonDatatableStore();
const columns = personColumns;
const currentPreset = ref<PersonFilterPreset>(PERSON_FILTER_PRESETS.ALL);
const dataTableFilters = reactive(createDefaultPersonFilters());
const { confirm } = useConfirmTippy();
const { deletePerson } = usePerson();
const { addDealUserRating, updateDealUserRating } = useDeal({ useStore: false });
const toastStore = useToastStore();
// --- Modal State & Control ---
const { modalRef: personFormModalRef, openModal: openPersonFormModal } = useModalControl();
const personIdToEdit = ref(0);

// --- Popover State & Ref (for related users) ---
const op = ref<any>(null);
const selectedRowData = ref<any>(null);

// --- Rating Dialog State ---
const isRatingDialogVisible = ref(false);
const selectedDealForRating = ref<DealResponse | null>(null);

// --- Helper function to sync filters ---
function syncStoreFiltersToDataTable() {
  const storeFilters = personStore.filters;
  const localFilters = createDefaultPersonFilters();

  localFilters.created_at.value = storeFilters.createdDateRange?.start
    ? [
        new Date(storeFilters.createdDateRange.start),
        storeFilters.createdDateRange.end
          ? new Date(storeFilters.createdDateRange.end)
          : new Date(storeFilters.createdDateRange.start),
      ]
    : null;

  localFilters.track_begin.value = storeFilters.presetDateRange?.start
    ? [
        new Date(storeFilters.presetDateRange.start),
        storeFilters.presetDateRange.end
          ? new Date(storeFilters.presetDateRange.end)
          : new Date(storeFilters.presetDateRange.start),
      ]
    : null;

  localFilters.full_name.value = storeFilters.searchString;
  localFilters.deal_name.value = storeFilters.dealName;
  Object.assign(dataTableFilters, localFilters);
}

// --- Watchers ---
watch(
  () => personStore.filters.createdDateRange,
  (newVal) => {
    dataTableFilters.created_at.value = newVal?.start
      ? [new Date(newVal.start), newVal.end ? new Date(newVal.end) : new Date(newVal.start)]
      : null;
  },
  { deep: true },
);

watch(
  () => personStore.filters.presetDateRange,
  (newVal) => {
    dataTableFilters.track_begin.value = newVal?.start
      ? [new Date(newVal.start), newVal.end ? new Date(newVal.end) : new Date(newVal.start)]
      : null;
  },
  { deep: true },
);

watch(
  () => personStore.filters.searchString,
  (newVal) => {
    dataTableFilters.full_name.value = newVal;
  },
);

watch(
  () => personStore.filters.dealName,
  (newVal) => {
    dataTableFilters.deal_name.value = newVal;
  },
);

// --- Computed Models for Custom Filters ---
const { getBundleTerms } = useTerm();

const sourceFilterModel = computed<number | null>({
  get: () => personStore.filters.sourceId,
  set: (value: number | null) => {
    personStore.filters.sourceId = value;
    debouncedApplyFilters();
  },
});

const stageFilterModel = computed<number | undefined>({
  get: () => personStore.filters.stageId ?? undefined,
  set: (value) => {
    personStore.filters.stageId = value ?? null;
  },
});

const tagFilterModel = computed<number[] | null>({
  get: () => personStore.filters.tagIds,
  set: (value) => {
    personStore.filters.tagIds = value && value.length > 0 ? value : null;
  },
});

const userFilterModel = computed<UserShort[]>({
  get: () => {
    return personStore.filters.relatedUsers ?? [];
  },
  set: (selectedUsers: UserShort[] | null | undefined) => {
    personStore.filters.relatedUsers =
      selectedUsers && selectedUsers.length > 0 ? selectedUsers : null;
  },
});

const debouncedApplyFilters = useDebounceFn(() => {
  personStore.applyFilters();
}, 500);

// --- Filter Update Handler (from DataTable) ---
const onFilterUpdate = (newFilters: any) => {
  const newStartDate = newFilters.created_at?.value?.[0];
  const newEndDate = newFilters.created_at?.value?.[1];
  personStore.filters.createdDateRange = newStartDate
    ? { start: newStartDate, end: newEndDate ?? newStartDate }
    : { start: null, end: null };

  const newPresetStartDate = newFilters.track_begin?.value?.[0];
  const newPresetEndDate = newFilters.track_begin?.value?.[1];
  personStore.filters.presetDateRange = newPresetStartDate
    ? { start: newPresetStartDate, end: newPresetEndDate ?? newPresetStartDate }
    : { start: null, end: null };

  personStore.filters.searchString = newFilters.full_name?.value || null;
  personStore.filters.dealName = newFilters.deal_name?.value || null;

  debouncedApplyFilters();
};

const refreshPersons = () => {
  personStore.applyFilters();
};

// --- Combine Users Helper ---
const combineUsers = (dealAssignment: any[]) => {
  const users: any[] = [];
  if (Array.isArray(dealAssignment)) {
    const mappedUsers = dealAssignment
      .filter((u) => u && u.user_id)
      .map((u) => ({
        ...u,
        id: u.user_id,
      }));
    users.push(...mappedUsers);
  }
  return Array.from(new Map(users.map((user) => [user.id, user])).values());
};

// --- Popover Toggle Function ---
const toggleUserPopover = (event: MouseEvent, data: any) => {
  selectedRowData.value = data;
  op.value?.toggle(event);
};

// --- Pagination ---
const paginatorText = computed(() => {
  const total = personStore.totalPersons || 0;
  if (total === 0) return "0 mục";
  const first = (personStore.pagination.page - 1) * personStore.pagination.pageSize + 1;
  const last = Math.min(first + personStore.pagination.pageSize - 1, total);
  return `Hiển thị ${first} - ${last} của ${total} mục`;
});

const handlePageChange = (event: PageState) => {
  const newPage = event.page + 1;
  const newPageSize = event.rows;

  if (personStore.pagination.pageSize !== newPageSize) {
    personStore.pagination.setPageSize(newPageSize);
  } else if (personStore.pagination.page !== newPage) {
    personStore.pagination.goToPage(newPage);
  }
};

// --- Modal Handling ---
const handleOpenPersonFormModal = async (data?: any) => {
  personIdToEdit.value = data?.id ?? 0;
  await nextTick();
  openPersonFormModal();
};

const handleDelete = async (data: PersonResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa khách hàng",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deletePerson({ id: data.id, full_name: data.full_name });
      refreshPersons();
    },
  });
};

// --- Actions ---
const getPersonActionItems = (data: any): MenuItem[] => {
  const items: MenuItem[] = [];

  if (data?.deal?.id) {
    items.push({
      label: "Đánh giá Deal",
      icon: "pi pi-star",
      command: () => openRatingDialog(data), // Call the rating dialog function
    });
  }

  return items;
};

const openRatingDialog = (personData: any) => {
  if (personData?.deal?.id) {
    selectedDealForRating.value = personData.deal as DealResponse;
    isRatingDialogVisible.value = true;
  }
};

const handleSaveRating = async (ratingsToSave: RatingUpdatePayload[]) => {
  if (!selectedDealForRating.value || !ratingsToSave || ratingsToSave.length === 0) {
    isRatingDialogVisible.value = false;
    return;
  }

  try {
    const promises = ratingsToSave.map((ratingData) => {
      if (ratingData.existing_rating_id) {
        return updateDealUserRating({
          id: ratingData.existing_rating_id,
          rating: ratingData.rating,
        });
      } else {
        return addDealUserRating({
          deal_user_id: ratingData.deal_user_id,
          category: ratingData.category,
          rating: ratingData.rating,
        });
      }
    });

    await Promise.all(promises);
    toastStore.success({ message: "Lưu đánh giá thành công." });
    await personStore.refresh();
  } catch (error) {
    console.error("Failed to save ratings:", error);
    toastStore.error({ message: "Lưu đánh giá thất bại." });
  } finally {
    isRatingDialogVisible.value = false;
  }
};

// --- Lifecycle ---
onMounted(() => {
  if (!personStore.persons || personStore.persons.length === 0) {
    personStore.loadInitialData().then(() => {
      syncStoreFiltersToDataTable();
    });
  } else {
    syncStoreFiltersToDataTable();
  }
});

onBeforeUnmount(() => {
  personStore.$dispose();
});
</script>
