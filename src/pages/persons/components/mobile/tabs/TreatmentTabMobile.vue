<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import useAttachment from "@/hooks/useAttachment";
import useMetadataParser from "@/hooks/useAttachmentDataParser";
import Empty from "@/base-components/Empty";
import DataView from "primevue/dataview";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import AttachmentItem from "@/components/Attachment/AttachmentItem.vue";
import RootAttachment from "@/components/Attachment/RootAttachment.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";

const props = defineProps<{
  personId: number;
}>();

const { listAttachments, attachments } = useAttachment();
const {
  processAttachments,
  metaDataByAttachment,
  teethDataByAttachment,
  getSafeParticipants,
  getTotalPrice,
} = useMetadataParser();

const showProducts = ref(false);
const selectedDate = ref<string | null>(null);
const isLoading = ref(false);

onMounted(async () => {
  await reload();
});

const reload = async () => {
  isLoading.value = true;
  try {
    await listAttachments({ filter: { person_id: props.personId } });
    processAttachments(attachments.value);
  } finally {
    isLoading.value = false;
  }
};

const availableDates = computed(() => {
  const dates = attachments.value.map((item) => {
    return new Date(item.created_at).toISOString().split("T")[0];
  });
  return [...new Set(dates)].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
});

const filteredAttachments = computed(() => {
  let filtered = attachments.value;

  if (!showProducts.value) {
    filtered = filtered.filter((item) => item.kind !== "product");
  }

  if (selectedDate.value) {
    filtered = filtered.filter(
      (item) => new Date(item.created_at).toISOString().split("T")[0] === selectedDate.value,
    );
  }

  return filtered;
});

const clearDateFilter = () => {
  selectedDate.value = null;
};

const hasNextOperations = (item: any) => {
  return (
    metaDataByAttachment.value[item.id]?.next_operation &&
    Object.keys(metaDataByAttachment.value[item.id]?.next_operation || {}).length > 0
  );
};
</script>

<template>
  <div class="p-3">
    <!-- Date filter -->
    <div class="mb-3 flex flex-wrap items-center justify-between gap-2">
      <div class="flex flex-wrap items-center gap-2">
        <span class="text-sm font-medium">Ngày tạo:</span>
        <div class="relative">
          <Select
            v-model="selectedDate"
            :options="availableDates"
            class="w-40"
            placeholder="Tất cả"
            size="small"
            :showClear="true"
            @clear="clearDateFilter"
          >
            <template #option="slotProps">
              {{ new Date(slotProps.option).toLocaleDateString("vi-VN") }}
            </template>
            <template #value="slotProps">
              {{
                slotProps.value ? new Date(slotProps.value).toLocaleDateString("vi-VN") : "Tất cả"
              }}
            </template>
          </Select>
        </div>
      </div>
      <div class="flex flex-wrap items-center gap-2">
        <span class="text-sm font-medium">Hiển thị sản phẩm:</span>
        <ToggleSwitch v-model="showProducts" />
      </div>
    </div>

    <!-- Treatment list -->
    <div v-if="isLoading" class="flex justify-center p-5">
      <i class="pi pi-spinner animate-spin text-2xl" />
    </div>
    <div v-else-if="filteredAttachments.length === 0" class="p-3">
      <Empty />
    </div>

    <DataView
      v-else
      :value="filteredAttachments"
      layout="list"
      :paginator="false"
      :rows="100"
      dataKey="id"
    >
      <template #list="{ items }">
        <div class="flex flex-col gap-3">
          <div
            v-for="item in items"
            :key="item.id"
            class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
          >
            <!-- Header with date and ID -->
            <div class="flex w-full items-center border-b border-gray-100 pb-2">
              <div class="flex w-full items-center justify-between gap-2">
                <DateTime :time="item.created_at" size="sm" />
                <span class="text-xs font-medium text-slate-400">
                  <i class="pi pi-hashtag pr-1 text-xs" />{{ item.id }}
                </span>
              </div>
            </div>

            <!-- Main content -->
            <div class="py-2">
              <!-- Operations/Products -->
              <div class="mb-2">
                <AttachmentItem
                  v-if="metaDataByAttachment[item.id]?.operation"
                  :key="item.id"
                  :operations="metaDataByAttachment[item.id].operation"
                  :parent="item.parent"
                  :show-operations="true"
                  :teeth-data="teethDataByAttachment[item.id] || {}"
                  hide-default-operations
                />
                <RootAttachment v-else :attachment="item" />
              </div>
            </div>

            <!-- Footer with participants -->
            <div class="flex flex-col gap-2 border-t border-gray-100 pt-2">
              <!-- Next operations if available -->
              <div v-if="hasNextOperations(item)" class="flex items-center justify-between gap-2">
                <span class="text-sm text-gray-600">Dự kiến:</span>
                <div class="flex flex-wrap gap-1">
                  <Chip
                    v-for="(operationName, operationId) in metaDataByAttachment[item.id]
                      .next_operation || {}"
                    :key="operationId"
                    :label="operationName"
                    class="border"
                  />
                </div>
              </div>

              <!-- Price for products -->
              <div
                v-else-if="item.kind === 'product'"
                class="flex items-center justify-between gap-2"
              >
                <span class="text-sm text-gray-600">Giá:</span>
                <Money :amount="getTotalPrice(item)" :show-icon="false" class="font-semibold" />
              </div>

              <!-- Phụ trách -->

              <div class="flex flex-wrap items-center justify-between gap-2">
                <span class="text-sm text-gray-600">Phụ trách:</span>
                <UserAvatarGroup
                  :users="[item.user_id, ...getSafeParticipants(item.id)]"
                  :expand="true"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataView>
  </div>
</template>
