<template>
  <div class="p-6 bg-white rounded-lg shadow-sm">
    <h2 class="text-xl font-semibold mb-4">DataTable V2 URL Parameters Test</h2>
    
    <!-- URL State Display -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-medium mb-2">Current URL State</h3>
      <div class="space-y-2 text-sm">
        <div><strong>Has URL State:</strong> {{ hasUrlState() }}</div>
        <div><strong>Current URL:</strong> {{ currentUrl }}</div>
        <div v-if="urlStateData" class="mt-2">
          <strong>Decoded State:</strong>
          <pre class="mt-1 p-2 bg-white rounded text-xs overflow-auto">{{ JSON.stringify(urlStateData, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- Test Controls -->
    <div class="mb-6 space-y-4">
      <h3 class="text-lg font-medium">Test Controls</h3>
      
      <div class="flex gap-2">
        <button 
          @click="testFilter"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test Filter (Name: "test")
        </button>
        
        <button 
          @click="testPagination"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test Pagination (Page 2)
        </button>
        
        <button 
          @click="testSorting"
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Test Sorting (Name ASC)
        </button>
        
        <button 
          @click="clearState"
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear URL State
        </button>
      </div>
    </div>

    <!-- DataTable -->
    <DataTableV2
      title="Products with URL Persistence"
      :data="products"
      :loading="loading"
      :total-records="totalRecords"
      lazy
      paginator
      :rows="rows"
      v-model:filters="filters"
      filterDisplay="row"
      @page="onLazyLoad"
      @filter="onLazyLoad"
      @sort="onLazyLoad"
      size="small"
      striped-rows
    >
      <!-- Name Column with Filter -->
      <Column 
        field="name" 
        header="Tên sản phẩm"
        :showFilterMenu="false"
        filterMatchMode="contains"
      >
        <template #filter="{ filterModel, filterCallback }">
          <InputText
            v-model="filterModel.value"
            @input="filterCallback()"
            placeholder="Tìm theo tên"
            class="w-full text-sm"
          />
        </template>
        <template #body="{ data }">
          <div class="font-medium">{{ data.name }}</div>
        </template>
      </Column>

      <!-- Type Column with Filter -->
      <Column 
        field="type" 
        header="Loại"
        :showFilterMenu="false"
        filterMatchMode="equals"
      >
        <template #filter="{ filterModel, filterCallback }">
          <Select
            v-model="filterModel.value"
            @change="filterCallback()"
            :options="[...PRODUCT_TYPE_OPTIONS]"
            optionLabel="name"
            optionValue="value"
            placeholder="Chọn loại"
            class="w-full text-sm"
            :showClear="true"
          />
        </template>
        <template #body="{ data }">
          <Term bundle="product_type" :term="data.type" />
        </template>
      </Column>

      <!-- Status Column with Filter -->
      <Column 
        field="status" 
        header="Trạng thái"
        :showFilterMenu="false"
        filterMatchMode="equals"
      >
        <template #filter="{ filterModel, filterCallback }">
          <Select
            v-model="filterModel.value"
            @change="filterCallback()"
            :options="statusOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="Chọn trạng thái"
            class="w-full text-sm"
            :showClear="true"
          />
        </template>
        <template #body="{ data }">
          <span :class="data.status === 2 ? 'text-green-600' : 'text-red-600'">
            {{ data.status === 2 ? 'Active' : 'Inactive' }}
          </span>
        </template>
      </Column>

      <!-- Price Column -->
      <Column field="price" header="Giá / đơn vị" sortable>
        <template #body="{ data }">
          <Money :amount="data.price" />
        </template>
      </Column>
    </DataTableV2>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRoute } from "vue-router";

import type { Product } from "@/api/bcare-types-v2";
import { PRODUCT_TYPE_OPTIONS, ProductType } from "@/api/product";
import Money from "@/base-components/Money.vue";
import Term from "@/base-components/Term/Term.vue";
import DataTableV2 from "@/components/DataTableV2/DataTableV2.vue";
import Column from "primevue/column";
import InputText from "primevue/inputtext";
import Select from "primevue/select";

import { useLazyDataTable } from "@/composables/useLazyDataTable";
import { createProductDataTableConfig } from "@/utils/productApiMapping";
import useProduct from "@/hooks/useProduct";

// API hook
const { listProducts } = useProduct();

// DataTable with URL persistence
const { 
  data: products, 
  totalRecords, 
  loading, 
  filters, 
  rows, 
  onLazyLoad,
  clearUrlState,
  getCurrentUrlState,
  hasUrlState
} = useLazyDataTable<Product, any>({
  ...createProductDataTableConfig(listProducts),
  
  // Enable URL persistence for testing
  enableUrlPersistence: true,
  urlStateKey: 'test-products',
  persistedFields: ['filters', 'pagination', 'sorting'],
  urlDebounceMs: 300
});

// Status options for filter dropdown
const statusOptions = [
  { name: "Active", value: 2 },
  { name: "Inactive", value: 1 },
];

// URL state monitoring
const route = useRoute();
const currentUrl = computed(() => window.location.href);
const urlStateData = computed(() => getCurrentUrlState());

// Test functions
const testFilter = () => {
  filters.value = {
    ...filters.value,
    name: { value: 'test', matchMode: 'contains' }
  };
  
  // Trigger filter event
  onLazyLoad({
    first: 0,
    rows: rows.value,
    page: 0,
    pageCount: 0,
    filters: filters.value,
    sortField: undefined,
    sortOrder: undefined,
    multiSortMeta: undefined,
    originalEvent: new Event('test-filter'),
    filterMatchModes: undefined
  });
};

const testPagination = () => {
  onLazyLoad({
    first: rows.value,
    rows: rows.value,
    page: 1,
    pageCount: 0,
    filters: filters.value,
    sortField: undefined,
    sortOrder: undefined,
    multiSortMeta: undefined,
    originalEvent: new Event('test-pagination'),
    filterMatchModes: undefined
  });
};

const testSorting = () => {
  onLazyLoad({
    first: 0,
    rows: rows.value,
    page: 0,
    pageCount: 0,
    filters: filters.value,
    sortField: 'name',
    sortOrder: 1,
    multiSortMeta: undefined,
    originalEvent: new Event('test-sorting'),
    filterMatchModes: undefined
  });
};

const clearState = () => {
  clearUrlState();
  // Reset filters
  filters.value = {};
  // Reload with default state
  onLazyLoad({
    first: 0,
    rows: rows.value,
    page: 0,
    pageCount: 0,
    filters: {},
    sortField: undefined,
    sortOrder: undefined,
    multiSortMeta: undefined,
    originalEvent: new Event('clear-state'),
    filterMatchModes: undefined
  });
};
</script>
