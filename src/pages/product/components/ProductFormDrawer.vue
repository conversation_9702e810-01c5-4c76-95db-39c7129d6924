<template>
  <Drawer
    :closable="false"
    :modal="true"
    :style="{ width: '50rem' }"
    :visible="visible"
    blockScroll
    position="right"
    @hide="onClose"
    @update:visible="$emit('update:visible', $event)"
    :header="isEdit ? 'C<PERSON><PERSON> nh<PERSON>t sản phẩm' : 'Thêm sản phẩm'"
  >
    <form @submit.prevent="onSubmit">
      <div class="space-y-6">
        <BasicInformation v-model:formData="formData" :errors="errors" />
        <Divider class="!my-6" />
        <PricingInformation v-model:formData="formData" :errors="errors" />
        <Divider class="!my-6" />
        <CategoryInformation v-model:formData="formData" :errors="errors" />
      </div>
    </form>

    <template #footer>
      <Button icon="pi pi-times" label="Hủy" outlined severity="danger" @click="onClose" />
      <Button autofocus icon="pi pi-save" label="Lưu" @click="onSubmit" />
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { ProductAddRequest } from "@/api/bcare-types-v2";
import { useFormValidation } from "@/composables/useFormValidation";
import useProduct from "@/hooks/useProduct";

import BasicInformation from "./ProductFormBasicInfo.vue";
import CategoryInformation from "./ProductFormCategory.vue";
import PricingInformation from "./ProductFormPricing.vue";

interface Props {
  visible: boolean;
  productId?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const { addProduct, updateProduct, getProduct } = useProduct();
const isEdit = computed(() => !!props.productId);

const formData = ref<ProductAddRequest>({
  name: "",
  code: "",
  description: "",
  price: 0,
  type: "product",
  status: 2, // Default to Active
  quantity: 0,
  sku: "",
  unit_id: 0,
  group_id: 0,
  category_id: 0,
  collection: [],
  operations: [],
});

const { errors, validateForm } = useFormValidation({
  name: [
    { validate: (v) => !!v && v.length >= 2, message: "Tên sản phẩm phải có ít nhất 2 ký tự" },
  ],
  code: [{ validate: (v) => !!v, message: "Mã sản phẩm không được để trống" }],
  price: [{ validate: (v) => v >= 0, message: "Giá không hợp lệ" }],
  unit_id: [{ validate: (v) => v > 0, message: "Vui lòng chọn đơn vị tính" }],
  category_id: [{ validate: (v) => v > 0, message: "Vui lòng chọn danh mục" }],
});

const resetForm = () => {
  formData.value = {
    name: "",
    code: "",
    description: "",
    price: 0,
    type: "product",
    status: 2,
    quantity: 0,
    sku: "",
    unit_id: 0,
    group_id: 0,
    category_id: 0,
    collection: [],
    operations: [],
  };

  Object.keys(errors.value).forEach((key) => {
    errors.value[key] = "";
  });
};

watch(
  () => props.productId,
  async (newValue) => {
    resetForm();
    if (newValue) {
      const product = await getProduct({ id: newValue });
      if (product) {
        const {
          name,
          code,
          description,
          price,
          type,
          status,
          quantity,
          sku,
          unit_id,
          group_id,
          category_id,
          collection,
          operations,
        } = product;

        formData.value = {
          name,
          code,
          description,
          price,
          type,
          status,
          quantity,
          sku,
          unit_id,
          group_id,
          category_id,
          collection: collection || [],
          operations: operations?.map((op) => ({ operation_id: op.id })) || [],
        };
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.visible,
  (newValue) => {
    if (!newValue) {
      resetForm();
    }
  },
);

const onSubmit = async () => {
  if (!validateForm(formData.value)) return;

  try {
    if (isEdit.value) {
      const res = await updateProduct({ id: props.productId!, ...formData.value });
      if (res) {
        emit("success");
        onClose();
      }
    } else {
      const res = await addProduct(formData.value);
      if (res) {
        emit("success");
        onClose();
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const onClose = () => {
  emit("update:visible", false);
  resetForm();
};
</script>
