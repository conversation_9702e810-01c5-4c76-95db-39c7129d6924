<template>
  <ProductFormDrawer
    v-model:visible="showProductForm"
    :product-id="selectedProductId"
    @success="handleFormSuccess"
  />

  <div class="intro-y mt-5">
    <DataTableV2
      title="Danh sách sản phẩm"
      :data="products"
      :loading="loading"
      :total-records="totalRecords"
      lazy
      paginator
      :rows="rows"
      v-model:filters="filters"
      @page="onLazyLoad"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> Danh sách sản phẩm ({{ totalRecords }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button severity="primary" icon="pi pi-plus" label="Thêm sản phẩm" @click="handleCreate" />
      </template>

      <!-- Column definitions -->
      <Column
        field="name"
        header="Tên sản phẩm"
        :showFilterMenu="false"
        filterField="name"
        :filterMatchModeOptions="[{ label: 'Contains', value: 'contains' }]"
      >
        <template #filter="{ filterModel, filterCallback }">
          <InputText
            v-model="filterModel.value"
            type="text"
            @input="filterCallback()"
            placeholder="Tìm theo tên"
            class="p-column-filter"
          />
        </template>
        <template #body="{ data }">
          <div class="flex flex-col">
            <div
              class="flex items-center gap-2"
              :class="{ 'cursor-pointer': data.operations?.length }"
              @click="data.operations?.length ? toggleOperations(data.id) : undefined"
            >
              <div class="flex items-center gap-2">
                <div class="font-medium" v-tooltip="data.description" v-if="data.description">
                  {{ data.name }}
                </div>
                <div class="font-medium" v-else>
                  {{ data.name }}
                </div>
                <div class="flex items-center gap-1" v-if="data.collection?.length">
                  <i
                    class="pi pi-star-fill text-blue-500"
                    v-if="data.collection.includes('primary')"
                  ></i>
                  <i
                    class="pi pi-star text-blue-500"
                    v-if="data.collection.includes('secondary')"
                  ></i>
                </div>
              </div>
              <Button
                v-if="data.operations?.length"
                type="button"
                icon="pi pi-chevron-down"
                :class="{
                  'flex h-5 w-5 items-center !p-0': true,
                  'rotate-180 transform transition-transform duration-200': expandedProducts[data.id],
                }"
                text
                rounded
                size="small"
              />
            </div>
            <div v-if="!expandedProducts[data.id]" class="flex items-center gap-1 text-slate-500">
              <span class="text-sm">#{{ data.code }}</span>
              <span class="text-sm" v-if="data.sku">
                <i class="pi pi-circle-fill px-2 align-middle text-[3px] text-slate-400" />
                <Tag severity="warning">{{ data.sku }}</Tag>
              </span>
            </div>
            <div
              v-if="data.operations?.length"
              class="transform transition-all duration-200 ease-in-out"
              :class="[
                expandedProducts[data.id]
                  ? 'translate-y-0 opacity-100'
                  : 'hidden -translate-y-2 opacity-0',
              ]"
            >
              <ProductOperationTree :operations="data.operations" />
            </div>
          </div>
        </template>
      </Column>

      <Column field="price" header="Giá / đơn vị" :showFilterMenu="false">
        <template #body="{ data }">
          <div class="flex items-baseline gap-1">
            <Money :amount="data.price" class="font-medium" />
            <span class="text-sm text-slate-500" v-if="data.unit_id">
              / {{ getUnitName(data.unit_id) }}
            </span>
          </div>
        </template>
      </Column>

      <Column
        field="type"
        header="Loại"
        :showFilterMenu="false"
        filterField="type"
        :filterMatchModeOptions="[{ label: 'Equals', value: 'equals' }]"
      >
        <template #filter="{ filterModel, filterCallback }">
          <Select
            v-model="filterModel.value"
            @change="filterCallback()"
            :options="PRODUCT_TYPE_OPTIONS"
            optionLabel="name"
            optionValue="value"
            placeholder="Chọn loại"
            class="p-column-filter"
            showClear
          />
        </template>
        <template #body="{ data }">
          <Term variant="soft" :color-key="getProductTypeName(data.type)" size="sm">
            {{ getProductTypeName(data.type) }}
          </Term>
        </template>
      </Column>

      <Column
        field="status"
        header="Trạng thái"
        :showFilterMenu="false"
        filterField="status"
        :filterMatchModeOptions="[{ label: 'Equals', value: 'equals' }]"
      >
        <template #filter="{ filterModel, filterCallback }">
          <Select
            v-model="filterModel.value"
            @change="filterCallback()"
            :options="statusOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="Chọn trạng thái"
            class="p-column-filter"
            showClear
          />
        </template>
        <template #body="{ data }">
          <div class="flex items-center gap-2">
            <i
              :class="[
                data.status === 2 ? 'pi pi-check-circle text-green-500' : 'pi pi-ban text-gray-500',
              ]"
            ></i>
            <span :class="[data.status === 2 ? 'text-green-600' : 'text-gray-600', 'font-medium']">
              {{ data.status === 2 ? "Active" : "Inactive" }}
            </span>
          </div>
        </template>
      </Column>

      <Column header="Thao tác" :exportable="false" style="min-width: 8rem">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button
              icon="pi pi-pencil"
              severity="secondary"
              size="small"
              @click="handleEdit(data)"
              v-tooltip.top="'Chỉnh sửa'"
            />
            <Button
              icon="pi pi-trash"
              severity="danger"
              size="small"
              @click="handleDelete(data)"
              v-tooltip.top="'Xóa'"
            />
          </div>
        </template>
      </Column>


  </div>
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, onMounted, ref, watch } from "vue";

import type { Product, ProductListResponse } from "@/api/bcare-types-v2";
import { PRODUCT_TYPE_OPTIONS, ProductType } from "@/api/product";
import Money from "@/base-components/Money.vue";
import Term from "@/base-components/Term/Term.vue";
import { type ColumnDefinition, DataTable } from "@/components/DataTable";
import { DataTableV2 } from "@/components/DataTableV2";
import Column from "primevue/column";
import InputText from "primevue/inputtext";
import Select from "primevue/select";
import Button from "primevue/button";
import Tag from "primevue/tag";
import ProductOperationTree from "@/components/Product/ProductOperationTree.vue";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useLazyDataTable } from "@/composables/useLazyDataTable";
import useProduct from "@/hooks/useProduct";
import useTerm from "@/hooks/useTerm";
import { createProductDataTableConfig } from "@/utils/productApiMapping";

import ProductFormDrawer from "./components/ProductFormDrawer.vue";

const { listProducts, deleteProduct } = useProduct({ autoLoad: false });

// State - will be replaced with useLazyDataTable
// const tableState = ref<ProductListResponse>({
//   total: 0,
//   total_page: 0,
//   products: [],
// });
// const perPage = ref<number>(10);
// const currentPage = ref(1);

// New DataTable V2 implementation
const {
  data: products,
  totalRecords,
  loading,
  filters,
  rows,
  onLazyLoad,
  error,
  isEmpty,
  hasError
} = useLazyDataTable<Product, any>({
  ...createProductDataTableConfig(listProducts)
});

// Status options for filter dropdown
const statusOptions = [
  { name: "Active", value: 2 },
  { name: "Inactive", value: 1 },
];

// Column definitions
const columns = computed<ColumnDefinition<Product>[]>(() => [
  {
    field: "name",
    header: "Tên sản phẩm",
    filterType: "text",
    filterPlaceholder: "Tìm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "price",
    header: "Giá / đơn vị",
    showFilterMenu: false,
  },

  {
    field: "type",
    header: "Loại",
    filterType: "select",
    filterPlaceholder: "Chọn loại",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: PRODUCT_TYPE_OPTIONS.map((opt) => ({
      title: opt.name,
      value: opt.value,
    })),
  },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Active", value: 2 },
      { title: "Inactive", value: 1 },
    ],
  },
]);

// Filter configurations - will be replaced with useLazyDataTable
// const filterConfigs = {
//   name: { field: "search", isFilter: false },
//   type: { field: "type", isFilter: true },
//   status: {
//     field: "status",
//     isFilter: true,
//     valueTransform: (value: string | number) => Number(value),
//   },
// };

// const defaultFilters = {
//   page: 1,
//   page_size: perPage.value,
// };

// const { filters, currentFilterPayload } = useFilterList(
//   (filters) => {
//     loadList(filters);
//   },
//   filterConfigs,
//   defaultFilters,
// );

// Handlers - will be replaced with useLazyDataTable
// const handlePageChange = async (event: { first: number; rows: number }) => {
//   try {
//     const page = Math.floor(event.first / event.rows) + 1;
//     currentPage.value = page;

//     // Gọi trực tiếp API với page mới, không thông qua useFilterList
//     const response = await listProducts({
//       ...currentFilterPayload.value,
//       page,
//       page_size: perPage.value,
//     });

//     if (response) {
//       tableState.value = response;
//     }
//   } catch (error) {
//     console.error("Error changing page:", error);
//   }
// };

// const handlePerPageChange = (newPerPage: number) => {
//   perPage.value = newPerPage;
//   loadList({ ...currentFilterPayload.value, page_size: newPerPage });
// };

// const loadList = async (filters: Record<string, any>) => {
//   try {
//     const response = await listProducts({
//       ...filters,
//       page: currentPage.value,
//       page_size: perPage.value,
//     });
//     if (response) {
//       tableState.value = response;
//     }
//   } catch (error) {
//     console.error("Error loading products:", error);
//   }
// };

const { confirm } = useConfirmTippy();

const handleDelete = async (product: Product, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa sản phẩm",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteProduct({ id: product.id });
      loadList(currentFilterPayload.value);
    },
  });
};

// Lifecycle
onMounted(() => {});

// Watchers
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

watch(
  filters,
  () => {
    currentPage.value = 1;
  },
  { deep: true },
);

// Add utility function for product type
const getProductTypeName = (type: ProductType): string => {
  return PRODUCT_TYPE_OPTIONS.find((opt) => opt.value === type)?.name || "";
};

const showProductForm = ref(false);
const selectedProductId = ref<number>();

const handleCreate = () => {
  showProductForm.value = true;
};

const handleEdit = (product: Product) => {
  selectedProductId.value = product.id;
  showProductForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};

// Reset selectedProductId when drawer closes
watch(
  () => showProductForm.value,
  (newValue) => {
    if (!newValue) {
      selectedProductId.value = undefined;
    }
  },
);

// Add unit name getter
const { getTermNameById } = useTerm();
const getUnitName = (unitId: number): string => {
  return getTermNameById("don_vi", unitId);
};

// Track expanded state
const expandedProducts = ref<Record<number, boolean>>({});

// Toggle function
const toggleOperations = (productId: number) => {
  expandedProducts.value[productId] = !expandedProducts.value[productId];
};
</script>
