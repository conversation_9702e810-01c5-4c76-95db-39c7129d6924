<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import type { PageState } from "primevue/paginator";

import DataTableFooter from "@/components/DataTable/DataTableFooter.vue";
import PersonAvatar from "@/components/Person/PersonAvatar.vue";
import SearchPeople from "@/components/Person/SearchPeople.vue";
import ReportToolbar from "@/components/Report/ReportToolbar.vue";
import OperationSelect from "@/components/Operation/OperationSelect.vue";
import MaterialSelector from "@/components/Operation/MaterialSelector.vue";

import {
  materialUsageReportFilterConfigs,
  materialUsageReportDefaultFilters,
} from "@/constants/columns/material-usage-report-columns";
import { useFilterList } from "@/hooks/useFilterList";
import { useMaterialUsageReportQuery } from "@/hooks/useMaterialUsageReportQuery";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import { getTodayDateRange } from "@/utils/time-helper";
import Empty from "@/base-components/Empty/Empty.vue";

// Types for flattened data structure
interface FlatMaterialUsageRow {
  // Person info
  person_name: string;
  person_id?: number;
  person_phone?: string;
  person_full_name?: string;

  // Operation info
  operation_name: string;

  // Material info
  material_name: string;
  quoted_quantity: number;
  used_quantity: number;
  difference: number;
  unit: string;

  // Rowspan calculations
  personRowspan?: number;
  operationRowspan?: number;
  isFirstPersonRow?: boolean;
  isFirstOperationRow?: boolean;
}

// Add props for consistency with DataTable
const props = defineProps<{
  size?: "small" | "large";
}>();

// Local pagination state
const page = ref(1);
const rowsPerPage = ref(20);
const rowsPerPageOptions = [20, 50, 100];

// Person filter state - using SearchPeople v-model pattern
const selectedPersonIds = ref<number | null>(null);

// Operation and Material filter states - using ID-based selectors
const selectedOperationId = ref<number | null>(null);
const selectedMaterialId = ref<number | null>(null);

// Initialize query hook for detail report
const { detailItems, total, fetchDetailReport, isLoading, exportReport, isExporting } = useMaterialUsageReportQuery({
  reportType: "detail",
});

// Export functionality - simplified since loading state is handled in composable
const handleExport = () => {
  exportReport(currentFilterPayload.value, "detail");
};

// Function to reset pagination
const resetPagination = () => {
  page.value = 1;
};

// Filter management using useFilterList
const { filters, currentFilterPayload } = useFilterList(
  () => {
    // Reset pagination when filters change
    resetPagination();
    loadDetailReport();
  },
  materialUsageReportFilterConfigs,
  materialUsageReportDefaultFilters,
);

// Auto-apply today's date range on component initialization
const initializeTodayDateRange = () => {
  const todayRange = getTodayDateRange();
  filters.value.dateRange = { value: todayRange };
};

// Handle page change
const handlePageChange = (event: PageState) => {
  page.value = event.page + 1; // PageState.page is 0-based, convert to 1-based
  rowsPerPage.value = event.rows;
  loadDetailReport(false);
};

// Load detail report data
const loadDetailReport = (getCount: boolean = true) => {
  const requestPayload = {
    page: page.value,
    page_size: rowsPerPage.value,
    ...currentFilterPayload.value,
  };

  fetchDetailReport(requestPayload, getCount);
};

// Watch for person selection changes (SearchPeople v-model)
watch(selectedPersonIds, (newPersonId) => {
  if (newPersonId) {
    // Apply selected person filter
    filters.value = {
      ...filters.value,
      person_id: { value: newPersonId },
    };
  } else {
    // Explicitly set person_id filter value to null so useFilterList can remove it
    filters.value = {
      ...filters.value,
      person_id: { value: null },
    };
  }
});

// Watch for operation selection changes
watch(selectedOperationId, (newOperationId) => {
  if (newOperationId) {
    filters.value = {
      ...filters.value,
      operation_id: { value: newOperationId },
    };
  } else {
    // Explicitly set operation_id filter value to null so useFilterList can remove it
    filters.value = {
      ...filters.value,
      operation_id: { value: null },
    };
  }
});

// Watch for material selection changes
watch(selectedMaterialId, (newMaterialId) => {
  if (newMaterialId) {
    filters.value = {
      ...filters.value,
      material_id: { value: newMaterialId },
    };
  } else {
    // Explicitly set material_id filter value to null so useFilterList can remove it
    filters.value = {
      ...filters.value,
      material_id: { value: null },
    };
  }
});

// Computed for toolbar filter values - only dateRange now
const toolbarFilters = computed({
  get: () => ({
    dateRange: filters.value.dateRange?.value || null,
  }),
  set: (value) => {
    filters.value = {
      ...filters.value,
      dateRange: { value: value.dateRange },
    };
  },
});

// Format number with proper decimal places
const formatNumber = (value: number): string => {
  return new Intl.NumberFormat("vi-VN", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

// Format difference with color coding
const formatDifference = (difference: number): string => {
  const color =
    difference === 0 ? "text-gray-600" : difference > 0 ? "text-red-600" : "text-green-600";
  const sign = difference > 0 ? "+" : "";
  return `<span class="${color} font-medium">${sign}${formatNumber(difference)}</span>`;
};

// Flatten nested data structure for DataTable with rowspan calculations
const flattenedData = computed<FlatMaterialUsageRow[]>(() => {
  const result: FlatMaterialUsageRow[] = [];

  detailItems.value.forEach((person) => {
    let personRowCount = 0;

    // Calculate total rows for this person
    person.operation_details.forEach((operation) => {
      personRowCount += operation.used_materials.length;
    });

    let personRowIndex = 0;

    person.operation_details.forEach((operation) => {
      const operationRowCount = operation.used_materials.length;

      operation.used_materials.forEach((material, materialIndex) => {
        const row: FlatMaterialUsageRow = {
          // Person info
          person_name: person.person_name,
          person_id: undefined, // API doesn't provide person_id in this response
          person_phone: undefined, // API doesn't provide phone in this response
          person_full_name: person.person_name,

          // Operation info
          operation_name: operation.operation_name,

          // Material info
          material_name: material.material_name,
          quoted_quantity: material.quoted_quantity,
          used_quantity: material.used_quantity,
          difference: material.difference,
          unit: material.unit,

          // Rowspan calculations
          personRowspan: personRowIndex === 0 ? personRowCount : 0,
          operationRowspan: materialIndex === 0 ? operationRowCount : 0,
          isFirstPersonRow: personRowIndex === 0,
          isFirstOperationRow: materialIndex === 0,
        };

        result.push(row);
        personRowIndex++;
      });
    });
  });

  return result;
});

// Get person full name for PersonAvatar component
const getPersonFullName = (row: FlatMaterialUsageRow) => {
  return row.person_full_name || row.person_name;
};

// Watch filter changes để debug
watch(
  () => filters.value,
  (newFilters) => {
    console.log("Filters changed:", newFilters);
  },
  { deep: true },
);

watch(
  () => currentFilterPayload.value,
  (newPayload) => {
    console.log("Filter payload changed:", newPayload);
  },
  { deep: true },
);

onMounted(() => {
  // Initialize today's date range first
  initializeTodayDateRange();
  // Note: loadDetailReport will be automatically called by useFilterList
  // when the date filter is applied via the callback function
});
</script>

<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <ReportToolbar
        title="BC Chi tiết Vật tư"
        :total-records="total"
        v-model="toolbarFilters"
        date-range-placeholder="Chọn khoảng thời gian"
        :show-search="false"
      />
    </template>

    <template #right-toolbar>
      <div class="flex items-center gap-2">
        <Button
          icon="pi pi-file-excel"
          v-tooltip.left="'Xuất dữ liệu'"
          :loading="isExporting"
          @click="handleExport"
        />
        <!-- Additional toolbar actions can be added here -->
        <slot name="toolbar-actions" />
      </div>
    </template>

    <template #footer>
      <DataTableFooter
        :totalRecords="total || 0"
        :page="page"
        :rows="rowsPerPage"
        :rowsPerPageOptions="rowsPerPageOptions"
        @pageChange="handlePageChange"
      />
    </template>

    <!-- Main content: Custom table with rowspan -->
    <!-- Header and filter row always visible -->
    <div class="relative w-full">
      <div class="overflow-x-auto">
        <table class="w-full overflow-hidden rounded-t-lg border border-surface-200 bg-surface-0">
          <!-- Table Header - consistent with DataTable styling -->
          <thead class="bg-white">
            <tr>
              <th
                class="rounded-tl-lg border-b border-surface-200 p-3 text-left text-sm font-medium text-surface-700"
              >
                Khách hàng
              </th>
              <th
                class="border-b border-surface-200 p-3 text-left text-sm font-medium text-surface-700"
              >
                Công việc thực hiện
              </th>
              <th
                class="border-b border-surface-200 p-3 text-left text-sm font-medium text-surface-700"
              >
                Vật tư
              </th>
              <th
                class="border-b border-surface-200 p-3 text-center text-sm font-medium text-surface-700"
              >
                Định mức
              </th>
              <th
                class="border-b border-surface-200 p-3 text-center text-sm font-medium text-surface-700"
              >
                Thực tế
              </th>
              <th
                class="border-b border-surface-200 p-3 text-center text-sm font-medium text-surface-700"
              >
                Chênh lệch
              </th>
              <th
                class="rounded-tr-lg border-b border-surface-200 p-3 text-center text-sm font-medium text-surface-700"
              >
                Đvt
              </th>
            </tr>

            <!-- Filter Row - clean styling without dividers -->
            <tr class="border-b">
              <!-- Person filter column -->
              <th class="relative p-3 font-normal">
                <SearchPeople
                  v-model="selectedPersonIds"
                  mode="person"
                  placeholder="Tìm khách hàng..."
                  :multiple="false"
                  class="h-full w-full"
                />
              </th>

              <!-- Operation filter column -->
              <th class="p-3 font-normal">
                <OperationSelect
                  v-model="selectedOperationId"
                  :single="true"
                  placeholder="Chọn công việc..."
                />
              </th>

              <!-- Material filter column -->
              <th class="p-3 font-normal">
                <MaterialSelector
                  v-model="selectedMaterialId"
                  :single="true"
                  placeholder="Chọn vật tư..."
                />
              </th>

              <!-- Empty filter cells for other columns -->
              <th class="p-3"></th>
              <th class="p-3"></th>
              <th class="p-3"></th>
              <th class="p-3"></th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody>
            <!-- Loading state -->
            <tr v-if="isLoading">
              <td colspan="7" class="py-8 text-center">
                <div class="space-y-4">
                  <div v-for="i in 3" :key="i" class="animate-pulse">
                    <div class="mb-4 h-16 rounded-lg bg-gray-200"></div>
                    <div class="space-y-2">
                      <div class="mx-auto h-4 w-3/4 rounded bg-gray-100"></div>
                      <div class="mx-auto h-4 w-1/2 rounded bg-gray-100"></div>
                    </div>
                  </div>
                </div>
              </td>
            </tr>

            <!-- Empty state -->
            <tr v-else-if="!flattenedData?.length">
              <td colspan="7" class="py-8 text-center">
                <Empty class="bg-white p-4" />
              </td>
            </tr>

            <!-- Data rows -->
            <tr
              v-else
              v-for="(row, index) in flattenedData"
              :key="index"
              class="border-b border-surface-100 transition-colors hover:bg-surface-50"
            >
              <!-- Person name column with rowspan -->
              <td
                v-if="row.isFirstPersonRow"
                :rowspan="row.personRowspan"
                class="px-3 py-2 align-top"
              >
                <div class="flex items-center gap-2">
                  <PersonAvatar :full-name="getPersonFullName(row)" />
                  <span class="font-medium text-surface-900">{{ row.person_name }}</span>
                </div>
              </td>

              <!-- Operation name column with rowspan -->
              <td
                v-if="row.isFirstOperationRow"
                :rowspan="row.operationRowspan"
                class="px-3 py-2 align-top"
              >
                <div class="flex items-center gap-2">
                  <i class="pi pi-cog text-sm text-primary"></i>
                  <span class="font-medium text-surface-900">{{ row.operation_name }}</span>
                </div>
              </td>

              <!-- Material name column -->
              <td class="px-3 py-2">
                <span class="font-medium text-surface-900">{{ row.material_name }}</span>
              </td>

              <!-- Quoted quantity column -->
              <td class="px-3 py-2 text-center text-surface-700">
                <span>{{ formatNumber(row.quoted_quantity) }}</span>
              </td>

              <!-- Used quantity column -->
              <td class="px-3 py-2 text-center text-surface-700">
                <span>{{ formatNumber(row.used_quantity) }}</span>
              </td>

              <!-- Difference column with color coding -->
              <td class="px-3 py-2 text-center">
                <span v-html="formatDifference(row.difference)"></span>
              </td>

              <!-- Unit column -->
              <td class="px-3 py-2 text-center text-surface-600">
                <span>{{ row.unit }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ContentWithFixedToolbar>
</template>
