<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import type { PageState } from "primevue/paginator";

import { DataTable } from "@/components/DataTable";
import DataTableFooter from "@/components/DataTable/DataTableFooter.vue";
import ReportToolbar from "@/components/Report/ReportToolbar.vue";
import {
  materialUsageSummaryColumns,
  materialUsageReportFilterConfigs,
  materialUsageReportDefaultFilters,
} from "@/constants/columns/material-usage-report-columns";
import { useFilterList } from "@/hooks/useFilterList";
import { useMaterialUsageReportQuery } from "@/hooks/useMaterialUsageReportQuery";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import { getTodayDateRange } from "@/utils/time-helper";

// Local pagination state
const page = ref(1);
const rowsPerPage = ref(20);
const rowsPerPageOptions = [20, 50, 100];

// Initialize query hook for summary report
const { summaryItems, total, fetchSummaryReport, isLoading, exportReport, isExporting } = useMaterialUsageReportQuery({
  reportType: "summary",
});

// Export functionality - simplified since loading state is handled in composable
const handleExport = () => {
  exportReport(currentFilterPayload.value, "summary");
};

// Function to reset pagination
const resetPagination = () => {
  page.value = 1;
};

// Filter management using useFilterList
const { filters, currentFilterPayload } = useFilterList(
  () => {
    // Reset pagination when filters change
    resetPagination();
    loadSummaryReport();
  },
  materialUsageReportFilterConfigs,
  materialUsageReportDefaultFilters,
);

// Auto-apply today's date range on component initialization
const initializeTodayDateRange = () => {
  const todayRange = getTodayDateRange();
  filters.value.dateRange = { value: todayRange };
};

// Handle page change
const handlePageChange = (event: PageState) => {
  page.value = event.page + 1; // PageState.page is 0-based, convert to 1-based
  rowsPerPage.value = event.rows;
  loadSummaryReport(false);
};

// Load summary report data
const loadSummaryReport = (getCount: boolean = true) => {
  const requestPayload = {
    page: page.value,
    page_size: rowsPerPage.value,
    ...currentFilterPayload.value,
  };

  fetchSummaryReport(requestPayload, getCount);
};

// Computed for toolbar filter values
const toolbarFilters = computed({
  get: () => ({
    search: filters.value.search?.value || "",
    dateRange: filters.value.dateRange?.value || null,
  }),
  set: (value) => {
    filters.value = {
      ...filters.value,
      search: { value: value.search },
      dateRange: { value: value.dateRange },
    };
  },
});

// Format number with proper decimal places
const formatNumber = (value: number): string => {
  return new Intl.NumberFormat("vi-VN", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

// Format difference with color coding
const formatDifference = (item: any): string => {
  const difference = item.difference || 0;
  const color =
    difference === 0 ? "text-gray-600" : difference > 0 ? "text-red-600" : "text-green-600";
  const sign = difference > 0 ? "+" : "";
  return `<span class="${color} font-medium">${sign}${formatNumber(difference)}</span>`;
};

onMounted(() => {
  // Initialize today's date range first
  initializeTodayDateRange();
  // Note: loadSummaryReport will be automatically called by useFilterList
  // when the date filter is applied via the callback function
});
</script>

<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <ReportToolbar
        title="BC Tổng hợp Vật tư"
        :total-records="total"
        v-model="toolbarFilters"
        search-placeholder="Tìm tên vật tư"
        date-range-placeholder="Chọn khoảng thời gian"
      />
    </template>

    <template #right-toolbar>
      <div class="flex items-center gap-2">
        <Button
          icon="pi pi-file-excel"
          v-tooltip.left="'Xuất dữ liệu'"
          :loading="isExporting"
          @click="handleExport"
        />
        <!-- Additional toolbar actions can be added here -->
        <slot name="toolbar-actions" />
      </div>
    </template>

    <template #footer>
      <DataTableFooter
        :totalRecords="total || 0"
        :page="page"
        :rows="rowsPerPage"
        :rowsPerPageOptions="rowsPerPageOptions"
        @pageChange="handlePageChange"
      />
    </template>

    <!-- Main content: DataTable -->
    <DataTable
      :columns="materialUsageSummaryColumns"
      :data="summaryItems"
      :loading="isLoading"
      :total-records="total"
      :paginator="false"
      v-model:filters="filters"
      size="small"
      :show-header="false"
      :hide-header-row="false"
      :hide-filter-row="true"
    >
      <!-- Custom column templates -->
      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-box text-blue-500"></i>
          <div>
            <div class="font-medium">{{ data.name }}</div>
            <div v-if="data.code" class="text-xs">{{ data.code }}</div>
          </div>
        </div>
      </template>

      <template #unit="{ data }">
        <span>{{ data.unit }}</span>
      </template>

      <template #total_attachment="{ data }">
        <span class="font-medium">{{ formatNumber(data.total_attachment) }}</span>
      </template>

      <template #total_quoted_quantity="{ data }">
        <span>{{ formatNumber(data.total_quoted_quantity) }}</span>
      </template>

      <template #total_used_quantity="{ data }">
        <span>{{ formatNumber(data.total_used_quantity) }}</span>
      </template>

      <template #difference="{ data }">
        <span v-html="formatDifference(data)"></span>
      </template>
    </DataTable>
  </ContentWithFixedToolbar>
</template>

<style scoped>
/* Custom styles if needed */
</style>
