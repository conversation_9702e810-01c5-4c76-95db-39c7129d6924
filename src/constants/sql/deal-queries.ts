export const DEAL_QUERY = `WITH base_deal_ids AS (SELECT d.id,
                                                         first_track.first_arrival_date,
                                                         next_appointment.type       AS latest_appointment_type,
                                                         next_appointment.start_time AS latest_appointment_start_time,
                                                         next_appointment.end_time   AS latest_appointment_end_time
                                                  FROM deal d
                                                         JOIN person p ON d.person_id = p.id and p.status <> -1 and p.deleted_at IS NULL
                                                         LEFT JOIN stage s ON d.stage_id = s.id
                                                         LEFT JOIN (SELECT deal_id, MIN(created_at) as first_arrival_date
                                                                    FROM track
                                                                    GROUP BY deal_id) first_track
                                                                   ON first_track.deal_id = d.id
                                                         LEFT JOIN (SELECT a_inner.person_id,
                                                                           a_inner.type,
                                                                           a_inner.start_time,
                                                                           a_inner.end_time
                                                                    FROM (SELECT person_id,
                                                                                 type,
                                                                                 start_time,
                                                                                 end_time,
                                                                                 ROW_NUMBER() OVER (PARTITION BY person_id ORDER BY start_time ASC) as rn
                                                                          FROM appointment
                                                                          WHERE status in (2, 5)
                                                                            AND type IN (5, 6, 7, 8, 9, 10)
                                                                            AND start_time > NOW()) a_inner
                                                                    WHERE a_inner.rn = 1) next_appointment
                                                                   ON next_appointment.person_id = p.id
                                                  WHERE d.status > -1
                                                    AND d.deleted_at IS NULL
                                                    AND (f_unaccent(d.name) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
                                                    AND ((f_unaccent(p.full_name) LIKE ('%' || f_unaccent($2) || '%') OR
                                                          f_unaccent(p.phone) LIKE ('%' || f_unaccent($2) || '%') OR
                                                          f_unaccent(p.person_field ->> 'code') LIKE
                                                          ('%' || f_unaccent($2) || '%')) OR $2 IS NULL)
                                                    AND (d.created_at > $3 OR $3 IS NULL)
                                                    AND (d.created_at < $4 OR $4 IS NULL)
                                                    AND (d.updated_at > $5 OR $5 IS NULL)
                                                    AND (d.updated_at < $6 OR $6 IS NULL)
                                                    AND (d.total_value >= $7 OR $7 IS NULL)
                                                    AND (d.total_value <= $8 OR $8 IS NULL)
                                                    AND (d.deposit_amount >= $9 OR $9 IS NULL)
                                                    AND (d.deposit_amount <= $10 OR $10 IS NULL)
                                                    AND (d.stage_id = ANY ($11::bigint[]) OR $11 IS NULL)
                                                    AND (d.state = $12 OR $12 IS NULL)
                                                    AND (d.person_id = ANY ($13::bigint[]) OR $13 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.after ? 'state' AND dh_inner.after ->> 'state' = $14)) OR
                                                         $14 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.after ? 'total_value' AND (dh_inner.after ->> 'total_value')::double precision = $15::double precision)) OR
                                                         $15 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.after ? 'name' AND dh_inner.after ->> 'name' LIKE '%' || $16 || '%')) OR
                                                         $16 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.after ? 'total_amount' AND (dh_inner.after ->> 'total_amount')::double precision = $17::double precision)) OR
                                                         $17 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.after ? 'deposit_amount' AND (dh_inner.after ->> 'deposit_amount')::double precision = $18::double precision)) OR
                                                         $18 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.operation = $19)) OR $19 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_stage_history dsh_inner
                                                                  WHERE dsh_inner.deal_id = d.id
                                                                    AND dsh_inner.before = ANY ($20::int[]))) OR
                                                         $20 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_stage_history dsh_inner
                                                                  WHERE dsh_inner.deal_id = d.id
                                                                    AND dsh_inner.after = ANY ($21::int[]))) OR
                                                         $21 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.user_id = $22)) OR $22 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_stage_history dsh_inner
                                                                  WHERE dsh_inner.deal_id = d.id
                                                                    AND dsh_inner.user_id = $23)) OR $23 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_history dh_inner
                                                                  WHERE dh_inner.deal_id = d.id
                                                                    AND dh_inner.changed_at BETWEEN $24 AND $25)) OR
                                                         $24 IS NULL OR $25 IS NULL)
                                                    AND ((EXISTS (SELECT 1
                                                                  FROM deal_stage_history dsh_inner
                                                                  WHERE dsh_inner.deal_id = d.id
                                                                    AND dsh_inner.changed_at BETWEEN $26 AND $27)) OR
                                                         $26 IS NULL OR $27 IS NULL)
                                                    AND (EXISTS (SELECT 1
                                                                 FROM tag_person tp
                                                                 WHERE tp.person_id = p.id
                                                                   AND tp.tag_id = ANY ($28::int[])) OR $28 IS NULL)
                                                    AND (((SELECT pa.user_id
                                                           FROM person_assignment pa
                                                           WHERE pa.person_id = p.id
                                                             AND pa.role = 'sale' LIMIT 1) = ANY ($29:: int []) OR EXISTS (
                           SELECT 1
                           FROM deal_user du
                           WHERE du.deal_id = d.id
                             AND du.user_id = ANY ($29:: int [])))
                              OR $29 IS NULL)
                             AND (first_track.first_arrival_date >= $30
                              OR $30 IS NULL)
                             AND (first_track.first_arrival_date <= $31
                              OR $31 IS NULL)
                             AND ((next_appointment.person_id IS NOT NULL
                             AND next_appointment.type = ANY ($32:: int []))
                              OR $32 IS NULL)
                             AND ((next_appointment.person_id IS NOT NULL
                             AND next_appointment.start_time >= $33)
                              OR $33 IS NULL)
                             AND ((next_appointment.person_id IS NOT NULL
                             AND next_appointment.start_time <= $34)
                              OR $34 IS NULL)
                           ORDER BY {{orderClause1}}
                             LIMIT $35
                           OFFSET $36 ), deal_data AS (
                           SELECT
                             d.*, bdi.first_arrival_date, bdi.latest_appointment_type, bdi.latest_appointment_start_time, bdi.latest_appointment_end_time
                           FROM deal d
                             JOIN base_deal_ids bdi
                           ON d.id = bdi.id
                             ),
                             person_data AS (
                           SELECT p.*
                           FROM person p
                           WHERE p.id IN (SELECT person_id FROM deal_data WHERE person_id IS NOT NULL)
                             )
                               , stage_data AS (
                           SELECT s.*
                           FROM stage s
                           WHERE s.id IN (SELECT stage_id FROM deal_data WHERE stage_id IS NOT NULL)
                             )
                               , deal_history_data AS (
                           SELECT
                             dh.deal_id, jsonb_agg(jsonb_build_object('id', dh.id, 'created_at', dh.created_at, 'before', dh.before, 'after', dh.after, 'operation', dh.operation, 'changed_at', dh.changed_at, 'user_id', dh.user_id)) AS history_data
                           FROM deal_history dh
                           WHERE dh.deal_id IN (SELECT id FROM deal_data)
                           GROUP BY dh.deal_id
                             ),
                             deal_stage_history_data AS (
                           SELECT
                             dsh.deal_id, jsonb_agg(jsonb_build_object('id', dsh.id, 'created_at', dsh.created_at, 'before', dsh.before, 'after', dsh.after, 'user_id', dsh.user_id, 'changed_at', dsh.changed_at)) AS stage_history_data
                           FROM deal_stage_history dsh
                           WHERE dsh.deal_id IN (SELECT id FROM deal_data)
                           GROUP BY dsh.deal_id
                             ),
                             person_tag_data AS (
                           SELECT
                             tp.person_id, jsonb_agg(tag.id::bigint) AS deal_tag_ids, jsonb_agg(jsonb_build_object('id', tag.id, 'name', tag.name, 'category', tag.category) ORDER BY tag.name) AS deal_tags_json
                           FROM tag_person tp
                             JOIN tag
                           ON tag.id = tp.tag_id
                           WHERE tp.person_id IN (SELECT person_id FROM person_data WHERE person_id IS NOT NULL)
                           GROUP BY tp.person_id
                             ),
                             deal_assignment_data AS (
                           SELECT
                             du.deal_id, jsonb_agg(u.id::bigint) AS deal_assignment_user_ids, jsonb_agg(jsonb_build_object('id', du.id, 'user_id', u.id, 'username', u.username, 'email', u.email, 'name', u.name, 'department_id', u.department_id, 'role', du.role, 'point', du.point, 'ratings', COALESCE ((SELECT jsonb_agg(jsonb_build_object('id', dur.id, 'category', dur.category, 'rating', dur.rating, 'created_at', dur.created_at, 'updated_at', dur.updated_at) ORDER BY dur.created_at)
                             FROM deal_user_rating dur WHERE dur.deal_user_id = du.id), '[]'::jsonb))
                             ORDER BY u.name) AS deal_assignment_json
                           FROM deal_user du
                             JOIN "user" u
                           ON du.user_id = u.id
                           WHERE du.deal_id IN (SELECT id FROM deal_data)
                           GROUP BY du.deal_id
                             ),
                             sale_user_data AS (
                           SELECT
                             p.id AS person_id, pa.user_id AS sale_user_id, jsonb_build_object('id', u.id, 'username', u.username, 'email', u.email, 'name', u.name, 'department_id', u.department_id) AS sale_user_json
                           FROM person_data p
                             JOIN person_assignment pa
                           ON pa.person_id = p.id AND pa.role = 'sale'
                             JOIN "user" u ON u.id = pa.user_id
                           WHERE (p.id
                               , pa.created_at) IN (
                             SELECT p_inner.id
                               , MAX (pa_inner.created_at)
                             FROM person_data p_inner
                             JOIN person_assignment pa_inner ON pa_inner.person_id = p_inner.id
                             AND pa_inner.role = 'sale'
                             GROUP BY p_inner.id
                             )
                             )
                               , referrer_data AS (
                           SELECT
                             pr.referred_id AS person_id, jsonb_build_object('id', p_ref.id, 'full_name', p_ref.full_name, 'phone', p_ref.phone, 'email', p_ref.email) AS referrer_json, row_to_json(pr)::jsonb AS referral_info_json
                           FROM person_data p
                             JOIN person_referral pr
                           ON pr.referred_id = p.id
                             JOIN person p_ref ON pr.referrer_id = p_ref.id
                           WHERE (p.id
                               , pr.created_at) IN (
                             SELECT p_inner.id
                               , MAX (pr_inner.created_at)
                             FROM person_data p_inner
                             JOIN person_referral pr_inner ON pr_inner.referred_id = p_inner.id
                             GROUP BY p_inner.id
                             )
                             )
                               , person_payment_data AS (
                           SELECT person_id, SUM (total_amount) AS total_person_payment
                           FROM payment
                           WHERE person_id IN (SELECT person_id FROM person_data)
                           GROUP BY person_id
                             )
SELECT dd.id,
       dd.status,
       dd.version,
       dd.created_at,
       dd.updated_at,
       dd.parent_deal_id,
       dd.total_value,
       dd.deposit_amount,
       dd.total_installments,
       dd.name,
       dd.person_id,
       dd.stage_id,
       dd.stage_history,
       dd.total_amount,
       dd.state,
       jsonb_build_object('id', p.id, 'full_name', p.full_name, 'phone', p.phone, 'email', p.email, 'gender', p.gender,
                          'date_of_birth', p.date_of_birth,
                          'address',
                          jsonb_build_object('province_id', p.province_id, 'district_id', p.district_id, 'ward_id',
                                             p.ward_id, 'address_number', p.address_number),
                          'job_id', p.job_id, 'source_id', p.source_id, 'user_id', p.user_id, 'person_field',
                          p.person_field)                                  AS person,
       jsonb_build_object('id', s.id, 'name', s.name, 'order_number', s.order_number, 'meta', s.meta, 'parent_stage_id',
                          s.parent_stage_id, 'pipeline_id', s.pipeline_id) AS stage,
       dhd.history_data                                                    AS deal_history,
       dshd.stage_history_data                                             AS stage_history_details,
       ptd.deal_tag_ids                                                    AS deal_tag_ids,
       ptd.deal_tags_json                                                  AS tags,
       dad.deal_assignment_user_ids,
       dad.deal_assignment_json                                            AS deal_assignment,
       sud.sale_user_id,
       sud.sale_user_json                                                  AS sale_user,
       rd.referrer_json                                                    AS referrer,
       rd.referral_info_json                                               AS referral_info,
       dd.first_arrival_date,
       dd.latest_appointment_type,
       dd.latest_appointment_start_time,
       dd.latest_appointment_end_time,
       COALESCE(ppd.total_person_payment, 0)                               AS total_person_payment
FROM deal_data dd
       LEFT JOIN person_data p ON dd.person_id = p.id
       LEFT JOIN stage_data s ON dd.stage_id = s.id
       LEFT JOIN deal_history_data dhd ON dd.id = dhd.deal_id
       LEFT JOIN deal_stage_history_data dshd ON dd.id = dshd.deal_id
       LEFT JOIN person_tag_data ptd ON p.id = ptd.person_id
       LEFT JOIN deal_assignment_data dad ON dd.id = dad.deal_id
       LEFT JOIN sale_user_data sud ON p.id = sud.person_id
       LEFT JOIN referrer_data rd ON p.id = rd.person_id
       LEFT JOIN person_payment_data ppd ON p.id = ppd.person_id
ORDER BY {{orderClause2}}
  LIMIT $35;
`;

export const DEAL_COUNT_QUERY = `SELECT COUNT(DISTINCT d.id) AS total_count
                                 FROM deal d
                                        JOIN person p ON d.person_id = p.id and p.status <> -1 and p.deleted_at IS NULL
                                        LEFT JOIN stage s ON d.stage_id = s.id
                                        LEFT JOIN (SELECT deal_id, MIN(created_at) as first_arrival_date
                                                   FROM track
                                                   GROUP BY deal_id) first_track ON first_track.deal_id = d.id
-- JOIN với thông tin của lịch hẹn tiếp theo gần nhất
                                        LEFT JOIN (SELECT a_inner.person_id, a_inner.type, a_inner.start_time
                                                   FROM (SELECT person_id, type, start_time, ROW_NUMBER() OVER(PARTITION BY person_id ORDER BY start_time ASC) as rn
                                                         FROM appointment
                                                         WHERE  status in (2, 5)
                                                           AND type IN (5, 6, 7, 8, 9, 10)
                                                           AND start_time > NOW()) a_inner
                                                   WHERE a_inner.rn = 1) next_appointment
                                                  ON next_appointment.person_id = p.id
                                 WHERE d.status > -1
                                   AND d.deleted_at IS NULL
                                   AND (f_unaccent(d.name) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
                                   AND ((f_unaccent(p.full_name) LIKE ('%' || f_unaccent($2) || '%') OR
                                         f_unaccent(p.phone) LIKE ('%' || f_unaccent($2) || '%') OR
                                         f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($2) || '%')) OR
                                        $2 IS NULL)
                                   AND (d.created_at > $3 OR $3 IS NULL)
                                   AND (d.created_at < $4 OR $4 IS NULL)
                                   AND (d.updated_at > $5 OR $5 IS NULL)
                                   AND (d.updated_at < $6 OR $6 IS NULL)
                                   AND (d.total_value >= $7 OR $7 IS NULL)
                                   AND (d.total_value <= $8 OR $8 IS NULL)
                                   AND (d.deposit_amount >= $9 OR $9 IS NULL)
                                   AND (d.deposit_amount <= $10 OR $10 IS NULL)
                                   AND (d.stage_id = ANY ($11::bigint[]) OR $11 IS NULL)
                                   AND (d.state = $12 OR $12 IS NULL)
                                   AND (d.person_id = ANY ($13::bigint[]) OR $13 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.after ? 'state' AND dh_inner.after ->>'state' = $14)) OR
                                        $14 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.after ? 'total_value' AND (dh_inner.after ->>'total_value')::double precision = $15::double precision)) OR
                                        $15 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.after ? 'name' AND dh_inner.after ->>'name' LIKE '%' || $16 || '%')) OR
                                        $16 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.after ? 'total_amount' AND (dh_inner.after ->>'total_amount')::double precision = $17::double precision)) OR
                                        $17 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.after ? 'deposit_amount' AND (dh_inner.after ->>'deposit_amount')::double precision = $18::double precision)) OR
                                        $18 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id AND dh_inner.operation = $19)) OR
                                        $19 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_stage_history dsh_inner
                                                 WHERE dsh_inner.deal_id = d.id
                                                   AND dsh_inner.before = ANY ($20::int[]))) OR $20 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_stage_history dsh_inner
                                                 WHERE dsh_inner.deal_id = d.id
                                                   AND dsh_inner.after = ANY ($21::int[]))) OR $21 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id AND dh_inner.user_id = $22)) OR
                                        $22 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_stage_history dsh_inner
                                                 WHERE dsh_inner.deal_id = d.id AND dsh_inner.user_id = $23)) OR
                                        $23 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_history dh_inner
                                                 WHERE dh_inner.deal_id = d.id
                                                   AND dh_inner.changed_at BETWEEN $24 AND $25)) OR $24 IS NULL OR
                                        $25 IS NULL)
                                   AND ((EXISTS (SELECT 1
                                                 FROM deal_stage_history dsh_inner
                                                 WHERE dsh_inner.deal_id = d.id
                                                   AND dsh_inner.changed_at BETWEEN $26 AND $27)) OR $26 IS NULL OR
                                        $27 IS NULL)
                                   AND (EXISTS (SELECT 1
                                                FROM tag_person tp
                                                WHERE tp.person_id = p.id AND tp.tag_id = ANY ($28::int[])) OR
                                        $28 IS NULL)
                                   AND (((SELECT pa.user_id
                                          FROM person_assignment pa
                                          WHERE pa.person_id = p.id AND pa.role = 'sale' LIMIT 1) = ANY ($29:: int [])
                                    OR EXISTS (
                                 SELECT 1
                                 FROM deal_user du
                                 WHERE du.deal_id = d.id
                                   AND du.user_id = ANY ($29:: int [])))
                                    OR $29 IS NULL)
                                   AND (first_track.first_arrival_date >= $30
                                    OR $30 IS NULL)
                                   AND (first_track.first_arrival_date <= $31
                                    OR $31 IS NULL)
                                   AND ((next_appointment.person_id IS NOT NULL
                                   AND next_appointment.type = ANY ($32:: int []))
                                    OR $32 IS NULL)
                                   AND ((next_appointment.person_id IS NOT NULL
                                   AND next_appointment.start_time >= $33)
                                    OR $33 IS NULL)
                                   AND ((next_appointment.person_id IS NOT NULL
                                   AND next_appointment.start_time <= $34)
                                    OR $34 IS NULL);
`;
