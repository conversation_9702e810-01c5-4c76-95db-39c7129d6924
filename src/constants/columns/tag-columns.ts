import { FilterMatchMode } from "@primevue/core/api";
import type { ColumnDefinition } from "@/components/DataTable";
import type { TagResponse } from "@/api/bcare-types-v2";

export const tagColumns: ColumnDefinition<TagResponse>[] = [
  {
    field: "name",
    header: "Tên tag",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm kiếm tên thẻ",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "35%" },
  },
  {
    field: "category",
    header: "Category",
    sortable: false,
    showFilterMenu: false,
    style: { width: "25%" },
  },
  {
    field: "description",
    header: "Mô tả",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm kiếm mô tả",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "50%" },
  },
];

// Filter configurations for useFilterList
export const tagFilterConfigs = {
  name: {
    field: "search", // API field name
    isFilter: false, // Goes to root level of payload
  },
  description: {
    field: "search", // Both name and description can map to search
    isFilter: false,
  },
  category: {
    field: "category",
    isFilter: true, // Goes to filter object
  },
};

export const tagDefaultFilters = {
  page: 1,
  page_size: 50, // Since we don't need pagination for <50 items
};
