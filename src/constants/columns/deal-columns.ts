import type { ColumnDefinition } from "@/components/DataTable/constants";
import { FilterMatchMode } from "@primevue/core/api";

export const dealColumns: ColumnDefinition<any>[] = [
  {
    field: "created_at",
    header: "<PERSON><PERSON><PERSON> tạo",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "12%" },
  },
  {
    field: "first_arrival_date",
    header: "Ngày tới đầu tiên",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "12%" },
  },
  {
    field: "name",
    header: "Tên Deal",
    sortable: true,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Nhập tên deal",
    style: { width: "23%" },
  },
  {
    field: "person_id", // Note: The filter logic in the store uses 'personInfo' param
    header: "<PERSON><PERSON><PERSON><PERSON> hàng",
    sortable: false,
    showFilterMenu: false,
    filterType: "text", // Simple text input, store handles combined search
    filterPlaceholder: "Tên, SĐT, M<PERSON> KH",
    style: { width: "23%" },
  },
  {
    field: "stage_id",
    header: "Giai đoạn",
    sortable: false,
    showFilterMenu: false,
    style: { width: "14%" },
  },
  {
    field: "tags",
    header: "Tags",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false,
    filterType: "custom",
    filterPlaceholder: "Chọn tag",
  },
  {
    field: "related_users",
    header: "Người liên quan",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false,
    filterType: "custom",
    filterPlaceholder: "Chọn người liên quan",
  },
  {
    field: "latest_appointment_type",
    header: "Loại lịch hẹn",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    filterPlaceholder: "Chọn loại lịch hẹn",
    style: { width: "12%" },
  },
  {
    field: "latest_appointment_time",
    header: "Thời gian hẹn",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    style: { width: "16%" },
  },
  {
    field: "total_person_payment",
    header: "Tổng thanh toán",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    style: { width: "12%" },
  },
];

export const createDefaultDealFilters = () => ({
  created_at: { value: null, matchMode: FilterMatchMode.CONTAINS },
  name: { value: null, matchMode: FilterMatchMode.CONTAINS },
  person_id: { value: null, matchMode: FilterMatchMode.CONTAINS },
  stage_id: { value: null, matchMode: FilterMatchMode.CONTAINS },
  related_users: { value: null, matchMode: FilterMatchMode.CONTAINS },
  tags: { value: null, matchMode: FilterMatchMode.CONTAINS },
  first_arrival_date: { value: null, matchMode: FilterMatchMode.CONTAINS },
  latest_appointment_type: { value: null, matchMode: FilterMatchMode.CONTAINS },
});

/**
 * Interface cho filters của DataTable Deal
 */
export interface DealFilter {
  value: string | null;
  matchMode: string;
}

export interface DealFilters {
  created_at: DealFilter;
  name: DealFilter;
  person_id: DealFilter;
  stage_id: DealFilter;
  related_users: DealFilter;
  tags: DealFilter;
}
