import { FilterMatchMode } from "@primevue/core/api";

import type { ColumnDefinition } from "@/components/DataTable";
import { transformDateRangeForFilter } from "@/utils/time-helper";

/**
 * Column definitions for Material Usage Summary Report
 */
export const materialUsageSummaryColumns: ColumnDefinition<any>[] = [
  {
    field: "name",
    header: "Vật tư",
    sortable: false,
    showFilterMenu: false,
    style: { width: "35%", minWidth: "250px" },
  },
  {
    field: "unit",
    header: "Đơn vị",
    sortable: false,
    showFilterMenu: false,
    style: { width: "12%", minWidth: "80px" },
  },
  {
    field: "total_attachment",
    header: "Số lần sử dụng",
    sortable: false,
    showFilterMenu: false,
    style: { width: "13%", minWidth: "120px" },
  },
  {
    field: "total_quoted_quantity",
    header: "Định mức",
    sortable: false,
    showFilterMenu: false,
    style: { width: "13%", minWidth: "120px" },
  },
  {
    field: "total_used_quantity",
    header: "Thực tế",
    sortable: false,
    showFilterMenu: false,
    style: { width: "13%", minWidth: "120px" },
  },
  {
    field: "difference",
    header: "Chênh lệch",
    sortable: false,
    showFilterMenu: false,
    style: { width: "14%", minWidth: "120px" },
  },
];

/**
 * Column definitions for Material Usage Detail Report (Flat structure with rowspan)
 */
export const materialUsageDetailColumns: ColumnDefinition<any>[] = [
  {
    field: "person_name",
    header: "Tên khách",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm người thực hiện",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "23%", minWidth: "180px" },
  },
  {
    field: "operation_name",
    header: "Tên cv",
    sortable: false,
    showFilterMenu: false,
    style: { width: "17%", minWidth: "120px" },
  },
  {
    field: "material_name",
    header: "Vật tư",
    sortable: false,
    showFilterMenu: false,
    style: { width: "23%", minWidth: "150px" },
  },
  {
    field: "quoted_quantity",
    header: "Định mức",
    sortable: false,
    showFilterMenu: false,
    style: { width: "12%", minWidth: "100px" },
  },
  {
    field: "used_quantity",
    header: "Thực tế",
    sortable: false,
    showFilterMenu: false,
    style: { width: "12%", minWidth: "100px" },
  },
  {
    field: "difference",
    header: "chênh lệch",
    sortable: false,
    showFilterMenu: false,
    style: { width: "13%", minWidth: "100px" },
  },
  {
    field: "unit",
    header: "Đvt",
    sortable: false,
    showFilterMenu: false,
    style: { width: "10%", minWidth: "80px" },
  },
];

/**
 * Column definitions for nested material details in detail report
 */
export const materialDetailColumns: ColumnDefinition<any>[] = [
  {
    field: "material_name",
    header: "Tên vật tư",
    sortable: false,
    showFilterMenu: false,
    style: { width: "35%", minWidth: "150px" },
  },
  {
    field: "quoted_quantity",
    header: "Định mức",
    sortable: false,
    showFilterMenu: false,
    style: { width: "17%", minWidth: "100px" },
  },
  {
    field: "used_quantity",
    header: "SL thực tế",
    sortable: false,
    showFilterMenu: false,
    style: { width: "17%", minWidth: "100px" },
  },
  {
    field: "difference",
    header: "Chênh lệch",
    sortable: false,
    showFilterMenu: false,
    style: { width: "19%", minWidth: "100px" },
  },
  {
    field: "unit",
    header: "Đơn vị",
    sortable: false,
    showFilterMenu: false,
    style: { width: "12%", minWidth: "80px" },
  },
];

/**
 * Filter configurations for useFilterList hook
 */
export const materialUsageReportFilterConfigs = {
  // External filters (from ReportToolbar)
  search: {
    field: "search", // Maps to search field in API
    isFilter: true,
  },
  // Inline column filters
  code: {
    field: "search", // Maps to search field in API
    isFilter: true,
  },
  name: {
    field: "search", // Maps to search field in API
    isFilter: true,
  },
  unit: {
    field: "search", // Maps to search field in API
    isFilter: true,
  },
  // External filters (from ReportToolbar)
  material_id: {
    field: "material_id",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
  operation_id: {
    field: "operation_id",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
  user_id: {
    field: "user_id",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
  person_id: {
    field: "person_id",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
  // Date range filter (handled specially in useFilterList)
  dateRange: {
    field: "dateRange", // Special handling in useFilterList splits this into from/to
    isFilter: true, // Goes to filter object, but handled specially
    valueTransform: transformDateRangeForFilter,
  },
};

/**
 * Default filter values for material usage reports
 * Note: page and page_size are handled separately at component level
 */
export const materialUsageReportDefaultFilters = {
  filter: {},
};
