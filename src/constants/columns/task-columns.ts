import { FilterMatchMode } from "@primevue/core/api";

import type { TaskResponse } from "@/api/bcare-types-v2";
import type { ColumnDefinition } from "@/components/DataTable";

/**
 * <PERSON><PERSON><PERSON> nghĩa cấu hình cột cho DataTable Task
 */
export const taskColumns: ColumnDefinition<TaskResponse>[] = [
  {
    field: "title",
    header: "Tên công việc",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterType: "text",
    filterPlaceholder: "Tìm kiếm công việc",
    style: { width: "37%", minWidth: "250px" },
  },
  {
    field: "person_id",
    header: "<PERSON>h<PERSON><PERSON> hàng",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterType: "text",
    filterPlaceholder: "T<PERSON><PERSON> kiếm khách hàng",
    style: { width: "20%", minWidth: "150px" },
  },
  {
    field: "start_date",
    header: "<PERSON><PERSON><PERSON> bắt đầu",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày bắt đầu",
    style: { width: "10%", minWidth: "140px" },
  },
  {
    field: "due_date",
    header: "Ngày kết thúc",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày kết thúc",
    style: { width: "10%", minWidth: "140px" },
  },
  {
    field: "assignments",
    header: "Nhận việc",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.IN,
    filterType: "custom",
    style: { width: "10%", minWidth: "120px" },
  },
  {
    field: "state",
    header: "Trạng thái",
    showFilterMenu: false,
    style: { width: "13%", minWidth: "120px" },
  },
  {
    field: "completed_at",
    header: "Ngày hoàn thành",
    showFilterMenu: false,
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày hoàn thành",
    style: { width: "12%", minWidth: "140px" },
  },
  {
    field: "actions",
    header: "",
    style: { minWidth: "20px" },
    sortable: false,
    showFilterMenu: false,
  },
];

/**
 * Tạo cấu trúc filter mặc định cho DataTable Task
 */
export const createDefaultTaskFilters = () => ({
  title: { value: null, matchMode: FilterMatchMode.CONTAINS },
  person_id: { value: null, matchMode: FilterMatchMode.CONTAINS },
  start_date: { value: null, matchMode: FilterMatchMode.CONTAINS },
  due_date: { value: null, matchMode: FilterMatchMode.CONTAINS },
  assignments: { value: null, matchMode: FilterMatchMode.IN },
  completed_at: { value: null, matchMode: FilterMatchMode.CONTAINS },
});

/**
 * Interface cho filters của DataTable Task
 */
export interface TaskFilter {
  value: string | null;
  matchMode: string;
}

export interface TaskFilters {
  title: TaskFilter;
  person_id: TaskFilter;
  start_date: TaskFilter;
  due_date: TaskFilter;
  assignments: TaskFilter;
  completed_at: TaskFilter;
}
