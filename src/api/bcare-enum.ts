export enum EnumTypeAppointmenntStatus {
  ORTHODONTIC = 1,
  GENERALITY = 2,
  IMPLANT = 3,
  MINOR_SURGERY = 4,
}

export const enum EnumFileUsageType {
  EXAMINATION_IMAGE = "hinh_tham_kham",
  X_RAY_IMAGE = "hinh_x_quang",
  FILE_IMAGE = "hinh_tap_tin",
  DOCUMENT_FILE_IMAGE = "tap_tin_tu_lieu",
  PROFILE_FILE_IMAGE = "tap_tin_ho_so",
}

export const enum EnumEntityType {
  DEAL = "deal",
  PERSON = "person",
}
export const enum AnswerType {
  BOOLEAN = "bool",
  SINGLE_CHOICE = "single_choice",
  MULTI_CHOICE = "multiple_choice",
  TABLE_CHOICE = "multiple_choice_table",
  INTEGER = "int",
  STRING = "string",
  TEXT = "text",
  LIST = "list",
}

export const enum TaskStatusEnum {
  ALL = 0,
  CLOSE = 1,
  NEWS = 2,
  PROCESSING = 3,
  DONE = 4,
  REVIEWER = 5,
}

export const enum TaskPriorityEnum {
  ALL = 0,
  HIGH = 3,
  MEDIUM = 2,
  LOW = 1,
}

export const enum TaskRoleAssignmentEnum {
  PRIMARY = "primary",
  REVIEWER = "reviewer",
  CONTRIBUTOR = "contributor",
}
export const enum GenderPerson {
  MALE = "male",
  FEMALE = "female",
  UNKNOWN = "unknown",
}
export const enum LogEnum {
  APP = "app",
  INFO = "info",
  ERROR = "error",
  SLOW = "slow",
  STAT = "stat",
  SEVERE = "severe",
}

export const enum CommonStatus {
  DELETED = -1,
  TEMP = -2,
  INACTIVE = 1,
  ACTIVE = 2, //normal
}

export const enum DealStatus {
  REFRESH = 13,
}

export const enum AttachmentStatus {
  DELETED = CommonStatus.DELETED,
  INACTIVE = CommonStatus.INACTIVE,
  ACTIVE = CommonStatus.ACTIVE,
  TEMP = CommonStatus.TEMP,
  UNPAID = 22,
}

export enum FilterOperator {
  EQ = "EQ",
  NEQ = "NEQ",
  GT = "GT",
  GTE = "GTE",
  LT = "LT",
  LTE = "LTE",
  IN = "IN",
  LIKE = "LIKE",
  NOTIN = "NOTIN",
  NOTLIKE = "NOTLIKE",
  BETWEEN = "BETWEEN",
  ISNULL = "ISNULL",
  ISNOTNULL = "ISNOTNULL",
  TODAY = "TODAY",
  CONTAINS = "CONTAINS",
  JSON_CONTAINS_ANY = "JSON_CONTAINS_ANY",
}

export enum FilterSqlFunction {
  TO_CHAR = "TO_CHAR",
  DATE = "DATE",
  YEAR = "YEAR",
  MONTH = "MONTH",
  DAY = "DAY",
  LOWER = "LOWER",
  UPPER = "UPPER",
}

export enum FilterAggregationFunction {
  SUM = "SUM",
  AVG = "AVG",
  COUNT = "COUNT",
  MIN = "MIN",
  MAX = "MAX",
}

export enum FilterJoinType {
  LEFT = "LEFT",
  INNER = "INNER",
  RIGHT = "RIGHT",
  FULL = "FULL",
}

export const enum TaskStateEnum {
  NEW_TASK = "new_task",
  IN_PROGRESS = "in_progress",
  AWAITING_APPROVAL = "awaiting_approval",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  COMPLETED_EARLY = "completed_early",
}

export const enum NoteType {
  COMPLAIN = 4,
  OTHER = 1,
}

export enum AppointmentReminderStatus {
  REMINDED = 1,
  NOT_REMINDED = 0,
  OTHER = -1,
}

// Thứ tự hiển thị appointment type theo yêu cầu client
export const APPOINTMENT_TYPE_ORDER = [
  5, // Gặp BSN
  6, // TQ trước GMC -> Tổng quát trước GMC
  7, // Tách Kẽ -> Tách kẽ
  8, // LDCK
  9, // GKC
  10, // GMC
  1, // Chỉnh nha
  2, // Tổng quát
  3, // Implant
  4, // Tiểu phẫu
] as const;

export enum DepartmentAssignment {
  DOCTOR = 1,
  CS = 2,
  ASSISTANT = 5,
  TELESALES = 10,
}

export enum DoctorType {
  ORTHODONTIC = 12,
  GENERALITY = 13,
  IMPLANT = 14,
  MINOR_SURGERY = 15,
}

const CALL_STATES = {
  INBOUND_ANSWERED: "inbound_answered",
  INBOUND_MISSED: "inbound_missed",
  OUTBOUND_ANSWERED: "outbound_answered",
  OUTBOUND_MISSED: "outbound_missed",
  UNDEFINED: "undefined",
} as const;
export type CallState = (typeof CALL_STATES)[keyof typeof CALL_STATES];

export const USER_DATA_KIND = {
  CALL_CENTER: "call_center",
  // Add future kinds here
} as const;

export type UserDataKind = (typeof USER_DATA_KIND)[keyof typeof USER_DATA_KIND];

export const enum DealState {
  DRAFT = "draft",
  WON = "won",
  LOST = "lost",
  PAYING = "paying",
  CANCELLED = "cancelled",
  ACTIVE = "active",
}

export const CallKind = {
  PROCESSED: {
    value: "PROCESSED",
    label: "Đã xử lý",
  },
  UNHEARD: {
    value: "UNHEARD",
    label: "Không nghe máy",
  },
  RENTAL: {
    value: "RENTAL",
    label: "Thuê bao",
  },
  TEST: {
    value: "TEST",
    label: "Test tổng đài",
  },
  UNDEFINED: {
    value: "UNDEFINED",
    label: "Chưa xử lý",
  },
} as const;

export type CallKindType = keyof typeof CallKind;

export const OPERATION_MATERIAL_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
} as const;

export type OperationMaterialStatus =
  (typeof OPERATION_MATERIAL_STATUS)[keyof typeof OPERATION_MATERIAL_STATUS];

export const OPERATION_MATERIAL_STATUS_OPTIONS = [
  { name: "Hoạt động", value: OPERATION_MATERIAL_STATUS.ACTIVE },
  { name: "Không hoạt động", value: OPERATION_MATERIAL_STATUS.INACTIVE },
];

export interface OperationMaterialFilters {
  operation_id?: number;
  material_id?: number;
  status?: OperationMaterialStatus;
}

// Default values for forms
export const DEFAULT_OPERATION_MATERIAL = {
  operation_id: 0,
  material_id: 0,
  quantity: 1,
  status: OPERATION_MATERIAL_STATUS.ACTIVE,
} as const;

export const MATERIAL_USAGE_KIND = {
  OPERATION_QUOTA: "operation_quota",
  MANUAL_ADDITION: "manual_addition",
} as const;

export type MaterialUsageKind = (typeof MATERIAL_USAGE_KIND)[keyof typeof MATERIAL_USAGE_KIND];

export const MATERIAL_USAGE_KIND_OPTIONS = [
  { name: "Định mức", value: MATERIAL_USAGE_KIND.OPERATION_QUOTA },
  { name: "Thêm thủ công", value: MATERIAL_USAGE_KIND.MANUAL_ADDITION },
] as const;

export interface MaterialUsageFilters {
  attachment_id?: number;
  material_id?: number;
  operation_id?: number;
  user_id?: number;
  kind?: MaterialUsageKind;
}

// Default values for forms
export const DEFAULT_MATERIAL_USAGE = {
  attachment_id: 0,
  material_id: 0,
  used_quantity: 1,
  quoted_quantity: 1,
  kind: MATERIAL_USAGE_KIND.OPERATION_QUOTA,
} as const;

// Cash Flow Constants
export const CASH_FLOW_TYPE = {
  INCOME: "income",
  EXPENSE: "expense",
} as const;

export type CashFlowType = (typeof CASH_FLOW_TYPE)[keyof typeof CASH_FLOW_TYPE];

export const CASH_FLOW_STATE = {
  PENDING: "PENDING",
  APPROVED: "APPROVED",
  PAID: "PAID",
  REJECTED: "REJECTED",
  CANCELED: "CANCELED",
} as const;

export type CashFlowState = (typeof CASH_FLOW_STATE)[keyof typeof CASH_FLOW_STATE];

// Cash Flow Labels - Consistent vocabulary
export const CASH_FLOW_TYPE_LABELS = {
  [CASH_FLOW_TYPE.INCOME]: "Thu",
  [CASH_FLOW_TYPE.EXPENSE]: "Chi",
} as const;

export const CASH_FLOW_STATE_LABELS = {
  [CASH_FLOW_STATE.PENDING]: "Chờ duyệt",
  [CASH_FLOW_STATE.APPROVED]: "Đã duyệt",
  [CASH_FLOW_STATE.PAID]: "Đã trả",
  [CASH_FLOW_STATE.REJECTED]: "Từ chối",
  [CASH_FLOW_STATE.CANCELED]: "Đã hủy",
} as const;

// Combined labels for cash flow type + state
export const getCashFlowDisplayLabel = (type: CashFlowType, state: CashFlowState): string => {
  if (state === CASH_FLOW_STATE.PAID) {
    return type === CASH_FLOW_TYPE.INCOME ? "Đã thu" : "Đã chi";
  }
  if (state === CASH_FLOW_STATE.PENDING) {
    return "Chờ duyệt";
  }
  if (state === CASH_FLOW_STATE.APPROVED) {
    return type === CASH_FLOW_TYPE.INCOME ? "Duyệt thu" : "Duyệt chi";
  }
  if (state === CASH_FLOW_STATE.REJECTED) {
    return "Từ chối";
  }
  if (state === CASH_FLOW_STATE.CANCELED) {
    return "Đã hủy";
  }
  return CASH_FLOW_STATE_LABELS[state] || state;
};

// Combined severity for cash flow type + state
export const getCashFlowDisplaySeverity = (type: CashFlowType, state: CashFlowState): string => {
  if (state === CASH_FLOW_STATE.PAID) {
    return type === CASH_FLOW_TYPE.INCOME ? "success" : "danger"; // Đã thu: xanh, Đã chi: đỏ
  }
  if (state === CASH_FLOW_STATE.PENDING) {
    return "warning"; // Chờ duyệt: cam
  }
  if (state === CASH_FLOW_STATE.APPROVED) {
    return type === CASH_FLOW_TYPE.INCOME ? "info" : "warn"; // Duyệt thu: xanh dương, Duyệt chi: cam
  }
  if (state === CASH_FLOW_STATE.REJECTED) {
    return "warn"; // Từ chối: cam
  }
  if (state === CASH_FLOW_STATE.CANCELED) {
    return "secondary"; // Đã hủy: xám
  }
  return CASH_FLOW_STATE_SEVERITIES[state] || "secondary";
};

export const CASH_FLOW_STATE_SEVERITIES = {
  [CASH_FLOW_STATE.PENDING]: "warning",
  [CASH_FLOW_STATE.APPROVED]: "info",
  [CASH_FLOW_STATE.PAID]: "success",
  [CASH_FLOW_STATE.REJECTED]: "warn",
  [CASH_FLOW_STATE.CANCELED]: "secondary",
} as const;
