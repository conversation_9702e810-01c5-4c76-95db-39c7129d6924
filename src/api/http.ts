import axios from "axios";
import <PERSON>ie from "js-cookie";

import { config } from "@/config";
import { COOKIE_ENUM } from "@/enums/cookie-enum";
import router from "@/router";
import { useToastStore } from "@/stores/toast-store";

import { RefreshTokenResponse } from "./bcare-types-v2";
import { useAuthStore } from "@/stores/auth-store";

declare module "axios" {
  export interface AxiosRequestConfig {
    skipToast?: boolean;
  }
}

export const BASE_URL = config.apiUrl;

const axiosInstance = axios.create({
  baseURL: config.apiUrl,
  withCredentials: true,
});

axiosInstance.defaults.headers.common["Content-Type"] = "application/json";

let toastStore: ReturnType<typeof useToastStore> | null = null;

//Lazy load nó nếu ko nó bị undefined khi Pinia chưa khởi tạo do đây nằm ngoài component
const getNotiStore = () => {
  if (!toastStore) {
    toastStore = useToastStore();
  }
  return toastStore;
};

const refreshAccessToken = async () => {
  const refresh_token = Cookie.get(COOKIE_ENUM.REFRESH_TOKEN);
  const { id } = JSON.parse(Cookie.get(COOKIE_ENUM.USER) ?? "{}");
  if (refresh_token && id) {
    try {
      const resRefresh = await axiosInstance.post<RefreshTokenResponse>(`/v1/auth/refresh`, {
        refresh_token: refresh_token,
        id: id,
      });
      if (resRefresh.data) {
        if (resRefresh.data.access_token) {
          Cookie.remove(COOKIE_ENUM.ACCESS_TOKEN);
          Cookie.set(COOKIE_ENUM.ACCESS_TOKEN, resRefresh.data.access_token, {
            expires: new Date(new Date().getTime() + resRefresh.data.access_expire * 1000),
          });

          // Update refresh token if server returns new one
          if (resRefresh.data.refresh_token) {
            Cookie.remove(COOKIE_ENUM.REFRESH_TOKEN);
            Cookie.set(COOKIE_ENUM.REFRESH_TOKEN, resRefresh.data.refresh_token, {
              expires: new Date(new Date().getTime() + resRefresh.data.refresh_expire * 1000),
            });
          }

          return true;
        }
      }
    } catch (error) {
      console.error("Error refreshing access token:", error);
    }
  }

  // Redirect to login page if refresh token is invalid or not available
  await router.push({ path: "/login" }).catch((err) => {
    console.error("Error navigating to login page:", err);
  });
  return false;
};

axiosInstance.interceptors.request.use(
  (config) => {
    // Lấy stack trace để xác định nguồn gốc lời gọi
    try {
      const err = new Error();
      const stackLines = err.stack?.split("\n").map((line) => line.trim());

      if (stackLines && stackLines.length > 2) {
        // Cố gắng tìm dòng stack đầu tiên bên ngoài module http.ts
        // Chỉ số có thể cần điều chỉnh tùy thuộc vào cấu trúc và trình duyệt
        // Ví dụ: stackLines[0] là "Error", stackLines[1] là tại đây (interceptor),
        // stackLines[2] có thể là trong service (get, post), stackLines[3] là nơi gọi service.
        let callerLine = stackLines[3]; // Thử dòng thứ 4 trước

        // Tìm dòng đầu tiên không chứa 'api/http.ts' hoặc 'axios' (linh hoạt hơn)
        for (let i = 2; i < stackLines.length; i++) {
          if (
            stackLines[i] &&
            !stackLines[i].includes("api/http.ts") &&
            !stackLines[i].includes("axios")
          ) {
            callerLine = stackLines[i];
            break;
          }
        }

        // Phân tích dòng để lấy thông tin (ví dụ đơn giản, cần cải thiện độ chính xác)
        // Định dạng thường là "at functionName (filePath:lineNumber:columnNumber)" hoặc "functionName@filePath:lineNumber:columnNumber"
        const match =
          callerLine?.match(/at (?:.*\()?(.*:\d+:\d+)\)?/) || callerLine?.match(/@(.*:\d+:\d+)/);
        const sourceLocation = match ? match[1] : callerLine; // Lấy phần file:line:col hoặc cả dòng nếu không khớp

        console.groupCollapsed(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        console.log(`Source: ${sourceLocation || "N/A"}`);
        console.trace("Full stack trace:"); // Vẫn giữ trace đầy đủ để debug nếu cần
        console.groupEnd();
      }
    } catch (e) {
      console.warn("Could not determine API call source from stack trace:", e);
    }

    const accessToken = Cookie.get("access_token");
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (response) => {
    if (response.config.skipToast) {
      return response.data;
    }

    if (response.data.code !== 0) {
      if (response.config.url === "/v1/auth/login") {
        // TODO: why we need this?
        // getNotiStore().error({
        //   title: "Đăng nhập thất bại",
        //   message: "",
        // });
      } else {
        if (response.config.skipToast) return;
        getNotiStore().error({
          title: "Lỗi " + response.data.code,
          message: response.data.message,
        });
      }
    }
    return response.data;
  },
  async (error) => {
    const originalRequest = error.config;

    let errMessage = "Có lỗi xảy ra";

    // Check if error.response exists before accessing properties
    if (error.code === "ERR_NETWORK") {
      errMessage = "Không thể kết nối tới máy chủ";
    } else if (error.response && error.response.data && error.response.data.message) {
      errMessage = error.response.data.message as string;
    }

    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Try to refresh token
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        return axiosInstance(originalRequest);
      }

      // If refresh fails, use auth store to properly logout
      const { logout } = useAuthStore();
      logout(); // This also clears cookies

      // Notify user about session expiration
      getNotiStore().info({
        title: "Phiên đăng nhập hết hạn",
        message: "Vui lòng đăng nhập lại để tiếp tục.",
      });

      // Redirect to login page
      await router.push({ path: "/login" });
      return Promise.reject(error);
    }

    // Display error notification for other errors
    if (!originalRequest.skipToast) {
      getNotiStore().error({
        title: error.message || "Lỗi",
        message: errMessage,
      });
    }

    return Promise.reject(error);
  },
);

const service = {
  get<T = unknown>(url: string, data?: object): Promise<T> {
    return axiosInstance.get(url, { params: data });
  },

  post<T = unknown>(url: string, data?: object): Promise<T> {
    return axiosInstance.post(url, data);
  },

  put<T = unknown>(url: string, data?: object): Promise<T> {
    return axiosInstance.put(url, data);
  },

  delete<T = unknown>(url: string, data?: object): Promise<T> {
    return axiosInstance.delete(url, { data });
  },

  upload: <T = unknown>(url: string, file: FormData | File): Promise<T> =>
    axiosInstance.post(url, file, {
      headers: { "Content-Type": "multipart/form-data" },
    }),

  download: <T = unknown>(url: string, data?: object, skipNotification = false): Promise<T> =>
    axiosInstance.get(url, {
      params: data,
      responseType: "blob",
      skipToast: skipNotification,
    }),

  downloadBlob: <T = unknown>(url: string, data?: object): Promise<Blob> =>
    axiosInstance.post(url, data, {
      responseType: "blob",
      skipToast: true,
      headers: {
        "Content-Type": "application/json",
      },
    }),
};

export default service;
