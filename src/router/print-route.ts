// router/print-route.ts
import type { RouteLocationNormalized } from "vue-router";

export default {
  path: "/print",
  name: "print",
  meta: { requiresAuth: true },
  children: [
    {
      path: "payment/:id",
      name: "print-payment",
      component: () => import("@/components/Print/PrintPaymentDetail.vue"),
      props: true,
    },
    {
      path: "deal/:id/:personId",
      name: "print-deal",
      component: () => import("@/components/Print/PrintDealDetail.vue"),
      props: true,
    },
    {
      path: "medication/:id/:personId/:attachmentId?",
      name: "print-medication",
      component: () => import("@/components/Print/PrintMedication.vue"),
      props: true,
    },
    {
      path: "plan/:type/:personId",
      name: "print-plan",
      component: () => import("@/components/Print/PrintPlan.vue"),
      props: true,
    },
    {
      path: "medical-exam/:personId/:tabIndex",
      name: "print-medical-exam",
      component: () => import("@/components/Print/PrintMedicalExam.vue"),
      props: (route: RouteLocationNormalized) => ({
        personId: Number(route.params.personId),
        tabIndex: Number(route.params.tabIndex),
      }),
    },
    {
      path: "refund/:id",
      name: "print-refund",
      component: () => import("@/components/Print/PrintRefundDetail.vue"),
      props: true,
    },
    {
      path: "outpatient-medical/:id",
      name: "print-outpatient-medical",
      component: () => import("@/components/Print/PrintOutpatientMedical/index.vue"),
      props: true,
    },
    {
      path: "cashflow/:id",
      name: "print-cashflow",
      component: () => import("@/components/Print/PrintCashFlowDetail.vue"),
      props: true,
    },
    {
      path: "cashflow-summary/:dateRange?",
      name: "print-cashflow-summary",
      component: () => import("@/components/Print/PrintCashFlowSummary.vue"),
      props: true,
    },
  ],
};
