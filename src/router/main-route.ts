import { RouteRecordRaw } from "vue-router";

const mainRoute: Readonly<RouteRecordRaw> = {
  path: "/",
  component: () => import("@/layouts/MainLayout.vue"),
  meta: { requiresAuth: true },
  children: [
    {
      path: "dashboard",
      name: "top-menu-dashboard-updental",
      component: () => import("@/pages/dashboard/Dashboard.vue"),
      meta: {
        breadcrumb: "Điều phối",
        defaultTab: "deals",
      },
    },
    {
      path: "test",
      name: "top-menu-test",
      component: () => import("@/pages/templates/test.vue"),
    },
    {
      path: "pipeline/add",
      name: "top-menu-pipeline-add",
      component: () => import("@/pages/pipeline/PipelineAdd.vue"),
      meta: {
        breadcrumb: "Thêm mới quy trình",
      },
    },
    {
      path: "/profile",
      name: "top-menu-dashboard-user",
      component: () => import("@/pages/user/UserProfile.vue"),
      meta: {
        breadcrumb: "Thông tin người dùng",
      },
    },
    {
      path: "user/:id",
      name: "top-menu-user-profile",
      component: () => import("@/pages/user/UserProfile.vue"),
      meta: {
        breadcrumb: "Thông tin nhân viên",
      },
    },
    {
      path: "/",
      name: "top-menu-dashboard-overview-1",
      component: () => import("@/pages/templates/DashboardOverview1.vue"),
    },
    {
      path: "user",
      name: "top-menu-user-list",
      component: () => import("@/pages/user/UserList.vue"),
      meta: {
        breadcrumb: "Danh sách nhân viên",
      },
    },
    {
      path: "schedule",
      name: "top-menu-schedule",
      component: () => import("@/pages/schedule/Schedules.vue"),
      meta: {
        breadcrumb: "Lịch làm việc",
      },
    },
    {
      path: "customer",
      name: "top-menu-customer-list",
      component: () => import("@/pages/customer/Persons.vue"),
      meta: {
        breadcrumb: "Danh sách khách hàng",
      },
    },
    {
      path: "new-tracks",
      name: "top-menu-new-tracks",
      component: () => import("@/pages/new-tracks/Tracks.vue"),
      meta: {
        breadcrumb: "Danh sách deal",
      },
    },
    {
      path: "form-submission",
      name: "top-menu-form-submission",
      component: () => import("@/pages/form-submission/FormSubmission.vue"),
      meta: {
        breadcrumb: "Danh sách submissions",
      },
    },
    {
      path: "customer/profile/:id",
      name: "top-menu-customer-profile",
      component: () => import("@/pages/persons/PersonProfile.vue"),
      meta: {
        breadcrumb: "Chi tiết khách hàng",
        defaultTab: "activity",
      },
    },
    {
      path: "appointments-doctor",
      name: "top-menu-appointments-doctor",
      component: () => import("@/pages/customer-care/AppointmentsDoctor.vue"),
      meta: {
        breadcrumb: "Lịch hẹn bác sĩ",
        defaultTab: "appointment",
      },
    },
    {
      path: "appointments-offline",
      name: "top-menu-appointments-offline",
      component: () => import("@/pages/customer-care/AppointmentsOffline.vue"),
      meta: {
        breadcrumb: "Lịch hẹn lý thuyết",
        defaultTab: "appointment",
      },
    },
    {
      path: "appointment-of-doctor",
      name: "top-menu-appointment-of-doctor",
      component: () => import("@/pages/customer-care/AppointmentOfDoctor.vue"),
      meta: {
        breadcrumb: "Lịch bác sĩ",
        defaultTab: "appointment",
      },
    },
    {
      path: "accounting",
      name: "top-menu-accounting",
      component: () => import("@/pages/accounting/Accounting.vue"),
      meta: {
        breadcrumb: "Doanh thu",
      },
    },
    {
      path: "cash-flow",
      name: "top-menu-cash-flow",
      component: () => import("@/pages/cash-flow/index.vue"),
      meta: {
        breadcrumb: "Quản lý dòng tiền",
      },
    },
    {
      path: "consumables",
      name: "top-menu-consumables",
      component: () => import("@/pages/accounting/Consumables.vue"),
      meta: {
        breadcrumb: "Vật tư",
      },
    },
    {
      path: "revenue",
      name: "top-menu-revenue",
      component: () => import("@/pages/statistics/Revenue/Revenue.vue"),
      meta: {
        breadcrumb: "Doanh thu",
      },
    },
    {
      path: "revenue-attachment",
      name: "top-menu-revenue-attachment",
      component: () => import("@/pages/statistics/RevenueAttachment/RevenueAttachment.vue"),
      meta: {
        breadcrumb: "Dịch vụ điều trị",
      },
    },
    {
      path: "revenue-attachment-doctor",
      name: "top-menu-revenue-attachment-doctor",
      component: () => import("@/pages/statistics/RevenueAttachment/RevenueAttachmentMobile.vue"),
      meta: {
        breadcrumb: "Dịch vụ điều trị Bác sĩ",
      },
    },
    {
      path: "product",
      name: "top-menu-product-list",
      component: () => import("@/pages/product/Products.vue"),
      meta: {
        breadcrumb: "Danh sách sản phẩm",
      },
    },
    {
      path: "material",
      name: "top-menu-material-list",
      component: () => import("@/pages/material/Materials.vue"),
      meta: {
        breadcrumb: "Danh sách vật tư",
      },
    },
    {
      path: "operation",
      name: "top-menu-operation-list",
      component: () => import("@/pages/operation/Operations.vue"),
      meta: {
        breadcrumb: "Danh sách Nội dung điều trị",
      },
    },
    {
      path: "message",
      name: "top-menu-message",
      component: () => import("@/pages/setting/message.vue"),
      meta: {
        breadcrumb: "Tin nhắn",
      },
    },
    {
      path: "task",
      name: "top-menu-task",
      component: () => import("@/pages/task/Task.vue"),
      meta: {
        breadcrumb: "Công việc",
        defaultTab: "activity",
      },
    },
    {
      path: "task/:id",
      name: "task-detail",
      component: () => import("@/pages/task/Task.vue"),
      meta: {
        breadcrumb: "Chi tiết công việc",
        defaultTab: "activity",
      },
    },
    {
      path: "bundle",
      name: "top-menu-bundle",
      component: () => import("@/pages/bundle/BundleList.vue"),
      meta: {
        breadcrumb: "Danh sách nhóm",
      },
    },
    {
      path: "bundle/edit",
      name: "top-menu-bundle-edit",
      component: () => import("@/pages/bundle/BundleEdit.vue"),
      meta: {
        breadcrumb: "Chỉnh sửa nhóm",
      },
    },
    {
      path: "field/create",
      name: "top-menu-field-create",
      component: () => import("@/pages/bundle/FieldCreate.vue"),
      meta: {
        breadcrumb: "Field",
      },
    },
    {
      path: "auth/roles",
      name: "side-menu-auth-roles",
      component: () => import("@/pages/role/index.vue"),
      meta: {
        breadcrumb: "Danh sách vai trò",
      },
    },
    {
      path: "auth/permissions",
      name: "side-menu-auth-permissions",
      component: () => import("@/pages/role/permission.vue"),
      meta: {
        breadcrumb: "Permissions list",
      },
    },
    {
      path: "call-center",
      name: "top-menu-call-center",
      component: () => import("@/pages/customer-care/Calls.vue"),
      meta: {
        breadcrumb: "Quản lý tổng đài",
      },
    },
    {
      path: "configuration",
      name: "top-menu-configuration",
      component: () => import("@/pages/setting/ConfigurationList.vue"),
      meta: {
        breadcrumb: "Cấu hình",
      },
    },
    {
      path: "configuration/:id",
      name: "top-menu-configuration-detail",
      component: () => import("@/pages/setting/ConfigurationDetail.vue"),
      meta: {
        breadcrumb: "Chi tiết cấu hình",
      },
    },
    {
      path: "admin/logs",
      name: "top-menu-logs",
      component: () => import("@/pages/log/LogViewer.vue"),
      meta: {
        breadcrumb: "Logs Viewer",
      },
    },
    {
      path: "tags",
      name: "top-menu-tags",
      component: () => import("@/pages/setting/Tags.vue"),
      meta: {
        breadcrumb: "Quản lí Tag",
      },
    },
    {
      path: "deal-stage-history",
      name: "top-menu-deal-stage-history",
      component: () => import("@/pages/deal-stage-history/DealStageHistory.vue"),
      meta: {
        breadcrumb: "Khách hàng mới",
      },
    },
    {
      path: "deals",
      name: "top-menu-deals",
      component: () => import("@/pages/deals/Deals.vue"),
      meta: {
        breadcrumb: "Danh sách deal",
      },
    },
    {
      path: "persons",
      name: "top-menu-persons",
      component: () => import("@/pages/persons/Persons.vue"),
      meta: {
        breadcrumb: "Danh sách khách hàng",
      },
    },
    {
      path: "material-usage-summary",
      name: "top-menu-material-usage-summary",
      component: () => import("@/pages/report/material-usage/MaterialUsageSummary.vue"),
      meta: {
        breadcrumb: "BC Tổng hợp Vật tư",
      },
    },
    {
      path: "material-usage-detail",
      name: "top-menu-material-usage-detail",
      component: () => import("@/pages/report/material-usage/MaterialUsageDetail.vue"),
      meta: {
        breadcrumb: "BC Chi tiết Vật tư",
      },
    },
  ],
};

export default mainRoute;
