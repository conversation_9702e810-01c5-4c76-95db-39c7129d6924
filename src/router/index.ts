import { createRouter, createWebHistory } from "vue-router";

import { useAuth } from "@/pages/auth/config/auth-composable";

import mainRoute from "./main-route";
import mobileRoute from "./mobile-route";
import printRoute from "./print-route";

const routes = [
  mainRoute,
  mobileRoute,
  printRoute,
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/auth/Login.vue"),
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/pages/templates/Register.vue"),
  },
  {
    path: "/error-page",
    name: "error-page",
    component: () => import("@/pages/templates/ErrorPage.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/pages/templates/ErrorPage.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }

    const isSamePath = to.path === from.path;
    const isTabChange = to.query.tab !== from.query.tab;

    if (isSamePath && !isTabChange) {
      return false;
    }

    return { left: 0, top: 0, behavior: "smooth" };
  },
});

router.beforeEach((to, _from, next) => {
  const isAuthenticated = useAuth().isAuthenticated;

  if (to.name === "login" && isAuthenticated.value) {
    return next("/");
  }

  if (to.meta.requiresAuth && !isAuthenticated.value) {
    return next("/login");
  }

  next();
});

export default router;
