/**
 * Simple number utilities to handle floating point precision
 */

/**
 * Round number to 5 decimal places to fix floating point errors
 * @param value - Number to round
 * @returns Rounded number
 */
export const roundTo5Decimals = (value: number): number => {
  return Math.round(value * 100000) / 100000;
};

/**
 * Format number for display, removing trailing zeros
 * @param value - Number to format
 * @returns Clean string representation
 */
export const formatNumber = (value: number): string => {
  return parseFloat(value.toString()).toString();
};