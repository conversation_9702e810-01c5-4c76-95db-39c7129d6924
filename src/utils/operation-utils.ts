/**
 * Operation utility functions
 * Combines operation-helpers.ts and operationUtils.ts for better organization
 * Pure functions that don't depend on any store state
 */

/**
 * Generates the correct API identifier part for an operation.
 * Based on the operation's internal ID (number for existing, string for new),
 * this function returns an object with either `operation_id` or `operation_key`.
 * This is a pure utility function designed to create API-compliant payloads.
 *
 * @param operation - The operation object, containing an `id` that can be a number or a string.
 * @returns An object with a single key: `operation_id` (if id is a number) or `operation_key` (if id is a string).
 */
export function getOperationApiIdentifier(
  operation: { id: number | string }
): { operation_id: number } | { operation_key: string } {
  if (typeof operation.id === 'number' && operation.id > 0) {
    return { operation_id: operation.id };
  }
  return { operation_key: String(operation.id) };
}

/**
 * Determines if an operation identifier represents a custom operation
 * @param identifier - The operation identifier (string or number)
 * @returns true if custom operation, false if database operation
 */
export const isCustomOperation = (identifier: string | number): boolean => {
  const idStr = String(identifier);
  return isNaN(parseInt(idStr));
};

/**
 * Gets the unified string identifier for a MaterialUsage
 * @param usage - Object with operation_id or operation_key
 * @returns The unified string identifier
 */
export const getUsageIdentifier = (usage: { operation_id?: number; operation_key?: string }): string => {
  return usage.operation_id?.toString() ?? usage.operation_key ?? "";
};

/**
 * Formats a custom operation key into a readable display name
 * Converts snake_case or kebab-case to Title Case
 * @param key - The operation key (e.g., "special_coating_process")
 * @returns Formatted display name (e.g., "Special Coating Process")
 */
export const formatOperationNameFromKey = (key: string): string => {
  if (!key || typeof key !== 'string') {
    return 'Custom Operation'; // Safe fallback
  }
  
  // Convert snake_case or kebab-case to Title Case
  const words = key.replace(/_/g, ' ').replace(/-/g, ' ').split(' ');
  return words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};