import type { DataTablePageEvent, DataTableFilterMetaData } from 'primevue/datatable';
import type { Product, ProductListRequest, ProductListResponse } from '@/api/bcare-types-v2';
import type { LazyDataTableResponse } from '@/composables/useLazyDataTable';

/**
 * Type definition for Product API parameters
 * Based on the ProductListRequest interface from bcare-types-v2
 */
export interface ProductListParams {
  page: number;
  page_size: number;
  search?: string;
  type?: string;
  status?: number;
  // Additional filters that might be used
  group_id?: number;
  category_id?: number;
  unit_id?: number;
  collection?: string;
  // Sorting parameters
  sort_field?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * Maps PrimeVue DataTablePageEvent to Product API parameters
 * This function converts the PrimeVue event structure to the format expected by the Product API
 * 
 * @param event - DataTablePageEvent from PrimeVue DataTable
 * @returns ProductListParams - API parameters for the product list endpoint
 */
export function mapPrimeVueEventToApiParams(event: DataTablePageEvent): ProductListParams {
  // Calculate page number (PrimeVue uses 0-based indexing, API uses 1-based)
  const page = Math.floor(event.first / event.rows) + 1;
  
  // Base parameters
  const apiParams: ProductListParams = {
    page: page,
    page_size: event.rows,
  };

  // Process filters
  if (event.filters) {
    for (const [key, filterValue] of Object.entries(event.filters)) {
      // Handle different filter value types
      const value = typeof filterValue === 'object' && filterValue !== null && 'value' in filterValue
        ? (filterValue as DataTableFilterMetaData).value
        : filterValue;

      if (value !== null && value !== '' && value !== undefined) {
        switch (key) {
          case 'name':
            // Map 'name' filter to 'search' parameter
            apiParams.search = String(value);
            break;
          case 'type':
            apiParams.type = String(value);
            break;
          case 'status':
            // Convert status to number as expected by API
            apiParams.status = Number(value);
            break;
          case 'group_id':
            apiParams.group_id = Number(value);
            break;
          case 'category_id':
            apiParams.category_id = Number(value);
            break;
          case 'unit_id':
            apiParams.unit_id = Number(value);
            break;
          case 'collection':
            apiParams.collection = String(value);
            break;
          default:
            // For any other filters, add them as-is
            (apiParams as any)[key] = value;
        }
      }
    }
  }

  // Process sorting
  if (event.sortField) {
    apiParams.sort_field = typeof event.sortField === 'string' ? event.sortField : String(event.sortField);
    apiParams.sort_order = event.sortOrder === 1 ? 'asc' : 'desc';
  }

  return apiParams;
}

/**
 * Maps Product API response to the format expected by useLazyDataTable
 * This function converts the ProductListResponse to LazyDataTableResponse
 * 
 * @param response - ProductListResponse from the API
 * @returns LazyDataTableResponse<Product> - Response format expected by the composable
 */
export function mapApiResponseToLazyDataTable(
  response: ProductListResponse
): LazyDataTableResponse<Product> {
  return {
    items: response.products || [],
    total: response.total || 0,
  };
}

/**
 * Creates a fetch function for products that can be used with useLazyDataTable
 * This function wraps the listProducts API call and handles the response mapping
 * 
 * @param listProductsFn - The listProducts function from useProduct hook
 * @returns Function that fetches products and returns mapped response
 */
export function createFetchProductsApi(
  listProductsFn: (params: ProductListRequest) => Promise<ProductListResponse | undefined | null>
) {
  return async (params: ProductListParams): Promise<LazyDataTableResponse<Product>> => {
    try {
      // Call the API with the mapped parameters
      const response = await listProductsFn(params as ProductListRequest);
      
      // Handle undefined or null response
      if (!response) {
        return {
          items: [],
          total: 0,
        };
      }
      
      // Map the response to the expected format
      return mapApiResponseToLazyDataTable(response);
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  };
}

/**
 * Default filter configuration for Products DataTable
 * This matches the existing filter structure used in the Products page
 */
export const DEFAULT_PRODUCT_FILTERS = {
  name: { value: null, matchMode: 'contains' },
  type: { value: null, matchMode: 'equals' },
  status: { value: null, matchMode: 'equals' },
} as const;

/**
 * Default pagination configuration for Products DataTable
 */
export const DEFAULT_PRODUCT_PAGINATION = {
  rows: 10,
  rowsPerPageOptions: [10, 20, 50, 100],
} as const;

/**
 * Utility function to create the complete configuration for useLazyDataTable
 * This provides a convenient way to set up the composable with all necessary configurations
 * 
 * @param listProductsFn - The listProducts function from useProduct hook
 * @returns Configuration object for useLazyDataTable
 */
export function createProductDataTableConfig(
  listProductsFn: (params: ProductListRequest) => Promise<ProductListResponse | undefined | null>
) {
  return {
    fetchDataFn: createFetchProductsApi(listProductsFn),
    initialFilters: DEFAULT_PRODUCT_FILTERS,
    mapEventToParams: mapPrimeVueEventToApiParams,
    initialRows: DEFAULT_PRODUCT_PAGINATION.rows,
  };
}
