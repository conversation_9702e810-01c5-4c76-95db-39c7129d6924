import { computed } from "vue";
import { chunk, compact } from "lodash";

export const joinRunes = (...params: (string | number)[]): string => {
  return params.map((param) => String(param)).join("__");
};

export function normalizeVietnamese(str: string): string {
  return str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/đ/g, "d")
    .replace(/Đ/g, "D")
    .toLowerCase();
}

// Add fuzzy search function
export function fuzzySearch(text: string, searchTerm: string): boolean {
  if (!text || !searchTerm) return false;

  // Normalize both strings to lowercase and remove diacritics
  const normalizedText = normalizeVietnamese(text);
  const normalizedSearchTerm = normalizeVietnamese(searchTerm);

  // Split search term into words for multi-word search
  const searchWords = normalizedSearchTerm.split(/\s+/).filter((word) => word.length > 0);

  // Check if all search words are found in the normalized text
  return searchWords.every((word) => normalizedText.includes(word));
}

export function getShortName(fullName: string | (() => string)) {
  return computed(() => {
    const name = typeof fullName === "function" ? fullName() : fullName;
    const parts = name.split(" ");
    if (parts.length <= 1) return name;

    return (
      parts
        .slice(0, -1)
        .map((part) => part[0].toUpperCase())
        .join(".") +
      "." +
      parts[parts.length - 1]
    );
  });
}

export const selectAllText = (event: FocusEvent) => {
  const input = event.target as HTMLInputElement;
  input.select();
};

export const simpleGlobalId = () => {
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).substring(2, 11);
  return `${timestamp}-${randomPart}`;
};

/**
 * Normalizes Vietnamese phone numbers to standard 10-digit format
 * Handles cases where leading 0 is missing
 */
export function normalizeVietnamesePhone(phone: string): string {
  if (!phone) return phone;

  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, "");

  // Already correct format (10 digits starting with 0)
  if (/^0\d{9}$/.test(digits)) {
    return digits;
  }

  // Missing leading 0 (9 digits)
  if (/^\d{9}$/.test(digits)) {
    return `0${digits}`;
  }

  // Return original if not matching expected patterns
  return phone;
}

/**
 * Convert numbers to Vietnamese words
 * Optimized with lodash utilities and proper Vietnamese pronunciation rules
 */
export const numberToWords = (amount: number): string => {
  if (amount === 0) return "không đồng";
  if (amount < 0) return "âm " + numberToWords(Math.abs(amount));
  
  const ones = ["", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"];
  const scales = ["", "nghìn", "triệu", "tỷ"];
  
  // Special cases for Vietnamese pronunciation
  const convertOnes = (digit: number, tensDigit: number = 0): string => {
    if (digit === 5 && tensDigit > 1) return "lăm"; // 25 -> hai mươi lăm
    if (digit === 1 && tensDigit > 1) return "mốt"; // 21 -> hai mươi mốt
    return ones[digit];
  };
  
  const convertTens = (tens: number, onesDigit: number): string => {
    if (tens === 0 && onesDigit > 0) return convertOnes(onesDigit);
    if (tens === 1 && onesDigit === 0) return "mười";
    if (tens === 1 && onesDigit === 5) return "mười lăm";
    if (tens === 1) return `mười ${convertOnes(onesDigit)}`;
    if (onesDigit === 0) return `${ones[tens]} mươi`;
    return `${ones[tens]} mươi ${convertOnes(onesDigit, tens)}`;
  };
  
  const convertHundreds = (num: number): string => {
    if (num === 0) return "";
    if (num < 10) return convertOnes(num);
    if (num < 100) return convertTens(Math.floor(num / 10), num % 10);
    
    const hundreds = Math.floor(num / 100);
    const remainder = num % 100;
    const tens = Math.floor(remainder / 10);
    const onesDigit = remainder % 10;
    
    let result = `${ones[hundreds]} trăm`;
    
    if (remainder === 0) return result;
    if (tens === 0) return `${result} linh ${convertOnes(onesDigit)}`;
    
    return `${result} ${convertTens(tens, onesDigit)}`;
  };
  
  // Convert number to groups of 3 digits using lodash chunk
  const digits = amount.toString().split('').reverse();
  const groups = chunk(digits, 3).map(group => 
    parseInt(group.reverse().join(''))
  );
  
  // Build result from groups
  const parts = groups.map((group, index) => {
    if (group === 0) return "";
    const groupWords = convertHundreds(group);
    return index > 0 ? `${groupWords} ${scales[index]}` : groupWords;
  });
  
  // Use lodash compact to remove empty strings and join
  return compact(parts).reverse().join(' ') + ' đồng';
};