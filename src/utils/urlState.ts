/**
 * URL State Management Utilities for DataTable V2
 * 
 * Provides functionality to serialize/deserialize DataTable state to/from URL parameters
 * for shareability, bookmarking, and browser history support.
 */

import { useRouter, useRoute } from 'vue-router';
import { ref, watch, nextTick } from 'vue';
import type { DataTablePageEvent } from 'primevue/datatable';

export interface DataTableState {
  filters: Record<string, any>;
  rows: number;
  page: number;
  first: number;
  sortField?: string;
  sortOrder?: number;
}

export interface CompressedState {
  f: Record<string, any>; // filters
  r: number;              // rows per page
  p: number;              // current page
  s?: {                   // sorting
    field: string;
    order: number;
  };
}

export interface UrlStatePayload {
  v: string;              // version for backward compatibility
  d: CompressedState;     // compressed data
}

export interface UseUrlStateOptions {
  key: string;                    // URL parameter key
  persistedFields?: string[];     // Which fields to persist ['filters', 'pagination', 'sorting']
  debounceMs?: number;           // Debounce delay for URL updates (default: 300ms)
  enableHistory?: boolean;       // Enable browser history management (default: true)
}

/**
 * Compress state to minimize URL length
 */
function compressState(state: DataTableState): CompressedState {
  const compressed: CompressedState = {
    f: state.filters || {},
    r: state.rows || 10,
    p: state.page || 0
  };

  if (state.sortField) {
    compressed.s = {
      field: state.sortField,
      order: state.sortOrder || 1
    };
  }

  return compressed;
}

/**
 * Decompress state from URL format
 */
function decompressState(compressed: CompressedState): DataTableState {
  return {
    filters: compressed.f || {},
    rows: compressed.r || 10,
    page: compressed.p || 0,
    first: (compressed.p || 0) * (compressed.r || 10),
    sortField: compressed.s?.field,
    sortOrder: compressed.s?.order
  };
}

/**
 * Serialize state to URL-safe format
 */
export function serializeStateToUrl(state: DataTableState): string {
  try {
    const payload: UrlStatePayload = {
      v: '1.0',
      d: compressState(state)
    };
    
    const jsonString = JSON.stringify(payload);
    return btoa(jsonString);
  } catch (error) {
    console.warn('Failed to serialize state to URL:', error);
    return '';
  }
}

/**
 * Deserialize state from URL parameter
 */
export function deserializeStateFromUrl(urlParam: string): DataTableState | null {
  try {
    if (!urlParam) return null;
    
    const jsonString = atob(urlParam);
    const payload: UrlStatePayload = JSON.parse(jsonString);
    
    // Version checking for backward compatibility
    if (payload.v !== '1.0') {
      console.warn('Unsupported URL state version:', payload.v);
      return null;
    }
    
    return decompressState(payload.d);
  } catch (error) {
    console.warn('Failed to deserialize state from URL:', error);
    return null;
  }
}

/**
 * Create a DataTablePageEvent from state
 */
export function createEventFromState(state: DataTableState): DataTablePageEvent {
  return {
    first: state.first || 0,
    rows: state.rows || 10,
    page: state.page || 0,
    pageCount: 0, // Will be calculated by DataTable
    filters: state.filters || {},
    sortField: state.sortField,
    sortOrder: state.sortOrder,
    multiSortMeta: undefined,
    originalEvent: new Event('url-state-load'),
    filterMatchModes: {}
  };
}

/**
 * Extract state from DataTablePageEvent
 */
export function extractStateFromEvent(event: DataTablePageEvent): DataTableState {
  return {
    filters: event.filters || {},
    rows: event.rows || 10,
    page: event.page || 0,
    first: event.first || 0,
    sortField: event.sortField,
    sortOrder: event.sortOrder
  };
}

/**
 * Composable for URL state management
 */
export function useUrlState(options: UseUrlStateOptions) {
  const router = useRouter();
  const route = useRoute();
  
  const {
    key,
    persistedFields = ['filters', 'pagination', 'sorting'],
    debounceMs = 300,
    enableHistory = true
  } = options;

  // Debounce timer
  let debounceTimer: NodeJS.Timeout | null = null;

  /**
   * Load initial state from URL
   */
  function loadStateFromUrl(): DataTableState | null {
    const urlParam = route.query[key] as string;
    return deserializeStateFromUrl(urlParam);
  }

  /**
   * Update URL with current state (debounced)
   */
  function updateUrl(state: DataTableState) {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(async () => {
      try {
        const serializedState = serializeStateToUrl(state);
        
        const newQuery = { ...route.query };
        if (serializedState) {
          newQuery[key] = serializedState;
        } else {
          delete newQuery[key];
        }

        // Update URL without triggering navigation
        await router.replace({
          path: route.path,
          query: newQuery
        });
      } catch (error) {
        console.warn('Failed to update URL with state:', error);
      }
    }, debounceMs);
  }

  /**
   * Clear state from URL
   */
  function clearUrlState() {
    const newQuery = { ...route.query };
    delete newQuery[key];
    
    router.replace({
      path: route.path,
      query: newQuery
    });
  }

  /**
   * Check if URL contains state for this key
   */
  function hasUrlState(): boolean {
    return !!route.query[key];
  }

  return {
    loadStateFromUrl,
    updateUrl,
    clearUrlState,
    hasUrlState,
    serializeStateToUrl,
    deserializeStateFromUrl,
    createEventFromState,
    extractStateFromEvent
  };
}

/**
 * Utility to validate URL state length
 */
export function validateUrlStateLength(state: DataTableState, maxLength = 2000): boolean {
  const serialized = serializeStateToUrl(state);
  return serialized.length <= maxLength;
}

/**
 * Utility to clean up filters (remove empty values)
 */
export function cleanFilters(filters: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(filters)) {
    if (value && typeof value === 'object' && 'value' in value) {
      if (value.value !== null && value.value !== '' && value.value !== undefined) {
        cleaned[key] = value;
      }
    } else if (value !== null && value !== '' && value !== undefined) {
      cleaned[key] = value;
    }
  }
  
  return cleaned;
}
