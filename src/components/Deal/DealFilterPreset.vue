<template>
  <div>
    <div class="flex flex-wrap gap-2">
      <!-- Deal Presets -->
      <template v-for="preset in dealPresets" :key="preset.type">
        <!-- Use PrimeVue Button -->
        <Button
          :label="preset.name"
          :icon="getButtonProps(preset).icon"
          :severity="getButtonProps(preset).severity"
          :outlined="getButtonProps(preset).outlined"
          @click="() => handleSelectPreset(preset.type)"
          :class="{
            'border border-gray-300 text-sm': true,
            'border-0': selectedPreset === preset.type,
          }"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import { useDealDatatableStore } from "@/stores/deal-datatable-store";
import { useAuthStore } from "@/stores/auth-store";
import { useSettingGlobalStore } from "@/stores/setting-global-store";

import {
  DEAL_FILTER_PRESETS,
  type DealFilterPreset,
} from "@/pages/customer/components/DealsTab/constants";

interface Props {
  modelValue?: DealFilterPreset;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: DEAL_FILTER_PRESETS.ALL,
});

const emit = defineEmits<{
  "update:modelValue": [value: DealFilterPreset];
}>();

const dealStore = useDealDatatableStore();
const authStore = useAuthStore();
const settingGlobalStore = useSettingGlobalStore();

// Updated PresetDefinition interface
interface PresetDefinition {
  type: DealFilterPreset;
  name: string;
  icon: string;
  severity?: "success" | "info" | "danger" | "primary" | "secondary" | "warning" | "contrast";
}

// Get visible preset filters from settings
const visiblePresetFilters = computed(() => {
  const dealSettings = settingGlobalStore.getSettingByKey("deal");
  const presetFiltersGroup = dealSettings?.find(
    (setting) => setting.type === "group" && setting.field_name === "preset_filters_visibility",
  );
  const visibleFilters = presetFiltersGroup?.children?.find(
    (setting) => setting.field_name === "visible_preset_filters",
  );
  return (
    visibleFilters?.options?.filter((option) => option.value).map((option) => option.field_name) ||
    []
  );
});

// Mapping preset types to setting field names
const presetTypeToFieldName: Record<DealFilterPreset, string> = {
  [DEAL_FILTER_PRESETS.ALL]: "all",
  [DEAL_FILTER_PRESETS.MY_DEALS]: "my_deals",
  [DEAL_FILTER_PRESETS.RELATED_DEALS]: "related_deals",
  [DEAL_FILTER_PRESETS.CREATED_TODAY]: "created_today",
  [DEAL_FILTER_PRESETS.WON_TODAY]: "won_today",
  [DEAL_FILTER_PRESETS.LOST_TODAY]: "lost_today",
  [DEAL_FILTER_PRESETS.ACTIVE_DEALS]: "active_deals",
  [DEAL_FILTER_PRESETS.STAGE_DEN_PHONG_KHAM]: "stage_den_phong_kham",
  [DEAL_FILTER_PRESETS.STAGE_DA_TV]: "stage_da_tv",
  [DEAL_FILTER_PRESETS.STAGE_CHUA_COC]: "stage_chua_coc",
  [DEAL_FILTER_PRESETS.STAGE_DA_COC]: "stage_da_coc",
  [DEAL_FILTER_PRESETS.STAGE_GAP_BS]: "stage_gap_bs",
  [DEAL_FILTER_PRESETS.STAGE_TONG_QUAT]: "stage_tong_quat",
};

// All available preset definitions
const allPresetDefinitions: PresetDefinition[] = [
  { type: DEAL_FILTER_PRESETS.ALL, name: "Tất cả", icon: "pi pi-list", severity: "primary" },
  {
    type: DEAL_FILTER_PRESETS.MY_DEALS,
    name: "Deal đã tạo",
    icon: "pi pi-user",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.WON_TODAY,
    name: "Deal won",
    icon: "pi pi-trophy",
    severity: "success",
  },
  {
    type: DEAL_FILTER_PRESETS.LOST_TODAY,
    name: "Deal lost",
    icon: "pi pi-shield",
    severity: "danger",
  },
  {
    type: DEAL_FILTER_PRESETS.ACTIVE_DEALS,
    name: "Deal active",
    icon: "pi pi-bolt",
    severity: "info",
  },
  // Stage-specific presets
  {
    type: DEAL_FILTER_PRESETS.STAGE_DEN_PHONG_KHAM,
    name: "Đến phòng khám",
    icon: "pi pi-home",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.STAGE_DA_TV,
    name: "Đã TV",
    icon: "pi pi-comments",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.STAGE_CHUA_COC,
    name: "Chưa cọc",
    icon: "pi pi-clock",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.STAGE_DA_COC,
    name: "Đã cọc",
    icon: "pi pi-check-circle",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.STAGE_GAP_BS,
    name: "Gặp BS",
    icon: "pi pi-user-plus",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.STAGE_TONG_QUAT,
    name: "Tổng quát",
    icon: "pi pi-eye",
    severity: "primary",
  },
];

// Filter presets based on visibility settings
const dealPresets = computed(() => {
  return allPresetDefinitions.filter((preset) => {
    const fieldName = presetTypeToFieldName[preset.type];
    return visiblePresetFilters.value.includes(fieldName);
  });
});

const selectedPreset = computed<DealFilterPreset>({
  get: () => props.modelValue,
  set: (value: DealFilterPreset) => emit("update:modelValue", value),
});

const getButtonProps = (preset: PresetDefinition) => {
  const isSelected = selectedPreset.value === preset.type;
  if (isSelected) {
    return {
      severity: preset.severity || "primary",
      outlined: false,
      icon: preset.icon,
    };
  } else {
    return {
      severity: "secondary",
      outlined: true,
      icon: preset.icon,
    };
  }
};

const handleSelectPreset = (presetType: DealFilterPreset): void => {
  if (selectedPreset.value !== presetType) {
    selectedPreset.value = presetType;

    const currentUserId = authStore.user?.user?.id;
    if (!currentUserId && presetType === DEAL_FILTER_PRESETS.MY_DEALS) {
      return;
    }

    dealStore.applyFilterPreset(presetType, currentUserId || 0);
  }
};
</script>
