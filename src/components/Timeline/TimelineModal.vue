<script setup lang="ts">
import Dialog from "primevue/dialog";
import { computed } from "vue";

import { HistoryEntry } from "@/api/bcare-types-v2";

import HistoryTimeline from "./HistoryTimeline.vue";

export type TimelineModalProps = {
  modelValue: boolean;
  history: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
  reverseOrder?: boolean;
};

const props = defineProps<TimelineModalProps>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
</script>

<template>
  <Dialog
    v-model:visible="visible"
    modal
    :draggable="false"
    dismissableMask
    header="Lịch sử chỉnh sửa"
    :style="{ width: '90vw', maxWidth: '768px' }"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <template v-if="history.length || (creatorId && createdAt)">
        <HistoryTimeline :history="history" :creator-id="creatorId" :created-at="createdAt" :reverse-order="reverseOrder" />
      </template>
      <template v-else>
        <div class="flex flex-col items-center justify-center py-12 text-center">
          <i class="pi pi-history mb-4 text-4xl text-gray-300"></i>
          <p class="text-gray-500">Chưa có lịch sử chỉnh sửa</p>
        </div>
      </template>
    </div>
  </Dialog>
</template>
