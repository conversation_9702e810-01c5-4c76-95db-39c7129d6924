<script setup lang="ts">
import { computed, onMounted, watch } from "vue";
import Tag from "primevue/tag";

import type { HistoryListRequest, TagResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import useHistory from "@/hooks/useHistory";
import useLocation from "@/hooks/useLocation";
import usePipeline from "@/hooks/usePipeline";
import useUser from "@/hooks/useUser";
import useTag from "@/hooks/useTag";
import { formatCurrency } from "@/utils/helper";
import { formatRelativeTime } from "@/utils/time-helper";
import Empty from "@/base-components/Empty";
import useTerm from "@/hooks/useTerm";

const RICH_TEXT_FIELDS = new Set(["notes", "body", "note"]);
const USER_ID_FIELDS = new Set(["user_id", "assigned_by", "creator_id", "doctor_id"]);
const DATE_FIELDS = new Set([
  "start_time",
  "end_time",
  "changed_at",
  "created_at",
  "updated_at",
  "timestamp",
  "arrived_at",
  "start_date",
  "due_date",
  "end_date",
  "date_of_birth",
]);
const TIME_SENSITIVE_DATE_FIELDS = new Set([
  // For :showTime prop
  "start_time",
  "end_time",
  "changed_at",
  "created_at",
  "updated_at",
  "timestamp",
  "arrived_at",
]);
const FIELDS_WITHOUT_OLD_VALUE = new Set([
  "current_serial",
  "arrived_at",
  "extra_notes",
  "person_field",
  ...RICH_TEXT_FIELDS,
]);
const TECHNICAL_FIELDS = new Set([
  "id",
  "created_at",
  "updated_at",
  "deleted_at",
  "version",
  "person_id",
  "deal_id",
  "related_id",
  "entity_id",
]);

const props = defineProps<{
  entityType: "person" | "deal";
  entityId: number;
}>();

const { isLoading, error, histories, listHistories } = useHistory();
const { getUserById } = useUser();
const { getProvinceById, getDistrictById, getWardById } = useLocation();
const { getStagesByPipelineID } = usePipeline();
const { getTagById } = useTag({ useStore: true, autoLoad: true });
const { getTermNameById } = useTerm();
const pipelineId = 2;

const fetchHistory = async () => {
  const request: HistoryListRequest = {
    page: 1,
    page_size: 300,
    filter: {
      entity_type: props.entityType,
      entity_id: props.entityId,
    },
    order_by: "changed_at DESC",
  };
  await listHistories(request);
};

onMounted(fetchHistory);
watch([() => props.entityId, () => props.entityType], fetchHistory, { immediate: false });

const getFieldLabel = (field: string) => {
  const labels: Record<string, string> = {
    state: "Giai đoạn",
    status: "Trạng thái",
    notes: "Ghi chú",
    note: "Nội dung",
    body: "Nội dung",
    type: "Loại",
    full_name: "Họ tên",
    phone: "Số điện thoại",
    email: "Email",
    date_of_birth: "Ngày sinh",
    gender: "Giới tính",
    address_number: "Số nhà",
    province_id: "Tỉnh/Thành phố",
    district_id: "Quận/Huyện",
    ward_id: "Phường/Xã",
    job_id: "Nghề nghiệp",
    source_id: "Nguồn",
    person_field: "Thông tin thêm",
    stage_id: "Stage",
    name: "Tên Deal",
    total_amount: "Tổng tiền",
    user_id: "Người dùng",
    doctor_id: "Bác sĩ",
    person_id: "Khách hàng",
    deal_id: "Deal",
    creator_id: "Người tạo",
    code: "Mã hồ sơ",
    treatment_id: "Loại điều trị",
    treatment_status_id: "Trạng thái điều trị",
    special_note: "Ghi chú đặc biệt",
    medical_condition: "Bệnh lý",
    description: "Ghi chú",
    pancake_link: "Link pancake",
    has_zalo: "Sử dụng zalo",
    secondary_phone: "Sdt phụ",
    bank_account_name: "Chủ tài khoản",
    bank_account_number: "Số tài khoản",
    bank: "Ngân hàng",
    bank_branch: "Chi nhánh",
    role: "Vai trò",
  };
  return labels[field] || field;
};

const getRelatedEntityLabel = (related: string) => {
  const labels: Record<string, string> = {
    PersonAssignment: "Nhân viên liên quan",
    DealUser: "Nhân viên liên quan",
    TagDeal: "Thẻ",
    TagPerson: "Thẻ",
  };
  return labels[related] || related;
};

const getRoleLabel = (role: string) => {
  const labels: Record<string, string> = {
    doctor: "Bác sĩ",
    counselor: "Tư vấn viên",
    sale: "Sale",
    customer_care: "Chăm sóc khách hàng",
    treatment_doctor: "Bác sĩ điều trị",
    consultant_doctor: "Bác sĩ tư vấn",
    consultant: "Tư vấn viên",
    doctor_assistant: "Trợ lý bác sĩ",
  };
  return labels[role] || role;
};

const formatDisplayValue = (field: string, value: any): string => {
  if (value === null || typeof value === "undefined") {
    if (RICH_TEXT_FIELDS.has(field)) {
      return '<i class="pi pi-trash text-red-500 mr-1"></i> <span class="text-red-500">Đã xóa</span>';
    }
    return '<span class="text-gray-500">Chưa có</span>';
  }

  if (field === "province_id") {
    const province = getProvinceById.value(Number(value));
    return province?.name || String(value);
  }
  if (field === "district_id") {
    const district = getDistrictById.value(Number(value));
    return district?.name || String(value);
  }
  if (field === "ward_id") {
    const ward = getWardById.value(Number(value));
    return ward?.name || String(value);
  }
  if (field === "stage_id") {
    const stages = getStagesByPipelineID(pipelineId);
    const stage = stages?.find((stage) => stage.id === Number(value));
    return stage?.name || String(value);
  }
  if (field === "source_id") {
    return getTermNameById("nguon", Number(value));
  }
  if (field === "treatment_id") {
    return getTermNameById("loai_dieu_tri", Number(value));
  }
  if (field === "treatment_status_id") {
    return getTermNameById("trang_thai_dieu_tri", Number(value));
  }

  if (field === "total_amount") {
    return formatCurrency(Number(value));
  }

  if (DATE_FIELDS.has(field)) {
    try {
      new Date(value).toISOString();
      return value;
    } catch (e) {
      return `<span class="text-red-500">Invalid Date</span>`;
    }
  }

  if (typeof value === "boolean") return value ? "Có" : "Không";
  if (typeof value === "object") {
    return '<span class="flex min-w-0 flex-1 flex-wrap items-baseline gap-1">Đã cập nhật</span>';
  }
  if (RICH_TEXT_FIELDS.has(field)) {
    return String(value);
  }

  if (USER_ID_FIELDS.has(field)) {
    return value;
  }
  if (field === "tag_id") {
    const tag = (getTagById as (id: number) => TagResponse | undefined)(Number(value));
    return `<span class="flex min-w-0 flex-1 flex-wrap items-baseline gap-1">#${tag?.name}</span>`;
  }
  if (field === "role") {
    return getRoleLabel(value);
  }

  return String(value);
};

const getGeneralChanges = (operation: string, before: any, after: any) => {
  const allKeys = new Set([...Object.keys(before || {}), ...Object.keys(after || {})]);
  return Array.from(allKeys)
    .filter((key) => !TECHNICAL_FIELDS.has(key) && !key.startsWith("_"))
    .map((key) => ({
      key,
      oldValue: before ? before[key] : undefined,
      newValue: after ? after[key] : undefined,
    }))
    .filter((change) => {
      if (operation?.toUpperCase() === "UPDATE") {
        return JSON.stringify(change.oldValue) !== JSON.stringify(change.newValue);
      }
      if (operation?.toUpperCase() === "CREATE") {
        return typeof change.newValue !== "undefined" && change.newValue !== null;
      }
      if (operation?.toUpperCase() === "DELETE") {
        return typeof change.oldValue !== "undefined" && change.oldValue !== null;
      }
      return true;
    });
};

const timelineEvents = computed(() => {
  return histories.value.map((record) => {
    let changes;
    if (record.related) {
      changes = getGeneralChanges(record.operation, record.before, record.after);
    } else {
      changes = getChanges(record.before, record.after);
    }
    const timestamp = record.changed_at || record.created_at;
    return {
      timestamp: timestamp,
      record: record,
      user: getUserById(record.user_id ?? 0),
      icon: getOperationIconClass(record.operation),
      colorClass: getOperationColorClass(record.operation),
      changes: changes.map(change => ({
        ...change,
        field: change.key, // Map key to field for consistency
        old_value: change.oldValue,
        new_value: change.newValue
      })),
    };
  });
});

const getChanges = (
  before: any,
  after: any,
): Array<{ key: string; oldValue: any; newValue: any }> => {
  if (
    typeof before !== "object" ||
    typeof after !== "object" ||
    before === null ||
    after === null
  ) {
    return [];
  }

  const changedKeys = new Set<string>();
  const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

  allKeys.forEach((key) => {
    if (JSON.stringify(before[key]) !== JSON.stringify(after[key])) {
      if (!["updated_at", "version", "created_at"].includes(key)) {
        changedKeys.add(key);
      }
    }
  });

  return Array.from(changedKeys)
    .filter((key) => !key.startsWith("_"))
    .map((key) => ({
      key: key,
      oldValue: before[key],
      newValue: after[key],
    }));
};

const getOperationIconClass = (operation?: string) => {
  switch (operation?.toUpperCase()) {
    case "CREATE":
      return "pi pi-plus-circle";
    case "UPDATE":
      return "pi pi-pencil";
    case "DELETE":
      return "pi pi-trash";
    default:
      return "pi pi-info-circle";
  }
};

const getEventIcon = (field: string) => {
  const icons: Record<string, string> = {
    state: "pi pi-check-circle",
    priority: "pi pi-flag",
    title: "pi pi-list",
    notes: "pi pi-clipboard",
    note: "pi pi-clipboard",
    due_date: "pi pi-calendar-times",
    start_date: "pi pi-calendar-plus",
    start_time: "pi pi-clock",
    end_time: "pi pi-clock",
    type: "pi pi-tag",
    person_id: "pi pi-user",
    users: "pi pi-users",
    user_id: "pi pi-user",
    assigned_by: "pi pi-user",
    creator_id: "pi pi-user",
    doctor_id: "pi pi-user",
    stage_id: "pi pi-flag",
    name: "pi pi-list",
    total_amount: "pi pi-money-bill",
    province_id: "pi pi-map-marker",
    district_id: "pi pi-map-marker",
    ward_id: "pi pi-map-marker",
    job_id: "pi pi-briefcase",
    source_id: "pi pi-share-alt",
    treatment_id: "pi pi-heartbeat",
    treatment_status_id: "pi pi-check-circle",
    tag_id: "pi pi-tag",
    role: "pi pi-users",
    default: "pi pi-sync",
  };
  return icons[field] || icons.default;
};

const getOperationColorClass = (operation?: string) => {
  switch (operation?.toUpperCase()) {
    case "CREATE":
      return "bg-green-500";
    case "UPDATE":
      return "bg-blue-500";
    case "DELETE":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const isShowOldValue = (field: string, operation?: string) => {
  if (operation?.toUpperCase() === "CREATE") return false;
  // Use the constant FIELDS_WITHOUT_OLD_VALUE
  return !FIELDS_WITHOUT_OLD_VALUE.has(field);
};
</script>

<template>
  <div class="w-full">
    <div v-if="isLoading" class="py-6 text-center text-gray-500">
      <i class="pi pi-spin pi-spinner mr-2" />
      Đang tải lịch sử...
    </div>

    <div v-else-if="error" class="m-4 rounded bg-red-50 p-4 text-center text-red-600">
      <i class="pi pi-exclamation-triangle mr-2" />
      Có lỗi xảy ra khi tải lịch sử
    </div>

    <div v-else-if="timelineEvents.length > 0" class="w-full space-y-4">
      <div v-for="(event, index) in timelineEvents" :key="index" class="relative">
        <!-- Revision Item -->
        <div class="flex gap-3">
          <!-- Avatar & Connector -->
          <div class="flex flex-col items-center">
            <!-- User Avatar Only -->
            <UserAvatar :user="event.user" class="h-8 w-8 flex-shrink-0" />

            <!-- Connector Line (aligned with avatar center) -->
            <div
              v-if="index < timelineEvents.length - 1"
              class="mt-2 min-h-6 w-0.5 flex-1 bg-gray-200"
            />
          </div>

          <!-- User Info & Content -->
          <div class="flex min-w-0 gap-2 pb-4">
            <!-- User Info Header -->
            <div class="mb-2 flex flex-col">
              <span class="text-sm font-medium text-gray-900">
                {{ event.user?.name || "Unknown User" }}
              </span>
              <span class="text-xs text-gray-500">
                {{ formatRelativeTime(event.record.changed_at || event.record.created_at) }}
              </span>
            </div>

            <!-- Changes -->
            <div class="space-y-2">
              <div v-for="(change, idx) in event.changes" :key="idx" class="text-sm">
                <!-- Field Label -->
                <div
                  v-if="change.field !== 'created_at'"
                  class="flex flex-wrap items-start gap-2"
                >
                  <Tag
                    class="flex-shrink-0 text-xs !font-medium"
                    :value="getFieldLabel(change.key)"
                    severity="info"
                  >
                    <template #icon>
                      <i :class="getEventIcon(change.key) + ' text-xs'" />
                    </template>
                  </Tag>

                  <!-- Change Content -->
                  <div class="min-w-0 flex-1 text-xs">
                    <!-- User ID Changes -->
                    <template v-if="USER_ID_FIELDS.has(change.key)">
                      <div class="flex items-center gap-2">
                        <UserAvatar
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                          :user-id="change.oldValue"
                          class="h-6 w-6"
                        />
                        <i
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue && change.newValue"
                          class="pi pi-arrow-right text-xs text-gray-400"
                        />
                        <UserAvatar
                          v-if="change.newValue"
                          :user-id="change.newValue"
                          class="h-6 w-6"
                        />
                        <span
                          v-else-if="!change.newValue"
                          v-html="formatDisplayValue(change.key, change.newValue)"
                          class="font-medium text-gray-900"
                        />
                      </div>
                    </template>

                    <!-- Role Changes -->
                    <template v-else-if="change.key === 'role'">
                      <div class="flex items-center gap-2">
                        <span
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                          class="text-gray-500 line-through"
                        >
                          {{ getRoleLabel(change.oldValue) }}
                        </span>
                        <i
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue && change.newValue"
                          class="pi pi-arrow-right text-xs text-gray-400"
                        />
                        <span v-if="change.newValue" class="font-medium text-gray-900">
                          {{ getRoleLabel(change.newValue) }}
                        </span>
                        <span v-else class="font-medium text-gray-900">
                          <span class="text-gray-500">Chưa có</span>
                        </span>
                      </div>
                    </template>

                    <!-- Date Changes -->
                    <template v-else-if="DATE_FIELDS.has(change.key)">
                      <div class="flex items-center gap-2">
                        <DateTime
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                          :time="change.oldValue"
                          :showTime="TIME_SENSITIVE_DATE_FIELDS.has(change.key)"
                          size="xs"
                          :showIcon="false"
                          class="text-gray-500 line-through"
                        />
                        <i
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue && change.newValue"
                          class="pi pi-arrow-right text-xs text-gray-400"
                        />
                        <DateTime
                          v-if="change.newValue"
                          :time="change.newValue"
                          :showTime="TIME_SENSITIVE_DATE_FIELDS.has(change.key)"
                          size="xs"
                          :showIcon="false"
                          class="font-medium text-gray-900"
                        />
                        <span v-else class="font-medium text-gray-900">
                          <span class="text-gray-500">Chưa có</span>
                        </span>
                      </div>
                    </template>

                    <!-- Tag Changes -->
                    <template v-else-if="change.key === 'tag_id'">
                      <div class="flex items-center gap-2">
                        <span
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                          class="text-gray-500 line-through"
                          v-html="formatDisplayValue(change.key, change.oldValue)"
                        />
                        <i
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue && change.newValue"
                          class="pi pi-arrow-right text-xs text-gray-400"
                        />
                        <span
                          class="font-medium text-gray-900"
                          v-html="formatDisplayValue(change.key, change.newValue)"
                        />
                      </div>
                    </template>

                    <!-- Generic Changes -->
                    <template v-else>
                      <div class="flex flex-wrap items-center gap-2">
                        <span
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                          class="text-gray-500 line-through"
                          v-html="formatDisplayValue(change.key, change.oldValue)"
                        />
                        <i
                          v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue && change.newValue"
                          class="pi pi-arrow-right text-xs text-gray-400"
                        />
                        <span
                          class="font-medium text-gray-900"
                          v-html="formatDisplayValue(change.key, change.newValue)"
                        />
                      </div>
                    </template>
                  </div>
                </div>

                <!-- Creation Event -->
                <div v-else class="flex items-center gap-2">
                  <Tag
                    class="flex-shrink-0 text-xs font-medium"
                    value="Đã tạo"
                    severity="success"
                  >
                    <template #icon>
                      <i class="pi pi-plus-circle text-xs" />
                    </template>
                  </Tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else><Empty /></div>
  </div>
</template>

<style scoped>
:deep(.prose) {
  overflow-wrap: break-word;
}
</style>
