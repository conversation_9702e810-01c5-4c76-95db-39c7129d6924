<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import dayjs from "dayjs";
import Tag from "primevue/tag";
import { computed } from "vue";

import { HistoryEntry } from "@/api/bcare-types-v2";
import PriorityChip from "@/components/Chip/PriorityChip.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import useConstant from "@/hooks/useConstant";
import useUser from "@/hooks/useUser";
import { parseExtraNotesV2 } from "@/pages/customer/components/AppointmentTab/utils";
import { formatRelativeTime } from "@/utils/time-helper";
import { StateSelectBtn } from "../Select";

const props = defineProps<{
  history?: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
  usedAtComponent?: string;
  reverseOrder?: boolean;
}>();

const { getUserById } = useUser();
const { getConstants } = useConstant();
// Format helpers

const formatValue = (field: string, value: string | number) => {
  if (!value && !["notes", "body", "note"].includes(field)) return "Chưa có";

  const dateFields = ["start_date", "due_date", "end_date", "start_time", "end_time", "arrived_at"];
  if (dateFields.includes(field)) {
    return useDateFormat(new Date(value), "DD/MM/YYYY HH:mm").value;
  }

  switch (field) {
    case "reminder_status":
      return value === 1 ? "Đã nhắc" : "Chưa nhắc";

    case "type":
      if (props.usedAtComponent === "task") {
        return value;
      }
      return getConstants.value?.appointment_type?.[value] || "Không xác định";

    case "extra_notes": {
      const newParsed = parseExtraNotesV2(value as string);

      const formatExtraNotes = (data: ReturnType<typeof parseExtraNotesV2>) => {
        const parts = [];

        if (data.expected_task.length) {
          parts.push(
            `<span class="font-medium">CV dự kiến:</span> ${data.expected_task.join(", ")}`,
          );
        }

        if (data.expected_task_other.length) {
          parts.push(
            `<span class="font-medium">CV dự kiến:</span> ${data.expected_task_other.join(", ")}`,
          );
        }

        if (data.reminder_fee) {
          parts.push(
            `<span class="font-medium">Nhắc phí:</span> ${Number(data.reminder_fee).toLocaleString("vi-VN")} đ`,
          );
        }

        return parts.length ? parts.join("<br/>") : "Không có";
      };

      return formatExtraNotes(newParsed);
    }

    case "current_serial": {
      return `Lần ${value}`;
    }

    case "body":
    case "note":
    case "notes": {
      if (!!value) {
        return value as string;
      }
      return '<i class="pi pi-trash text-red-500"></i> <span class="text-red-500">Đã xóa</span>';
    }

    default:
      return value;
  }
};

// Field mapping
const getFieldLabel = (field: string) => {
  const labels: Record<string, string> = {
    state: "Trạng thái",
    priority: "Độ ưu tiên",
    title: "Tiêu đề",
    notes: "Ghi chú",
    note: "Nội dung",
    body: "Nội dung",
    due_date: "Ngày hết hạn",
    start_date: "Ngày bắt đầu",
    type: getTypeLabel(),
    users: "Người tham gia",
    start_time: "Thời gian bắt đầu",
    end_time: "Thời gian kết thúc",
    completed_at: "Ngày hoàn thành",
    doctor_id: "Bác sĩ phụ trách",
    reminder_status: "Nhắc hẹn",
    extra_notes: "CV dự kiến",
    created_at: "Lịch hẹn",
    current_serial: "Lặp lại",
    person_id: "Khách hàng",
    arrived_at: "Thời gian đến",
  };
  return labels[field] || field;
};

const getEventColor = (field: string) => {
  const colors: Record<string, string> = {
    state: "#4ade80", // green-400
    priority: "#fb923c", // orange-400
    title: "#60a5fa", // blue-400
    notes: "#94a3b8", // slate-400
    note: "#94a3b8", // slate-400
    due_date: "#f472b6", // pink-400
    start_date: "#c084fc", // purple-400
    type: "#22d3ee", // cyan-400
    person_id: "#a78bfa", // violet-400
    users: "#818cf8", // indigo-400
    default: "#9ca3af", // gray-400
    start_time: "#f59e0b", // yellow-400
    reminder_status: "#f472b6", // pink-400
    extra_notes: "#22d3ee", // cyan-400
    created_at: "#22c55e", // green-600
    current_serial: "#60a5fa", // blue-400
  };
  return colors[field] || colors.default;
};

const getEventIcon = (field: string) => {
  const icons: Record<string, string> = {
    state: "pi pi-check-circle",
    priority: "pi pi-flag",
    title: "pi pi-list",
    notes: "pi pi-clipboard",
    note: "pi pi-clipboard",
    due_date: "pi pi-calendar-times",
    start_date: "pi pi-calendar-plus",
    start_time: "pi pi-clock",
    end_time: "pi pi-clock",
    type: "pi pi-tag",
    person_id: "pi pi-user",
    users: "pi pi-users",
    default: "pi pi-sync",
    current_serial: "pi pi-refresh",
  };
  return icons[field] || icons.default;
};

// Transform logs for Timeline component
const timelineEvents = computed(() => {
  // Handle case when history is null or undefined
  if (!props.history || !Array.isArray(props.history)) {
    // Still add creation event if available
    const events = [];
    if (props.creatorId && props.createdAt) {
      events.push({
        timestamp: props.createdAt,
        user_id: props.creatorId,
        changes: [
          {
            field: "created_at",
            old_value: "",
            new_value: "",
          },
        ],
        color: "#22c55e", // green-600
        icon: "pi pi-plus-circle",
      });
    }
    return events;
  }

  let events = [...props.history].map((log) => ({
    ...log,
    color: getEventColor(log.changes[0]?.field),
    icon: getEventIcon(log.changes[0]?.field),
    changes: log.changes.filter((change) => isVisibleField(change.field)),
  }));

  // Thêm event tạo mới nếu có creatorId và createdAt
  if (props.creatorId && props.createdAt) {
    events.push({
      timestamp: props.createdAt,
      user_id: props.creatorId,
      changes: [
        {
          field: "created_at",
          old_value: "",
          new_value: "",
        },
      ],
      color: "#22c55e", // green-600
      icon: "pi pi-plus-circle",
    });
  }

  // Sort by timestamp if reverseOrder is enabled
  if (props.reverseOrder) {
    events.sort((a, b) => dayjs(b.timestamp).valueOf() - dayjs(a.timestamp).valueOf());
  }

  return events;
});

const getTypeLabel = () => {
  if (props.usedAtComponent === "task") {
    return "Loại công việc";
  }
  return "Loại hẹn";
};

const isShowOldValue = (field: string) => {
  const fieldsNotShowOldValue = [
    "current_serial",
    "note",
    "notes",
    "body",
    "arrived_at",
    "extra_notes",
  ];
  return !fieldsNotShowOldValue.includes(field);
};

const isVisibleField = (field: string) => {
  const hiddenFields = ["completed_at", "end_date"];
  return !hiddenFields.includes(field);
};
</script>

<template>
  <div class="w-full space-y-4">
    <div v-for="(item, index) in timelineEvents" :key="index" class="relative">
      <!-- Revision Item -->
      <div class="flex gap-3">
        <!-- Avatar & Connector -->
        <div class="flex flex-col items-center">
          <!-- User Avatar Only -->
          <slot name="avatar" :item="item" :index="index">
            <UserAvatar :user="getUserById(item.user_id)" class="h-8 w-8 flex-shrink-0" />
          </slot>

          <!-- Connector Line (aligned with avatar center) -->
          <div
            v-if="index < timelineEvents.length - 1"
            class="mt-2 min-h-6 w-0.5 flex-1 bg-gray-200"
          />
        </div>

        <!-- User Info & Content -->
        <div class="flex min-w-0 gap-2 pb-4">
          <!-- User Info Header -->
          <div class="mb-2 flex flex-col">
            <span class="text-sm font-medium text-gray-900">
              {{ getUserById(item.user_id)?.name || "Unknown User" }}
            </span>
            <span class="text-xs text-gray-500">
              {{ formatRelativeTime(item.timestamp) }}
            </span>
          </div>

          <!-- Changes -->
          <div class="space-y-2">
            <slot name="changes" :item="item" :index="index">
              <div v-for="(change, idx) in item.changes" :key="idx" class="text-sm">
                <slot
                  name="change-item"
                  :change="change"
                  :item="item"
                  :index="index"
                  :changeIndex="idx"
                >
                  <!-- Field Label -->
                  <div
                    v-if="change.field !== 'created_at'"
                    class="flex flex-wrap items-start gap-2"
                  >
                    <Tag
                      class="flex-shrink-0 text-xs !font-medium"
                      :value="getFieldLabel(change.field)"
                      severity="info"
                    >
                      <template #icon>
                        <i :class="getEventIcon(change.field) + ' text-xs'" />
                      </template>
                    </Tag>

                    <!-- Change Content -->
                    <div class="min-w-0 flex-1 text-xs">
                      <slot
                        name="change-content"
                        :change="change"
                        :item="item"
                        :index="index"
                        :changeIndex="idx"
                      >
                        <!-- State Change -->
                        <template v-if="change.field === 'state'">
                          <div class="flex items-center gap-2">
                            <StateSelectBtn
                              :state="change.old_value"
                              size="small"
                              :editable="false"
                              :show-dot="false"
                            />
                            <i class="pi pi-arrow-right text-xs text-gray-400" />
                            <StateSelectBtn
                              :state="change.new_value"
                              size="small"
                              :editable="false"
                              :show-dot="false"
                            />
                          </div>
                        </template>

                        <!-- Doctor Change -->
                        <template v-else-if="change.field === 'doctor_id'">
                          <div class="flex items-center gap-2">
                            <UserAvatar :user="getUserById(change.old_value)" class="h-6 w-6" />
                            <i class="pi pi-arrow-right text-xs text-gray-400" />
                            <UserAvatar :user="getUserById(change.new_value)" class="h-6 w-6" />
                          </div>
                        </template>

                        <!-- Priority Change -->
                        <template v-else-if="change.field === 'priority'">
                          <div class="flex items-center gap-2">
                            <PriorityChip :priority="change.old_value" size="small" />
                            <i class="pi pi-arrow-right text-xs text-gray-400" />
                            <PriorityChip :priority="change.new_value" size="small" />
                          </div>
                        </template>

                        <!-- Note/Content Changes -->
                        <template
                          v-else-if="
                            change.field === 'note' ||
                            change.field === 'body' ||
                            change.field === 'notes'
                          "
                        >
                          <div class="rounded-lg border bg-white p-3">
                            <div
                              class="prose prose-sm max-w-none break-words"
                              v-lightbox
                              v-html="formatValue(change.field, change.new_value)"
                            />
                          </div>
                        </template>

                        <!-- Type Change -->
                        <template v-else-if="change.field === 'type'">
                          <div class="flex items-center gap-2">
                            <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                              {{ formatValue(change.field, change.old_value) }}
                            </span>
                            <i class="pi pi-arrow-right text-xs text-gray-400" />
                            <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                              {{ formatValue(change.field, change.new_value) }}
                            </span>
                          </div>
                        </template>

                        <!-- Generic Change -->
                        <template v-else>
                          <div class="flex flex-wrap items-center gap-2">
                            <span
                              v-if="isShowOldValue(change.field)"
                              class="text-gray-500 line-through"
                            >
                              <span
                                v-if="change.field === 'extra_notes'"
                                v-html="formatValue(change.field, change.old_value)"
                              />
                              <span v-else>{{ formatValue(change.field, change.old_value) }}</span>
                            </span>
                            <i
                              v-if="isShowOldValue(change.field)"
                              class="pi pi-arrow-right text-xs text-gray-400"
                            />
                            <span class="font-medium text-gray-900">
                              <span
                                v-if="change.field === 'extra_notes'"
                                v-html="formatValue(change.field, change.new_value)"
                              />
                              <span v-else>{{ formatValue(change.field, change.new_value) }}</span>
                            </span>
                          </div>
                        </template>
                      </slot>
                    </div>
                  </div>

                  <!-- Creation Event -->
                  <div v-else class="flex items-center gap-2">
                    <Tag
                      class="flex-shrink-0 text-xs font-medium"
                      value="Đã tạo"
                      severity="success"
                    >
                      <template #icon>
                        <i class="pi pi-plus-circle text-xs" />
                      </template>
                    </Tag>
                  </div>
                </slot>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.prose) {
  overflow-wrap: break-word;
}
</style>
