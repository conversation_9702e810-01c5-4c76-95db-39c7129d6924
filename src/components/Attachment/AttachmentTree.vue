<script lang="ts" setup>
//AttachmentTree.vue
import { storeToRefs } from "pinia";
import Chip from "primevue/chip";
import { onMounted, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import type { AttachmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Counter from "@/base-components/Counter.vue";
import DateTime from "@/base-components/DateTime.vue";
import Dot from "@/base-components/Dot.vue";
import HugeiconsDentalTooth from "@/base-components/Icons/ToothIcon.vue";
import Money from "@/base-components/Money.vue";
import ProductPrice from "@/base-components/ProductPrice.vue";
import OperationSelect from "@/components/Operation/OperationSelect.vue";
import TeethSelect from "@/components/TeethSelect.vue";
import useAttachment from "@/hooks/useAttachment";
import useAttachmentMeta from "@/hooks/useAttachmentDataMeta";
import { useTreatmentFormStore } from "@/stores/treatment-form-store";
import { getOperationApiIdentifier } from "@/utils/operation-utils";

const store = useTreatmentFormStore();
const { isEditing } = storeToRefs(store);

const props = defineProps<{
  attachment: AttachmentResponse;
  parent: AttachmentResponse;
}>();

const emit = defineEmits(["removeAttachment", "operationsChanged"]);

const { listAttachments, updateAttachment, deleteAttachment } = useAttachment();
// Material quotas handled by V2 system
const childAttachments = ref<AttachmentResponse[]>([]);
const operationPopRef = ref();
const teethPopRef = ref();
const localOperations = ref<{ id: number | string; name: string }[]>([]);
const localTeeth = ref<Record<string, boolean>>({});

const { getMetaValues, syncMetaData, initializeAttachment, loadInitialMetaData } =
  useAttachmentMeta({
    operation: {},
    teeth: {},
  });

// Legacy material handling removed - V2 system handles this

// Lifecycle hook để khởi tạo dữ liệu ban đầu
onMounted(async () => {
  const response = await listAttachments({
    filter: {
      parent_id: props.parent.id,
      status: AttachmentStatus.ACTIVE,
    },
    page_size: 3,
    order_by: "created_at DESC",
  });
  childAttachments.value = response?.attachments || [];

  // Khởi tạo attachment và lấy metadata
  initializeAttachment(props.attachment.id);
  await loadInitialMetaData(props.attachment.id);
  const metaValues = getMetaValues(props.attachment.id);

  // Cập nhật local state từ metadata
  if (Object.keys(metaValues.operation).length > 0) {
    localOperations.value = Object.entries(metaValues.operation).map(([id, name]) => ({
      id: isNaN(Number(id)) ? id : Number(id), // Convert back to number if valid
      name: name as string,
    }));

    // ⚠️  ONLY load operation materials in CREATE mode
    // In EDIT mode, material usages are already loaded via TreatmentForm.loadExistingMaterialUsages()
    if (!isEditing.value) {
      // Load operation materials for existing operations ONLY in create mode
      const operationIds = localOperations.value
        .map((op) => {
          return typeof op.id === "string" ? parseInt(op.id) : op.id;
        })
        .filter((id) => !isNaN(id) && id > 0);

      // Material quotas handled by V2 system
    } else {
      console.log(
        "🔄 Edit mode detected - skipping operation/material/list call (data already loaded via material/usage/list)",
      );
    }

    // Emit initial operations
    emitOperationsChanged(localOperations.value);
  }

  if (Object.keys(metaValues.teeth).length > 0) {
    localTeeth.value = metaValues.teeth;
  }
});

const emitOperationsChanged = (operations: { id: string | number; name: string }[]) => {
  const operationsWithApiIdentifiers = operations.map((op) => ({
    ...op,
    ...getOperationApiIdentifier(op),
  }));

  emit("operationsChanged", {
    attachmentId: props.attachment.id,
    operations: operationsWithApiIdentifiers,
  });
};

// Handler để loại bỏ attachment
const handleRemoveAttachment = async () => {
  await deleteAttachment({ id: props.attachment.id });
  emit("removeAttachment", props.attachment.id);
};

// Handler để mở popover cho operations
const handleSelfClick = (event: MouseEvent) => {
  operationPopRef.value?.popoverRef.toggle(event);
};

// Handler để mở popover cho teeth
const handleTeethClick = (event: MouseEvent) => {
  teethPopRef.value?.popoverRef.toggle(event);
};

// Handler để loại bỏ một operation
const handleRemoveOperation = (operationId: number | string) => {
  // Simple operation removal - V2 system handles material cleanup
  localOperations.value = localOperations.value.filter((op) => op.id !== operationId);

  // Update metadata
  const metaValues = getMetaValues(props.attachment.id);
  delete metaValues.operation[operationId];
  syncMetaData(props.attachment.id, "operation");

  // Emit operations changed event for V2 system to handle
  emitOperationsChanged(localOperations.value);
};

// Handler để loại bỏ một tooth
const handleRemoveTooth = (toothId: string) => {
  // Update cho attachment hiện tại
  const newTeeth = { ...localTeeth.value };
  delete newTeeth[toothId];
  localTeeth.value = newTeeth;

  const metaValues = getMetaValues(props.attachment.id);
  metaValues.teeth = newTeeth;
  syncMetaData(props.attachment.id, "teeth");

  // Update cho parent attachment
  const parentMetaValues = getMetaValues(props.parent.id);
  parentMetaValues.teeth = newTeeth;
  syncMetaData(props.parent.id, "teeth");
};

// Handler để cập nhật operations từ OperationSelect
const updateOperations = (value: any) => {
  // The component expects an array of operations.
  // We only process the update if the emitted value is an array.
  if (Array.isArray(value)) {
    const newOperations = value as { id: number | string; name: string }[];
    localOperations.value = newOperations;
    const metaValues = getMetaValues(props.attachment.id);
    metaValues.operation = Object.fromEntries(newOperations.map((op) => [op.id, op.name]));
    syncMetaData(props.attachment.id, "operation");

    // Emit operations changed event for V2 system to handle
    emitOperationsChanged(newOperations);
  }
};

// Handler để cập nhật teeth từ TeethSelect
const updateTeeth = (value: Record<string, boolean>) => {
  console.log("updateTeeth", value);
  localTeeth.value = { ...value };

  const metaValues = getMetaValues(props.attachment.id);
  metaValues.teeth = value;
  syncMetaData(props.attachment.id, "teeth");

  //TODO update luôn cho parent, sau này có thể implement việc merge data
  const parentMetaValues = getMetaValues(props.parent.id);
  parentMetaValues.teeth = value;
  syncMetaData(props.parent.id, "teeth");
};

// Local state cho quantity
const localQuantity = ref(props.parent.quantity);

watch(localQuantity, async (newQty) => {
  if (newQty === 0) {
    // Xóa attachment và emit sự kiện removeAttachment
    await handleRemoveAttachment();
  } else {
    // ✅ FIX: Update quantity and wait for DB update BEFORE emitting
    await updateAttachment({
      id: props.parent.id,
      quantity: newQty,
    });
    
    const pendingItem = store.pendingAttachments[props.attachment.id];
    if (pendingItem) {
      store.pendingAttachments[props.attachment.id] = {
        ...pendingItem,
        parent: { ...pendingItem.parent, quantity: newQty },
      };
    }

    // ✅ FIX: Emit AFTER DB update to ensure fresh quantity extraction
    emitOperationsChanged(localOperations.value);
    console.log('🔄 Quantity updated in DB, emitted operationsChanged for material usage update');
  }
});
</script>

<template>
  <div>
    <!-- Header Attachment -->
    <div
      :class="{
        'border border-amber-200 bg-amber-50 text-warning':
          parent.status === AttachmentStatus.TEMP || parent.status === AttachmentStatus.UNPAID,
        'border border-green-200 bg-green-50 text-success': parent.status === CommonStatus.ACTIVE,
      }"
      class="relative z-20 flex items-center rounded p-1.5"
    >
      <div class="flex-grow cursor-pointer">
        <AttachmentTitle :attachment="props.parent" />
      </div>
      <div v-if="parent.product.price" class="flex items-center">
        <div class="w-24 text-right">
          <Money v-if="parent.price" :amount="parent.price" />
          <ProductPrice v-else :id="parent.product_id" />
        </div>
        <div class="ml-2">
          <Counter
            v-model="localQuantity"
            class="inline-block font-semibold"
            prefix="&times; "
            :readonly="isEditing"
            @click.stop
          />
        </div>
      </div>
      <button v-if="!isEditing" class="ml-2 text-red-500" @click="handleRemoveAttachment">
        <i class="pi pi-times text-xs" />
      </button>
    </div>

    <!-- Child Attachments và Operations/Teeth -->
    <div class="mt-1 border border-transparent">
      <!-- Hiển thị child attachments nếu có -->
      <template v-if="childAttachments.length > 0">
        <div v-if="childAttachments.length === 3" class="relative pl-4 text-muted">
          <span
            class="absolute -top-3 left-1 h-8 w-2 rounded-bl border-b border-l border-slate-300"
          ></span>
          <i class="pi pi-ellipsis-h text-xs" />
          <Dot class="px-1" />
          <DateTime :time="childAttachments[2].created_at" />
        </div>
        <div
          v-for="(child, index) in childAttachments.slice(0, 2)"
          :key="child.id"
          class="relative pl-4 text-muted"
        >
          <span
            class="absolute -top-3 left-1 h-8 w-2 rounded-bl border-b border-l border-slate-300"
          ></span>
          <AttachmentTitle :attachment="child" />
          <Dot class="px-1" />
          <DateTime :time="child.created_at" />
        </div>
      </template>

      <!-- Operations và Teeth -->
      <div class="relative pl-3">
        <span
          class="absolute -top-3 left-1 h-7 w-2 rounded-bl border-b border-l border-slate-300"
        ></span>
        <div class="flex gap-3">
          <!-- Operations -->
          <div class="flex flex-wrap items-center gap-1">
            <template v-if="localOperations.length">
              <Chip
                v-for="operation in localOperations"
                :key="operation.id"
                :label="operation.name"
                class="cursor-pointer"
                removable
                @click="handleSelfClick"
                @remove.stop="handleRemoveOperation(operation.id)"
              />
            </template>
            <span
              v-else
              class="my-1 ml-1 cursor-pointer underline decoration-dotted"
              @click="handleSelfClick"
            >
              <i class="pi pi-hammer text-xs text-muted" /> Nội dung điều trị
            </span>
          </div>
          <div class="border-r"></div>
          <!-- Teeth -->
          <div class="flex flex-wrap items-center gap-1">
            <template v-if="Object.keys(localTeeth).length">
              <Chip
                v-for="(selected, toothId) in localTeeth"
                v-show="selected"
                :key="toothId"
                class="cursor-pointer"
                removable
                @click="handleTeethClick"
                @remove.stop="handleRemoveTooth(toothId)"
              >
                <span class="flex">
                  <HugeiconsDentalTooth />
                </span>
                <span class="ml-1">
                  <template v-if="toothId === 'upper'">Hàm trên</template>
                  <template v-else-if="toothId === 'lower'">Hàm dưới</template>
                  <template v-else>{{ toothId }}</template>
                </span>
              </Chip>
            </template>
            <span
              v-else
              class="cursor-pointer underline decoration-dotted"
              @click="handleTeethClick"
            >
              <HugeiconsDentalTooth /> <span>Răng</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Popover cho Operations -->
    <OperationSelect
      ref="operationPopRef"
      :modelValue="localOperations"
      :product-ids="[parent.product_id]"
      @update:modelValue="updateOperations"
      has-default-opts
    />

    <!-- Popover cho Teeth -->
    <TeethSelect ref="teethPopRef" :modelValue="localTeeth" @update:modelValue="updateTeeth" />
  </div>
</template>
