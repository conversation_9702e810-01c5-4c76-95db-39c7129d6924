<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import type { AttachmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import HugeiconsDentalTooth from "@/base-components/Icons/ToothIcon.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";

interface Props {
  parent: AttachmentResponse;
  operations: Record<string | number, string>;
  showOperations?: boolean;
  teethData?: Record<string, boolean>;
  hideDefaultOperations?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  hideDefaultOperations: false,
});
const localExpanded = ref(props.showOperations ?? true);
const showDefaultOperations = ref(false);

// Get default operation IDs from settings (same as OperationSelect.vue)
const settingName = "operation_select_default_opts";
const { currentSettingValue } = useComponentSetting(settingName);
const defaultIds = computed(() => currentSettingValue.value?.operation_ids ?? []);

// Update localExpanded when showOperations changes
watch(
  () => props.showOperations,
  (newValue) => {
    if (newValue !== undefined) {
      localExpanded.value = newValue;
    }
  },
);

const isExpanded = computed({
  get: () => localExpanded.value,
  set: (value) => {
    localExpanded.value = value;
  },
});

const teethList = computed(() => {
  if (!props.teethData) return [];
  return Object.entries(props.teethData)
    .filter(([_, value]) => value)
    .map(([index]) => index);
});

// Split operations into default and non-default (optimized single pass)
const operationsSplit = computed(() => {
  const defaultOperationIds = new Set(defaultIds.value || []);
  const defaults: Record<string | number, string> = {};
  const nonDefaults: Record<string | number, string> = {};

  Object.entries(props.operations).forEach(([id, name]) => {
    const operationId = Number(id);
    if (defaultOperationIds.has(operationId)) {
      defaults[id] = name;
    } else {
      nonDefaults[id] = name;
    }
  });

  return { defaults, nonDefaults };
});

const defaultOperations = computed(() => operationsSplit.value.defaults);
const nonDefaultOperations = computed(() => operationsSplit.value.nonDefaults);

// Operations to display based on current state
const displayedOperations = computed(() => {
  if (!props.hideDefaultOperations || showDefaultOperations.value) {
    return props.operations;
  }
  return nonDefaultOperations.value;
});

// Check if there are hidden default operations
const hasHiddenDefaults = computed(() => {
  return (
    props.hideDefaultOperations &&
    Object.keys(defaultOperations.value).length > 0 &&
    !showDefaultOperations.value
  );
});

// Check if should show toggle button for default operations
const shouldShowToggleButton = computed(() => {
  return (
    props.hideDefaultOperations &&
    Object.keys(defaultOperations.value).length > 0
  );
});

// Get the count of non-default operations for toggle button positioning
const nonDefaultCount = computed(() => Object.keys(nonDefaultOperations.value).length);
</script>

<template>
  <div>
    <div class="flex items-center">
      <div
        class="flex-grow rounded border p-1.5"
        :class="{
          'border-gray-200 bg-gray-50 text-gray-700': parent.status === CommonStatus.TEMP,
          'border-amber-200 bg-amber-50 text-warning': parent.status === AttachmentStatus.UNPAID,
          'border-green-200 bg-green-50 text-success': parent.status === CommonStatus.ACTIVE,
        }"
      >
        <AttachmentTitle :attachment="props.parent" />
      </div>

      <div
        v-if="(displayedOperations && Object.keys(displayedOperations).length) || hasHiddenDefaults"
        class="ml-1 flex items-center"
      >
        <i
          class="pi ml-1 cursor-pointer rounded border p-1 text-xs text-slate-400 hover:shadow"
          :class="isExpanded ? 'pi-chevron-up' : 'pi-chevron-down'"
          @click="isExpanded = !isExpanded"
        />
      </div>

      <span
        v-if="teethList.length"
        class="ml-1 inline-flex items-center gap-0.5 text-sm text-orange-500"
      >
        <HugeiconsDentalTooth class="h-4 w-4" /> {{ teethList.join(", ") }}
      </span>
    </div>

    <div
      v-if="
        isExpanded &&
        ((displayedOperations && Object.keys(displayedOperations).length) || hasHiddenDefaults)
      "
      class="mt-1 border border-transparent"
    >
      <div class="relative pl-4 text-muted">
        <span
          class="absolute -top-1 left-1 h-4 w-2 rounded-bl border-b border-l border-slate-300"
        ></span>

        <!-- Non-default operations -->
        <div v-for="(operation, id, index) in nonDefaultOperations" :key="id" class="relative mb-1">
          <div class="flex items-center gap-1">
            <span>{{ operation }}</span>

            <!-- Reddit-style toggle for default operations (show after last non-default operation) -->
            <button
              v-if="shouldShowToggleButton && index === nonDefaultCount - 1"
              @click="showDefaultOperations = !showDefaultOperations"
              class="flex size-4 items-center justify-center rounded-full border border-slate-300 bg-white text-xs text-slate-500 transition-colors hover:border-slate-400 hover:bg-slate-50 hover:font-bold"
              :title="showDefaultOperations ? 'Hide default operations' : 'Show default operations'"
            >
              <i
                :class="showDefaultOperations ? 'pi pi-minus' : 'pi pi-plus'"
                class="text-[8px]"
              ></i>
            </button>
          </div>
        </div>

        <!-- Default operations (shown when expanded or hideDefaultOperations is false) -->
        <div v-if="!props.hideDefaultOperations || showDefaultOperations">
          <div v-for="(operation, id) in defaultOperations" :key="id" class="mb-1">
            {{ operation }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
