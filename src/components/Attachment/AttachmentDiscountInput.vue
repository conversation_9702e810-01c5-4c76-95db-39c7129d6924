<script setup lang="ts">
import { computed, ref } from "vue";

import type { AttachmentResponse } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";

interface Props {
  attachment: AttachmentResponse;
  discount?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:discount", value: number): void;
}>();

const isEditing = ref(false);

const discountValue = computed({
  get: () => props.discount || 0,
  set: (value: number) => {
    emit("update:discount", value);
    isEditing.value = false;
  },
});

const toggleEdit = (event: MouseEvent) => {
  event.stopPropagation();
  isEditing.value = true;
};

// Calculate maximum discount as price * quantity
const maxDiscount = computed(() => {
  const price = props.attachment.price || props.attachment.product?.price || 0;
  const quantity = props.attachment.quantity || 1;
  return price * quantity;
});
</script>

<template>
  <div class="flex items-center" @click.stop>
    <template v-if="isEditing">
      <InputNumber
        v-model="discountValue"
        :min="0"
        :max="maxDiscount"
        mode="currency"
        currency="VND"
        :showButtons="false"
        placeholder="Giảm giá"
        pt:pcInputText:root:class="text-sm w-full text-info font-semibold rounded-none border-0 border-b p-0 shadow-none focus:ring-0 text-center"
        @click.stop
        fluid
        @keydown.enter.prevent
        @keyup.enter.prevent
      />
    </template>
    <template v-else>
      <span @click.stop="toggleEdit" class="cursor-pointer">
        <Money :amount="-discountValue" variant="info" class="font-medium" />
      </span>
    </template>
  </div>
</template>
