<template>
  <div class="space-y-3">
    <!-- Existing Tags -->
    <div class="flex flex-wrap gap-2">
      <TagChip
        v-for="tag in localTags"
        :key="`tag-${tag.id}`"
        :tag="tag"
        :size="size"
        closable
        :modifiable="false"
        @close="handleRemoveTag"
      />
      <ModernTagSelector
        v-model="selectedTags"
        :available-tags="availableTagsFiltered"
        :entity-type="entityType"
        :size="size"
        :is-loading="isLoading"
        @tag-selected="handleAddTag"
        @tag-removed="handleRemoveTag"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import TagChip from "./TagChip.vue";
import ModernTagSelector from "./ModernTagSelector.vue";
import { TagInfo, TagShort, TagResponse } from "@/api/bcare-types-v2";
import useTag from "@/hooks/useTag";

interface Props {
  entityType: "person" | "customer" | "deal" | "task" | "company";
  entityId: number;
  initialTags?: TagInfo[] | null; // Array ban đầu từ parent - có thể null/undefined
  size?: "xs" | "sm" | "base";
}

const props = withDefaults(defineProps<Props>(), {
  initialTags: () => [],
  size: "xs",
});

// No emits - component tự quản lý optimistic updates
const emit = defineEmits<{
  tagsUpdated: [tags: TagInfo[]]; // Emit khi có thay đổi thành công
}>();

// Use tag hook for API operations
const {
  addTagToPerson,
  addTagToDeal,
  deleteTagFromPerson,
  deleteTagFromDeal,
  isLoading,
  tags: allTags,
} = useTag({ useStore: true, autoLoad: true });

// Local state for optimistic updates
const localTags = ref<TagInfo[]>([]);
const originalTags = ref<TagInfo[]>([]);

// Watch initial tags and sync to local state
watch(
  () => props.initialTags,
  (newTags) => {
    // Handle null, undefined, or empty arrays
    const safeTags = Array.isArray(newTags) ? newTags : [];
    localTags.value = [...safeTags];
    originalTags.value = [...safeTags];
  },
  { immediate: true, deep: true },
);

// Track selected tags for selector
const selectedTags = computed(() => {
  return localTags.value;
});

// Show all available tags (don't filter out selected ones)
const availableTagsFiltered = computed(() => {
  return allTags.value;
});

// Convert TagResponse/TagShort to TagInfo with validation
const convertToTagInfo = (tag: TagShort | TagResponse): TagInfo => {
  if (!tag || typeof tag.id !== "number" || !tag.name || !tag.category) {
    throw new Error("Invalid tag data: missing required fields (id, name, category)");
  }

  return {
    id: tag.id,
    name: tag.name.trim(),
    category: tag.category.trim(),
  };
};

// Handle adding tag with optimistic update
const handleAddTag = async (tag: TagShort | TagResponse) => {
  // Validate inputs
  if (!tag || !tag.id || !props.entityId) {
    console.error("❌ Invalid tag or entity ID:", { tag, entityId: props.entityId });
    return;
  }

  // Check if tag already exists
  if (localTags.value.some((t) => t.id === tag.id)) {
    console.log("ℹ️ Tag already exists, skipping add:", tag.name);
    return;
  }

  let tagInfo: TagInfo;
  try {
    tagInfo = convertToTagInfo(tag);
  } catch (error) {
    console.error("❌ Failed to convert tag:", error);
    return;
  }

  console.log(`🏷️ Optimistic Add: Adding tag to ${props.entityType}:`, {
    entityType: props.entityType,
    entityId: props.entityId,
    tagId: tag.id,
    tagName: tag.name,
    tagCategory: tag.category,
  });

  // Optimistic update - add immediately to UI
  localTags.value = [...localTags.value, tagInfo];

  try {
    // Make actual API call
    if (props.entityType === "person") {
      await addTagToPerson({ person_id: props.entityId, tag_id: tag.id });
    } else if (props.entityType === "deal") {
      await addTagToDeal({ deal_id: props.entityId, tag_id: tag.id });
    } else {
      // For other entity types, just log for now
      console.log(`🚧 API not implemented for ${props.entityType}`);
    }

    // Success - update original tags and emit
    originalTags.value = [...localTags.value];
    emit("tagsUpdated", localTags.value);

    console.log(`✅ Tag added successfully to ${props.entityType}`);
  } catch (error) {
    console.error(`❌ Failed to add tag to ${props.entityType}:`, error);

    // Revert optimistic update on error
    localTags.value = [...originalTags.value];

    // You could show a toast notification here
    console.log(`🔄 Reverted optimistic update for ${props.entityType}`);
  }
};

// Handle removing tag with optimistic update
const handleRemoveTag = async (tagId: number) => {
  // Validate inputs
  if (!tagId || !props.entityId) {
    console.error("❌ Invalid tag ID or entity ID:", { tagId, entityId: props.entityId });
    return;
  }

  const tagToRemove = localTags.value.find((t) => t.id === tagId);

  // Check if tag exists
  if (!tagToRemove) {
    console.log("ℹ️ Tag not found in local tags, skipping remove:", tagId);
    return;
  }

  console.log(`🗑️ Optimistic Remove: Removing tag from ${props.entityType}:`, {
    entityType: props.entityType,
    entityId: props.entityId,
    tagId: tagId,
    tagName: tagToRemove?.name,
  });

  // Optimistic update - remove immediately from UI
  localTags.value = localTags.value.filter((t) => t.id !== tagId);

  try {
    // Make actual API call
    if (props.entityType === "person") {
      await deleteTagFromPerson({ person_id: props.entityId, tag_id: tagId });
    } else if (props.entityType === "deal") {
      await deleteTagFromDeal({ deal_id: props.entityId, tag_id: tagId });
    } else {
      // For other entity types, just log for now
      console.log(`🚧 API not implemented for ${props.entityType}`);
    }

    // Success - update original tags and emit
    originalTags.value = [...localTags.value];
    emit("tagsUpdated", localTags.value);

    console.log(`✅ Tag removed successfully from ${props.entityType}`);
  } catch (error) {
    console.error(`❌ Failed to remove tag from ${props.entityType}:`, error);

    // Revert optimistic update on error
    localTags.value = [...originalTags.value];

    // You could show a toast notification here
    console.log(`🔄 Reverted optimistic update for ${props.entityType}`);
  }
};
</script>
