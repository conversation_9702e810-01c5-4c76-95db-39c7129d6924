<template>
  <span
    :class="tagClasses"
    :style="{ maxWidth: props.maxWidth }"
    @click="handleClick"
    @mousedown.stop="handleMouseDownOutside"
    v-tooltip="!isEditing ? (props.tag as TagResponse).description : null"
  >
    <slot name="icon" />

    <!-- Display mode -->
    <span
      v-if="!isEditing"
      class="overflow-hidden transition-all duration-200"
      :class="getTextHoverClasses()"
    >
      {{ props.tag.name }}
    </span>

    <!-- Edit mode - Use v-show for potentially smoother transitions -->
    <input
      v-show="isEditing"
      ref="inputRef"
      v-model="editedName"
      type="text"
      class="m-0 box-border whitespace-nowrap border-none bg-transparent !p-0 outline-none focus:outline-none focus:ring-0"
      :class="sizeClasses"
      :style="{
        width: inputWidth,
        lineHeight: 'inherit',
        height: 'auto',
        fontSize: 'inherit',
        fontWeight: 'inherit',
        fontFamily: 'inherit',
      }"
      @input="adjustInputWidth"
      @keydown="handleKeydown"
      @blur="handleBlur"
      @mousedown.stop
    />

    <!-- Edit button for modifiable tags -->
    <button
      v-if="props.modifiable && !isEditing"
      @click.stop="startEditing"
      :class="editButtonClasses"
      class="absolute top-1/2 flex-shrink-0 -translate-y-1/2 rounded-full bg-black/20 opacity-0 hover:bg-black/30 focus:opacity-100 focus:outline-none focus:ring-1 focus:ring-offset-1 group-hover:translate-x-0 group-hover:opacity-100"
    >
      <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
        <path
          fill-rule="evenodd"
          d="M3 10a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM8.5 10a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM15.5 8.5a1.5 1.5 0 100 3 1.5 1.5 0 000-3z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <!-- Close button for closable tags - hidden during edit mode -->
    <button
      v-if="props.closable && !isEditing"
      @click.stop="handleDelete"
      :class="closeButtonClasses"
      class="absolute top-1/2 flex-shrink-0 -translate-y-1/2 rounded-full bg-black/20 opacity-0 hover:bg-black/30 focus:opacity-100 focus:outline-none focus:ring-1 focus:ring-offset-1 group-hover:translate-x-0 group-hover:opacity-100"
    >
      <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
        <path
          fill-rule="evenodd"
          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <!-- Hidden span to measure text width - ensure identical styling -->
    <span
      ref="hiddenSpanRef"
      class="invisible absolute whitespace-nowrap"
      :class="sizeClasses"
      :style="{
        fontSize: 'inherit',
        fontWeight: 'inherit',
        fontFamily: 'inherit',
        lineHeight: 'inherit',
      }"
      aria-hidden="true"
    >
      {{ editedName || props.tag.name }}
    </span>
  </span>
</template>

<script setup lang="ts">
import { TagInfo, TagResponse, TagShort } from "@/api/bcare-types-v2";
import { computed, ref, nextTick, watch } from "vue";

interface Props {
  tag: TagResponse | TagInfo | TagShort;
  size?: "xs" | "sm" | "base";
  closable?: boolean;
  selected?: boolean;
  modifiable?: boolean;
  maxWidth?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: "sm",
  closable: false,
  selected: false,
  modifiable: true,
  maxWidth: undefined,
});

const emit = defineEmits<{
  close: [id: number];
  update: [updatedTag: TagResponse | TagInfo];
  click: [tag: TagResponse | TagInfo | TagShort];
}>();

// Local state for inline editing
const isEditing = ref(false);
const editedName = ref("");
const inputRef = ref<HTMLInputElement | null>(null);
const hiddenSpanRef = ref<HTMLSpanElement | null>(null);
const inputWidth = ref("60px");

// Use centralized color system from useHashColor
import { getCategoryColorClasses } from "@/composables/useHashColor";

// Modern size styling
const sizeClasses = computed(() => {
  const sizes = {
    xs: "px-2 py-0.5 text-xs rounded-full",
    sm: "px-2.5 py-1 text-sm rounded-full",
    base: "px-3 py-1.5 text-base rounded-full",
  };
  return sizes[props.size];
});

const editButtonClasses = computed(() => {
  const sizes = {
    xs: "h-4 w-4 p-0.5",
    sm: "h-4 w-4 p-0.5",
    base: "h-5 w-5 p-1",
  };

  // Position based on whether close button is also present and visible
  // Close button is hidden during edit mode, so edit button can be at the edge
  const position = (props.closable && !isEditing.value) ? {
    xs: "right-7",
    sm: "right-7",
    base: "right-8",
  } : {
    xs: "right-1.5",
    sm: "right-1.5",
    base: "right-2",
  };

  return `${sizes[props.size]} ${position[props.size]}`;
});

const closeButtonClasses = computed(() => {
  const sizes = {
    xs: "right-1.5 h-4 w-4 p-0.5",
    sm: "right-1.5 h-4 w-4 p-0.5",
    base: "right-2 h-5 w-5 p-1",
  };
  return sizes[props.size];
});

// Color classes based on category
const colorClasses = computed(() => {
  return getCategoryColorClasses(props.tag.category || "default");
});

// Watch for changes to editedName and adjust input width
watch(
  editedName,
  () => {
    if (isEditing.value) {
      nextTick(() => {
        adjustInputWidth();
      });
    }
  },
  { immediate: true },
);

// Watch for editing state changes
watch(
  isEditing,
  (editing) => {
    if (editing) {
      nextTick(() => {
        adjustInputWidth();
      });
    }
  },
  { immediate: true },
);

// Function to get text hover classes based on available buttons
const getTextHoverClasses = () => {
  // Only consider visible buttons (close button is hidden during edit)
  const hasEditButton = props.modifiable && !isEditing.value;
  const hasCloseButton = props.closable && !isEditing.value;

  if (hasEditButton && hasCloseButton) {
    // Both buttons present - need more space
    return 'group-hover:max-w-[calc(100%-3rem)] group-hover:truncate';
  } else if (hasEditButton || hasCloseButton) {
    // One button present
    return 'group-hover:max-w-[calc(100%-1.5rem)] group-hover:truncate';
  }
  // No buttons or in edit mode
  return '';
};

// Function to adjust input width based on content
const adjustInputWidth = () => {
  if (hiddenSpanRef.value && isEditing.value) {
    // Update hidden span content to match current input
    hiddenSpanRef.value.textContent = editedName.value || props.tag.name;

    // Force a reflow to get accurate measurements
    hiddenSpanRef.value.offsetWidth;

    const extraPadding = 16; // More padding for better UX
    const contentWidth = hiddenSpanRef.value.offsetWidth + extraPadding;
    const minWidth = 80; // Slightly larger minimum width
    const maxWidth = 300; // Prevent input from becoming too wide

    const finalWidth = Math.min(Math.max(contentWidth, minWidth), maxWidth);
    inputWidth.value = `${finalWidth}px`;
  } else {
    // Fallback calculation
    const charWidth = props.size === 'xs' ? 7 : props.size === 'sm' ? 8 : 9;
    const calculatedWidth = Math.max(editedName.value.length * charWidth + 20, 80);
    inputWidth.value = `${Math.min(calculatedWidth, 300)}px`;
  }
};

const handleDelete = (event: Event) => {
  event.stopPropagation();
  emit("close", props.tag.id);
};

const handleClick = () => {
  // Only emit click event, no longer directly start editing
  emit("click", props.tag);
};

// Computed classes for the tag
const tagClasses = computed(() => {
  const hasButtons = props.closable || props.modifiable;
  const baseClasses = [
    sizeClasses.value,
    colorClasses.value.bg,
    !hasButtons ? "hover:translate-y-[-2px]" : "",
    props.selected ? `border ${colorClasses.value.border}` : "border border-transparent",
    "group relative inline-flex items-center font-medium transition-all duration-200 hover:shadow-sm"
  ];
  return baseClasses.filter(Boolean).join(" ");
});

const startEditing = () => {
  editedName.value = props.tag.name;
  isEditing.value = true;
  nextTick(() => {
    adjustInputWidth();
    if (inputRef.value) {
      inputRef.value.focus();
      inputRef.value.select();
    }
  });
};

const saveEdit = () => {
  const trimmedName = editedName.value.trim();
  if (trimmedName && trimmedName !== props.tag.name) {
    const updatedTag = { ...props.tag, name: trimmedName };
    emit("update", updatedTag);
  }
  isEditing.value = false;
};

const cancelEdit = () => {
  isEditing.value = false;
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    event.preventDefault();
    saveEdit();
  } else if (event.key === "Escape") {
    cancelEdit();
  }
};

let isBlurring = false;
const handleMouseDownOutside = () => {
  isBlurring = true;
};

const handleBlur = () => {
  if (isBlurring && isEditing.value) {
    saveEdit();
  }
  isBlurring = false;
};
</script>
