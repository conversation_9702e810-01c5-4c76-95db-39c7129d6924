<template>
  <div class="relative">
    <!-- Trigger Button -->
    <button
      @click="popoverRef?.toggle($event)"
      :class="triggerClasses"
      class="border border-dashed border-gray-300 bg-transparent font-medium transition-all duration-200 hover:border-gray-400 hover:text-gray-700"
    >
      + Tag
    </button>

    <!-- Popover -->
    <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @hide="onHide">
      <div class="w-[24rem]">
        <!-- Main content area with fixed height -->
        <div class="max-h-[40rem] overflow-hidden">
          <div
            v-if="filteredGroupedTags && Object.keys(filteredGroupedTags).length > 0"
            class="h-full p-3"
          >
            <!-- Tag Groups with Snap Scroll -->
            <div class="h-full overflow-y-auto scroll-smooth">
              <ul class="m-0 list-none p-0">
                <li
                  v-for="([category, tags], index) in Object.entries(filteredGroupedTags)"
                  :key="category"
                  class="relative last:-mb-2"
                >
                  <!-- Category Header (Sticky) -->
                  <div
                    class="sticky top-0 z-10 flex cursor-pointer items-center justify-between rounded-t-md bg-white/95 py-2 text-primary-500 shadow-sm backdrop-blur-sm hover:bg-gray-100"
                    @click="toggleAllTagsInCategory(category)"
                  >
                    <span class="font-medium capitalize">
                      {{ `${category}` }}
                      <span class="ml-2 text-sm text-gray-500">
                        (<span
                          :class="{
                            'font-medium text-blue-500': getSelectedCountInCategory(category) > 0,
                          }"
                        >
                          {{ getSelectedCountInCategory(category) }} </span
                        >/{{ tags.length }})
                      </span>
                    </span>
                    <i :class="getCategoryIconClass(category)" />
                  </div>

                  <!-- Tag List -->
                  <div class="snap-y snap-mandatory">
                    <div class="flex flex-wrap gap-2 py-3">
                      <TagChip
                        v-for="tag in tags"
                        :key="tag.id"
                        :tag="tag"
                        size="xs"
                        :selected="isTagSelected(tag.id)"
                        :modifiable="false"
                        class="cursor-pointer"
                        @click="toggleTag(tag)"
                      />
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- Loading State -->
          <div v-else-if="isLoading" class="flex h-full items-center justify-center p-2">
            <i class="pi pi-spin pi-spinner text-2xl text-gray-400"></i>
          </div>

          <!-- Empty State -->
          <div v-else class="flex h-full items-center justify-center p-2">
            <Empty />
          </div>
        </div>

        <!-- Footer with Search -->
        <div class="border-t p-3">
          <div class="flex items-center gap-2">
            <IconField class="flex-grow">
              <InputText
                v-model="searchQuery"
                autofocus
                class="w-full text-sm"
                placeholder="Tìm kiếm tag..."
                type="text"
              />
              <InputIcon
                :class="searchQuery ? 'pi-times' : 'pi-search'"
                class="pi cursor-pointer"
                @click="clearSearch"
              />
            </IconField>
            <Button class="text-sm" label="Close" @click="popoverRef?.hide()" />
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import TagChip from "./TagChip.vue";
import { TagShort, TagResponse } from "@/api/bcare-types-v2";
import Empty from "@/base-components/Empty/Empty.vue";

interface Props {
  availableTags: (TagShort | TagResponse)[];
  entityType: string;
  size?: "xs" | "sm" | "base";
  isLoading?: boolean;
  modelValue?: (TagShort | TagResponse)[]; // For tracking selected tags
}

const props = withDefaults(defineProps<Props>(), {
  size: "sm",
  isLoading: false,
  modelValue: () => [],
});

const emit = defineEmits<{
  tagSelected: [tag: TagShort | TagResponse];
  tagRemoved: [tagId: number];
  "update:modelValue": [tags: (TagShort | TagResponse)[]];
}>();

const popoverRef = ref();
const searchQuery = ref("");

// Track selected tags internally
const selectedTags = computed(() => props.modelValue || []);

// Dynamic trigger button classes based on size
const triggerClasses = computed(() => {
  const sizeClasses = {
    xs: "px-2 py-0.5 text-xs rounded-full",
    sm: "px-2.5 py-1 text-sm rounded-full",
    base: "px-3 py-1.5 text-base rounded-full",
  };
  return `${sizeClasses[props.size]} text-gray-600`;
});

// Group tags by category
const groupedTags = computed(() => {
  const groups: Record<string, (TagShort | TagResponse)[]> = {};
  props.availableTags.forEach((tag) => {
    if (!groups[tag.category]) {
      groups[tag.category] = [];
    }
    groups[tag.category].push(tag);
  });
  return groups;
});

// Filter tags based on search query
const filteredGroupedTags = computed(() => {
  if (!searchQuery.value) return groupedTags.value;

  const query = searchQuery.value.toLowerCase();
  const filtered: Record<string, (TagShort | TagResponse)[]> = {};

  Object.entries(groupedTags.value).forEach(([category, tags]) => {
    const matchingTags = tags.filter(
      (tag) => tag.name.toLowerCase().includes(query) || tag.category.toLowerCase().includes(query),
    );
    if (matchingTags.length > 0) {
      filtered[category] = matchingTags;
    }
  });

  return filtered;
});

// Check if tag is selected
const isTagSelected = (tagId: number) => {
  return selectedTags.value.some((tag) => tag.id === tagId);
};

// Get selected count in category
const getSelectedCountInCategory = (category: string) => {
  const categoryTags = filteredGroupedTags.value[category] || [];
  return categoryTags.filter((tag) => isTagSelected(tag.id)).length;
};

// Get category icon class
const getCategoryIconClass = (category: string) => {
  const categoryTags = filteredGroupedTags.value[category] || [];
  const selectedCount = getSelectedCountInCategory(category);

  return {
    "pi-check-square": selectedCount === categoryTags.length && categoryTags.length > 0,
    "pi-minus-circle": selectedCount > 0 && selectedCount < categoryTags.length,
    "pi-square": selectedCount === 0,
    pi: true,
    "text-blue-500": true,
    "ml-auto": true,
  };
};

// Toggle single tag
const toggleTag = (tag: TagShort | TagResponse) => {
  console.log("🏷️ Tag toggled:", tag);

  if (isTagSelected(tag.id)) {
    // Remove tag
    const newTags = selectedTags.value.filter((t) => t.id !== tag.id);
    emit("update:modelValue", newTags);
    emit("tagRemoved", tag.id);
  } else {
    // Add tag
    const newTags = [...selectedTags.value, tag];
    emit("update:modelValue", newTags);
    emit("tagSelected", tag);
  }
};

// Toggle all tags in category
const toggleAllTagsInCategory = (category: string) => {
  const categoryTags = filteredGroupedTags.value[category] || [];
  const selectedCount = getSelectedCountInCategory(category);

  if (selectedCount === categoryTags.length) {
    // Remove all tags in this category
    const newTags = selectedTags.value.filter((tag) => tag.category !== category);
    emit("update:modelValue", newTags);
    categoryTags.forEach((tag) => emit("tagRemoved", tag.id));
  } else {
    // Add all unselected tags in this category
    const tagsToAdd = categoryTags.filter((tag) => !isTagSelected(tag.id));
    const newTags = [...selectedTags.value, ...tagsToAdd];
    emit("update:modelValue", newTags);
    tagsToAdd.forEach((tag) => emit("tagSelected", tag));
  }
};

const clearSearch = () => {
  searchQuery.value = "";
};

const onHide = () => {
  clearSearch();
};

defineExpose({
  popoverRef,
});
</script>
