<template>
  <MultiSelect
    :modelValue="selectedTagIds"
    @update:modelValue="emitSelection"
    :options="groupedTags"
    optionLabel="name"
    optionValue="id"
    optionGroupLabel="category"
    optionGroupChildren="items"
    :placeholder="placeholder"
    display="chip"
    filter
    fluid
    :maxSelectedLabels="1"
    :pt="{
      label: { class: 'flex items-center h-full' },
      pcChip: { class: 'block overflow-hidden whitespace-nowrap text-ellipsis max-w-[30px]' },
    }"
  >
    <!-- Group Header Template -->
    <template #optiongroup="slotProps">
      {{ slotProps.option.category }}
    </template>

    <!-- Option Template (Individual Tag) -->
    <template #option="slotProps">
      <TagChip :tag="slotProps.option" size="xs" :modifiable="false" :closable="false" />
    </template>

    <!-- Chip Template (Selected Tag) -->
    <template #chip="slotProps">
      <TagChip
        :tag="getTagById(slotProps.value)!"
        size="xs"
        :modifiable="false"
        :closable="false"
        v-if="getTagById(slotProps.value)"
      />
    </template>
  </MultiSelect>
</template>

<script setup lang="ts">
import { computed } from "vue";
import useTag from "@/hooks/useTag";
import TagChip from "./TagChip.vue";

const props = defineProps<{
  modelValue: number[] | undefined | null;
  placeholder?: string;
}>();

const emit = defineEmits(["update:modelValue"]);

const { getTagById, groupedTags } = useTag({
  useStore: true,
  autoLoad: true,
});

const selectedTagIds = computed(() => props.modelValue || []);

const emitSelection = (selectedIds: number[]) => {
  emit("update:modelValue", selectedIds.length > 0 ? selectedIds : null);
};
</script>
