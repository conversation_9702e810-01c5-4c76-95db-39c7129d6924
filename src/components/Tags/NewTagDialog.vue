<template>
  <Dialog
    :style="{ width: '450px' }"
    v-model:visible="isVisible"
    modal
    header="Tạo tag mới"
    @hide="handleClose"
    :draggable="false"
  >
    <form @submit.prevent="handleSubmit" class="space-y-2">
      <!-- Tag Name -->
      <FormField label="Tên tag">
        <InputText
          id="tagName"
          v-model="formData.name"
          placeholder="Nhập tên tag..."
          :class="{ 'p-invalid': errors.name }"
          fluid
        />
      </FormField>

      <!-- Category -->
      <FormField label="Category">
        <Select
          id="tagCategory"
          v-model="formData.category"
          :options="filteredCategories"
          placeholder="Chọn hoặc nhập danh mục..."
          @focus="showAllCategories"
          @blur="validateCategory"
          :editable="true"
          fluid
        />
      </FormField>

      <!-- Description -->
      <FormField label="Mô tả">
        <Textarea
          id="tagDescription"
          v-model="formData.description"
          placeholder="Nhập mô tả cho tag (tù<PERSON> chọn)..."
          rows="3"
          fluid
        />
      </FormField>

      <!-- Preview -->
      <FormField v-if="formData.name" label="Xem trước">
        <div class="rounded-lg border bg-gray-50 p-3">
          <TagChip
            :tag="{
              id: 0,
              name: formData.name,
              category: formData.category || 'default',
              description: formData.description,
            }"
            :modifiable="false"
          />
        </div>
      </FormField>
    </form>

    <template #footer>
      <div class="flex justify-end gap-3">
        <Button
          label="Hủy"
          severity="secondary"
          outlined
          @click="handleClose"
          :disabled="isLoading"
        />
        <Button
          label="Tạo tag"
          :loading="isLoading"
          @click="handleSubmit"
          :disabled="!isFormValid"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

import TagChip from "./TagChip.vue";
import FormField from "@/components/Form/FormField.vue";
import useTag from "@/hooks/useTag";
import type { TagAddRequest } from "@/api/bcare-types-v2";

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  "tag-created": [tag: any];
}>();

// Use tag composable
const { addTag, isLoading, error, loadTags, getCategories } = useTag({ useStore: true });

// Form data
const formData = ref<TagAddRequest>({
  name: "",
  category: "",
  description: "",
});

// Validation errors
const errors = ref({
  name: "",
  category: "",
});

// AutoComplete state
const filteredCategories = ref<string[]>([]);

// Computed
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const isFormValid = computed(() => {
  return (
    formData.value.name.trim() &&
    formData.value.category.trim() &&
    !errors.value.name &&
    !errors.value.category
  );
});

// Methods
const validateName = () => {
  if (!formData.value.name.trim()) {
    errors.value.name = "Tên tag là bắt buộc";
  } else if (formData.value.name.length < 2) {
    errors.value.name = "Tên tag phải có ít nhất 2 ký tự";
  } else {
    errors.value.name = "";
  }
};

const validateCategory = () => {
  if (!formData.value.category.trim()) {
    errors.value.category = "Danh mục là bắt buộc";
  } else {
    errors.value.category = "";
  }
};

const showAllCategories = () => {
  filteredCategories.value = [...getCategories.value];
};

const resetForm = () => {
  formData.value = {
    name: "",
    category: "",
    description: "",
  };
  errors.value = {
    name: "",
    category: "",
  };
};

const handleClose = () => {
  if (!isLoading.value) {
    resetForm();
    emit("update:visible", false);
  }
};

const handleSubmit = async () => {
  // Validate all fields
  validateName();
  validateCategory();

  if (!isFormValid.value) {
    return;
  }

  try {
    const request: TagAddRequest = {
      name: formData.value.name.trim(),
      category: formData.value.category.trim(),
      description: formData.value.description?.trim() || undefined,
    };

    const result = await addTag(request);

    if (result) {
      // Refetch the tag list to ensure we have the latest data
      await loadTags();

      emit("tag-created", result);
      resetForm();
      emit("update:visible", false);
    }
  } catch (err) {
    console.error("Failed to create tag:", err);
  }
};

// Watch for visibility changes to reset form
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      resetForm();
      // Initialize categories for autocomplete with actual categories from tags
      filteredCategories.value = [...getCategories.value];
    }
  },
);
</script>
