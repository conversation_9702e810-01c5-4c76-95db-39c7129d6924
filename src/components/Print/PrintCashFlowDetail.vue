<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useDateFormat } from "@vueuse/core";

import useCashflow from "@/hooks/useCashflow";
import useDepartment from "@/hooks/useDepartment";
import { useAuth } from "@/pages/auth/config/auth-composable";
import { formatCurrency } from "@/utils/helper";
import { numberToWords } from "@/utils/string";

import PrintLayout from "./PrintLayout.vue";

const route = useRoute();
const cashflowId = Number(route.params.id);

const { currentUser } = useAuth();
const { getCashFlow, currentCashFlow } = useCashflow({ useStore: false });
const { getDepartmentNameById } = useDepartment();

// Fetch data
const fetchCashFlowData = async () => {
  if (cashflowId) {
    await getCashFlow(cashflowId);
  }
};

onMounted(fetchCashFlowData);

// Helper functions
const formatDate = (date: string) => {
  return useDateFormat(date, "DD/MM/YYYY").value;
};

const formatDateTime = (date: string) => {
  return useDateFormat(date, "DD/MM/YYYY HH:mm").value;
};

// Strip HTML tags and decode HTML entities for print view
const stripHtmlTags = (html: string): string => {
  if (!html) return "";

  // Create a temporary div element to parse HTML
  const temp = document.createElement("div");
  temp.innerHTML = html;

  // Get text content (automatically strips HTML tags)
  let text = temp.textContent || temp.innerText || "";

  // Clean up extra whitespace and newlines
  text = text.replace(/\s+/g, " ").trim();

  return text;
};

const getStateLabel = (state: string) => {
  switch (state) {
    case "PENDING":
      return "Chờ duyệt";
    case "APPROVED":
      return "Đã duyệt";
    case "REJECTED":
      return "Từ chối";
    case "PAID":
      return "Đã thanh toán";
    case "CANCELED":
      return "Đã hủy";
    default:
      return state;
  }
};

const printTitle = computed(() => {
  if (!currentCashFlow.value) return "PHIẾU THU CHI";
  return currentCashFlow.value.type === "income" ? "PHIẾU THU" : "PHIẾU CHI";
});

const amountInWords = computed(() => {
  if (!currentCashFlow.value) return "";
  return numberToWords(currentCashFlow.value.amount);
});

// Get department name (from counterpart - person who receives/pays money)
const departmentName = computed(() => {
  if (!currentCashFlow.value?.counterpart?.department_id) return "";
  return getDepartmentNameById(currentCashFlow.value.counterpart.department_id);
});

// Merge category and description for content
const contentText = computed(() => {
  if (!currentCashFlow.value) return "";

  const category = currentCashFlow.value.category?.name || "";
  const description = stripHtmlTags(currentCashFlow.value.description || "");

  if (category && description) {
    return `${category} - ${description}`;
  }
  return category || description || "";
});

// Format notes as plain text for printing
const notesText = computed(() => {
  if (!currentCashFlow.value?.notes || currentCashFlow.value.notes.length === 0) {
    return "";
  }

  return currentCashFlow.value.notes
    .map((note, index) => {
      const noteText = stripHtmlTags(note.body);
      // Note: creator field may not exist in current data structure
      const creator = (note as any).creator?.name || "Không rõ";
      return `${index + 1}. ${creator}: ${noteText}`;
    })
    .join(" | ");
});

// Helper computed properties for display logic
const showRecipientInfo = computed(() => {
  return currentCashFlow.value?.recipient?.name;
});

const showApproverInfo = computed(() => {
  return currentCashFlow.value?.approver?.name;
});

const showPayerInfo = computed(() => {
  return (
    currentCashFlow.value?.payer?.name &&
    currentCashFlow.value?.payer?.id !== currentCashFlow.value?.counterpart?.id
  );
});

const recipientDisplayText = computed(() => {
  if (!currentCashFlow.value?.recipient?.name) return "";

  let text = currentCashFlow.value.recipient.name;
  if (currentCashFlow.value.paid_at && currentCashFlow.value.type === "expense") {
    text += ` (Đã chi: ${formatDateTime(currentCashFlow.value.paid_at)})`;
  }
  return text;
});

const approverDisplayText = computed(() => {
  if (!currentCashFlow.value?.approver?.name) return "";

  let text = currentCashFlow.value.approver.name;
  // Note: approved_at field may not exist in the current data structure
  // Using created_at as fallback for approval timestamp
  if (currentCashFlow.value.state === "APPROVED" || currentCashFlow.value.state === "PAID") {
    text += ` (Duyệt: ${formatDateTime(currentCashFlow.value.created_at)})`;
  }
  return text;
});

const payerDisplayText = computed(() => {
  if (!showPayerInfo.value) return "";

  let text = currentCashFlow.value!.payer!.name;
  if (currentCashFlow.value!.paid_at) {
    text += ` (Đã chi: ${formatDateTime(currentCashFlow.value!.paid_at)})`;
  }
  return text;
});
</script>

<template>
  <PrintLayout v-if="currentCashFlow" :title="printTitle" :print-date="currentCashFlow.created_at">
    <template #content>
      <!-- Main Information in vertical layout -->
      <div class="mb-8 space-y-4">
        <!-- Counterpart Information -->
        <div class="flex">
          <span class="w-60"
            >{{ currentCashFlow.type === "income" ? "Người thu" : "Người chi" }}:</span
          >
          <span
            class="ml-2 flex-1"
            :class="
              currentCashFlow.counterpart?.name ? '' : 'border-b border-dotted border-gray-400'
            "
          >
            {{ currentCashFlow.counterpart?.name || "" }}
          </span>
        </div>

        <!-- Recipient Information (if available) -->
        <div v-if="showRecipientInfo" class="flex">
          <span class="w-60">Người nhận:</span>
          <span class="ml-2 flex-1">
            {{ recipientDisplayText }}
          </span>
        </div>

        <!-- Approver Information (if available) -->
        <div v-if="showApproverInfo" class="flex">
          <span class="w-60">Người duyệt:</span>
          <span class="ml-2 flex-1">
            {{ approverDisplayText }}
          </span>
        </div>

        <!-- Department -->
        <div class="flex">
          <span class="w-60">Bộ phận:</span>
          <span class="ml-2 flex-1">
            {{ departmentName || "..........................................................." }}
          </span>
        </div>

        <!-- Content (merged category + description) -->
        <div class="flex">
          <span class="w-60"
            >Nội dung {{ currentCashFlow.type === "income" ? "thu" : "chi" }}:</span
          >
          <span class="ml-2 flex-1">
            {{ contentText || "..........................................................." }}
          </span>
        </div>

        <!-- Amount in numbers -->
        <div class="flex">
          <span class="w-60">{{ currentCashFlow.type === "income" ? "Thu" : "Chi" }} số tiền:</span>
          <span class="ml-2 flex-1">
            {{ formatCurrency(currentCashFlow.amount) }}
          </span>
        </div>

        <!-- Amount in words -->
        <div class="flex">
          <span class="w-60">Bằng chữ:</span>
          <span class="ml-2 flex-1">
            {{ amountInWords }}
          </span>
        </div>

        <!-- Notes (if any) -->
        <div v-if="notesText" class="flex">
          <span class="w-60">Ghi chú:</span>
          <span class="ml-2 flex-1">
            {{ notesText }}
          </span>
        </div>
      </div>
    </template>

    <template #footer>
      <!-- Date and location -->
      <div class="mb-10 text-right italic">
        <span
          >TP. HCM, ngày {{ new Date(currentCashFlow.created_at).getDate() }} tháng
          {{ new Date(currentCashFlow.created_at).getMonth() + 1 }} năm
          {{ new Date(currentCashFlow.created_at).getFullYear() }}</span
        >
      </div>

      <!-- Signatures in 4 columns as shown in the original -->
      <div class="grid grid-cols-4 gap-4 text-center">
        <!-- Giám đốc -->
        <div>
          <p class="mb-16 font-medium">Giám đốc</p>
        </div>

        <!-- Quản lý -->
        <div>
          <p class="mb-16 font-medium">Quản lý</p>
        </div>

        <!-- Người lập phiếu -->
        <div>
          <p class="mb-12 font-medium">Người lập phiếu</p>
          <p class="font-semibold">
            {{ currentCashFlow.creator?.name || currentUser?.name || "..." }}
          </p>
        </div>

        <!-- Người thu/chi tiền -->
        <div>
          <p class="mb-12 font-medium">
            {{ currentCashFlow.type === "income" ? "Người thu tiền" : "Người chi tiền" }}
          </p>
          <p class="font-semibold">{{ currentCashFlow.counterpart?.name || "..." }}</p>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
