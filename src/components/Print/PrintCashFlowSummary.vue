<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { useRoute } from "vue-router";

import type { CashFlowReportRequest } from "@/api/bcare-types-v2";
import { formatCurrency } from "@/utils/helper";
import { transformDateRangeForDateOnlyFilter } from "@/utils/time-helper";
import useCashflow from "@/hooks/useCashflow";

import PrintLayout from "./PrintLayout.vue";

const route = useRoute();

// Use the composable without store (standalone mode for print)
const { fetchCashFlowSummary, summary, isLoading } = useCashflow({ useStore: false });

// Extract date range from route params
const dateRangeParam = route.params.dateRange as string | undefined;

// Parse date range from route params
const parseDateRange = (dateRangeStr: string | undefined) => {
  if (!dateRangeStr || dateRangeStr === "null") return null;

  try {
    const parsed = JSON.parse(decodeURIComponent(dateRangeStr));
    if (Array.isArray(parsed) && parsed.length >= 1) {
      return parsed.map((dateStr) => new Date(dateStr));
    }
  } catch (error) {
    console.error("Failed to parse date range:", error);
  }

  return null;
};

const dateRange = ref(parseDateRange(dateRangeParam));

// Fetch data directly on component mount
onMounted(async () => {
  try {
    const reportRequest: CashFlowReportRequest = {};

    // Only apply date range filter if provided
    if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length >= 1) {
      let dateRangeArray: Date[] | null = null;

      if (dateRange.value.length === 1 && dateRange.value[0]) {
        // Single date selected - use same date for from and to
        dateRangeArray = [dateRange.value[0], dateRange.value[0]];
      } else if (dateRange.value.length === 2) {
        const startDate = dateRange.value[0];
        const endDate = dateRange.value[1];

        if (startDate && endDate) {
          dateRangeArray = [startDate, endDate];
        } else if (startDate) {
          // Only start date selected - use same date for both
          dateRangeArray = [startDate, startDate];
        }
      }

      if (dateRangeArray) {
        const dateFilter = transformDateRangeForDateOnlyFilter(dateRangeArray);
        if (dateFilter) {
          reportRequest.from_date = dateFilter.from;
          reportRequest.to_date = dateFilter.to;
        }
      }
    }

    // Fetch cash flow summary with only date range filter
    await fetchCashFlowSummary(reportRequest);
  } catch (error) {
    console.error("Failed to fetch cash flow summary for print:", error);
  }
});

// Format date range for display with more explicit formatting
const formatDateRange = computed(() => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return "Tất cả thời gian";
  }

  const startDate = dateRange.value[0];
  const endDate = dateRange.value[1] || startDate;

  if (startDate && endDate) {
    const formatDate = (date: Date) => {
      const day = date.getDate().toString().padStart(2, "0");
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    if (startDate.getTime() === endDate.getTime()) {
      return `Ngày ${formatDate(startDate)}`;
    }

    return `Từ ngày ${formatDate(startDate)} đến ngày ${formatDate(endDate)}`;
  }

  return "Tất cả thời gian";
});

// Payment method data for display (main stats)
const paymentMethodData = computed(() => [
  {
    label: "Tiền mặt",
    value: summary.value.total_cash_all,
  },
  {
    label: "Thẻ tín dụng",
    value: summary.value.total_credit_card_all,
  },
  {
    label: "mPOS",
    value: summary.value.total_mpos_all,
  },
  {
    label: "Chuyển khoản",
    value: summary.value.total_bank_all,
  },
  {
    label: "MoMo",
    value: summary.value.total_momo_all,
  },
]);

// Summary totals (displayed at bottom) - now includes net amount
const summaryTotals = computed(() => [
  {
    label: "Tổng thu",
    value: summary.value.total_income,
    isPositive: true,
  },
  {
    label: "Tổng chi",
    value: summary.value.total_expense,
    isPositive: false,
  },
  {
    label: "Tồn quỹ",
    value: summary.value.net_amount,
    isPositive: summary.value.net_amount >= 0,
  },
]);
</script>

<template>
  <PrintLayout v-if="!isLoading && summary" title="BÁO CÁO THU CHI">
    <template #content>
      <!-- Report Time Period -->
      <div class="mb-4">
        <p class="font-medium">{{ formatDateRange }}</p>
      </div>

      <!-- Summary Totals Section -->
      <div class="mb-8">
        <table class="w-full border-collapse border border-gray-300">
          <thead>
            <tr class="bg-gray-50">
              <th class="w-2/3 border border-gray-300 px-4 py-2 text-left">Tổng</th>
              <th class="w-1/3 border border-gray-300 px-4 py-2 text-right">Số tiền</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in summaryTotals" :key="item.label">
              <td class="border border-gray-300 px-4 py-2 font-medium">
                {{ item.label }}
              </td>
              <td
                class="border border-gray-300 px-4 py-2 text-right"
                :class="item.isPositive ? 'text-green-600' : 'text-red-600'"
              >
                {{ formatCurrency(item.value) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Payment Methods Table -->
      <div class="mb-8">
        <table class="w-full border-collapse border border-gray-300">
          <thead>
            <tr class="bg-gray-50">
              <th class="w-2/3 border border-gray-300 px-4 py-2 text-left">
                Phương thức thanh toán
              </th>
              <th class="w-1/3 border border-gray-300 px-4 py-2 text-right">Số tiền</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in paymentMethodData" :key="item.label">
              <td class="border border-gray-300 px-4 py-2 font-medium">
                {{ item.label }}
              </td>
              <td class="border border-gray-300 px-4 py-2 text-right">
                {{ formatCurrency(item.value) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </template>

    <template #footer>
      <!-- Date and location -->
      <div class="mb-10 text-right italic">
        <span>
          TP. HCM, ngày {{ new Date().getDate() }} tháng {{ new Date().getMonth() + 1 }} năm
          {{ new Date().getFullYear() }}
        </span>
      </div>

      <!-- Signatures -->
      <div class="grid grid-cols-3 gap-4 text-center">
        <!-- Người lập báo cáo -->
        <div>
          <p class="mb-16 font-medium">Người lập báo cáo</p>
        </div>

        <!-- Kế toán -->
        <div>
          <p class="mb-16 font-medium">Kế toán</p>
        </div>

        <!-- Giám đốc -->
        <div>
          <p class="mb-16 font-medium">Giám đốc</p>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
