<template>
  <div :class="['flex', inline ? 'flex-row items-center gap-2' : 'flex-col']">
    <div :class="[inline ? 'flex items-center gap-2' : `${props.size === 'sm' ? '' : 'mb-1'} flex items-center gap-2`]">
      <i v-if="icon" :class="[icon, sizeClasses.icon, `text-${iconColor}`]" />
      <span :class="[sizeClasses.label, 'text-slate-500', 'font-normal']">
        {{ label }}<span v-if="showColon">:</span>
      </span>
      <i
        v-if="readonly"
        class="pi pi-lock ml-1 text-xs text-gray-400"
        title="Bạn không có quyền chỉnh sửa"
      ></i>
    </div>
    <div :class="{ 'pointer-events-none opacity-80': readonly }">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

// import { useHashColor } from "@/composables/useHashColor";

const props = withDefaults(
  defineProps<{
    label: string;
    icon?: string;
    iconColor?: string;
    size?: "sm" | "md" | "lg";
    inline?: boolean;
    showColon?: boolean;
    readonly?: boolean;
  }>(),
  {
    size: "md",
    inline: false,
    showColon: false,
    readonly: false,
  },
);

const iconColor = computed(() => {
  if (props.iconColor) {
    return props.iconColor;
  }
  return "slate-500";
});

const sizeClasses = computed(() => ({
  icon: {
    "text-xs": props.size === "sm" || props.size === undefined,
    "text-sm": props.size === "md",
    "text-base": props.size === "lg",
  },
  label: {
    "text-xs py-0.5": props.size === "sm" || props.size === undefined,
    "text-sm py-1": props.size === "md",
    "text-base py-1.5": props.size === "lg",
  },
}));
</script>
