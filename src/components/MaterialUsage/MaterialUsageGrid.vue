<script lang="ts" setup>
import { computed } from "vue";
import type { ReportColumn, ReportRow, ReportCell } from "@/composables/useMaterialUsageReport";

interface Props {
  columns: ReportColumn[];
  rows: ReportRow[];
  cells: Record<number, Record<string, ReportCell>>;
  totals: Record<number, ReportCell>;
  size?: "sm" | "xs";
}

const props = withDefaults(defineProps<Props>(), {
  size: "sm",
});

// Size classes - Match original MaterialUsageMatrix
const sizeClasses = computed(() => {
  if (props.size === "xs") {
    return {
      table: "text-xs",
      header: "p-1 text-xs",
      cell: "p-1 text-xs",
    };
  }

  return {
    table: "text-sm",
    header: "p-2 text-sm",
    cell: "p-2 text-sm",
  };
});

/**
 * Get cell value for a specific material and operation
 */
const getCellValue = (materialId: number, operationId: string): ReportCell | null => {
  return props.cells[materialId]?.[operationId] || null;
};

/**
 * Get total value for a specific material
 */
const getTotalValue = (materialId: number): ReportCell | null => {
  return props.totals[materialId] || null;
};
</script>

<template>
  <div class="w-full overflow-x-auto">
    <table :class="['w-full border-collapse', sizeClasses.table]">
      <!-- Header row -->
      <thead>
        <tr class="border-b border-dashed border-gray-300 bg-gray-50">
          <th
            :class="[
              'border-r border-dashed border-gray-300 text-left font-semibold',
              sizeClasses.header,
            ]"
          >
            Vật tư
          </th>
          <th
            v-for="column in columns"
            :key="column.id"
            :class="[
              'min-w-[100px] border-r border-dashed border-gray-300 text-center font-semibold',
              sizeClasses.header,
            ]"
          >
            <div class="flex items-center justify-center">
              <span>{{ column.label }}</span>
            </div>
          </th>
          <th
            :class="[
              'border-dashed border-gray-300 bg-blue-50 text-center font-semibold',
              sizeClasses.header,
            ]"
          >
            Tổng
          </th>
        </tr>
      </thead>

      <!-- Data rows -->
      <tbody>
        <tr
          v-for="(row, index) in rows"
          :key="row.id"
          class="hover:bg-gray-50"
        >
          <td
            :class="[
              'border-r border-dashed border-gray-300 align-middle',
              { 'border-b border-dashed': index < rows.length - 1 },
              sizeClasses.cell,
            ]"
          >
            {{ row.label }}
          </td>
          <td
            v-for="column in columns"
            :key="column.id"
            :class="[
              'border-r border-dashed border-gray-300 text-center align-middle',
              { 'border-b border-dashed': index < rows.length - 1 },
              sizeClasses.cell,
            ]"
          >
            <span class="font-mono tabular-nums">
              {{ getCellValue(row.id, column.id)?.formattedValue || "-" }}
            </span>
          </td>
          <td
            :class="[
              'bg-blue-50 text-center align-middle',
              {
                'border-b border-dashed border-gray-300': index < rows.length - 1,
              },
              sizeClasses.cell,
            ]"
          >
            <div
              v-if="getTotalValue(row.id) && getTotalValue(row.id)!.value > 0"
              class="flex items-center justify-center"
            >
              <span class="min-w-[60px] text-center font-mono tabular-nums">
                {{ getTotalValue(row.id)!.formattedValue }}
              </span>
              <span class="ml-1 min-w-[40px] text-left">
                {{ row.unit }}
              </span>
            </div>
            <span v-else class="font-mono tabular-nums">-</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>