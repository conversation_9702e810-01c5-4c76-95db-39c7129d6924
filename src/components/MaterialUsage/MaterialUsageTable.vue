<template>
  <div class="w-full">
    <!-- Matrix grid -->
    <div
      v-if="operations.length > 0"
      class="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 overflow-auto rounded-lg border border-gray-200"
      style="max-height: 60vh;"
    >
      <div
        class="grid min-w-max gap-px bg-gray-200"
        :style="gridTemplateColumns"
      >
        <!-- Header row -->
        <!-- Material column header with search -->
        <div
          class="sticky top-0 left-0 z-30 flex items-center gap-2 bg-gray-100 px-3 py-2.5 font-semibold text-gray-900"
        >
          <span class="whitespace-nowrap text-sm">Vật tư</span>
          <!-- Search input -->
          <IconField class="min-w-[80px] flex-1">
            <InputText
              v-model="searchTerm"
              placeholder="Tìm kiếm"
              class="w-full rounded-none border-0 border-b border-gray-300 bg-gray-50 p-1 text-xs font-normal shadow-none focus:border-info focus:ring-0"
            />
            <InputIcon
              v-if="searchTerm"
              class="pi pi-times cursor-pointer"
              @click="clearMaterialSearch"
            />
          </IconField>
        </div>

        <!-- Operation column headers with toggle buttons -->
        <div
          v-for="operation in operations"
          :key="operation.id"
          class="sticky top-0 z-20 min-w-[120px] bg-gray-100 px-3 py-2.5 text-center font-semibold text-gray-900"
        >
          <div class="flex h-full flex-col items-center justify-center gap-1">
            <span class="text-sm leading-tight">{{ operation.name }}</span>
            <button
              v-if="getProductQuantity(operation.id) > 1 && !isEditMode"
              @click="toggleOperationMultiplier(operation.id)"
              :class="[
                'relative inline-flex items-center gap-1 rounded-full px-2.5 py-1 text-xs font-medium transition-all duration-200',
                isOperationToggled(operation.id)
                  ? 'bg-blue-500 text-white shadow-sm hover:bg-blue-600'
                  : 'border border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50',
              ]"
              :title="
                isOperationToggled(operation.id)
                  ? 'Click to show base quantity'
                  : `Click to multiply by ${getProductQuantity(operation.id)}`
              "
            >
              <span class="flex items-center gap-1">
                <span class="tabular-nums">×{{ getProductQuantity(operation.id) }}</span>
              </span>
            </button>
          </div>
        </div>

        <!-- Total column header -->
        <div
          class="sticky top-0 z-20 flex min-w-[120px] items-center justify-center bg-blue-100 px-3 py-2.5 text-center font-semibold text-gray-900"
        >
          <span class="text-sm">Tổng</span>
        </div>

        <!-- Material rows -->
        <template v-for="material in filteredMaterials" :key="material.id">
          <!-- Material name cell -->
          <div
            class="group sticky left-0 z-10 flex min-h-[60px] items-center justify-between bg-white px-3 py-2.5"
          >
            <div class="flex min-w-0 flex-1 items-center gap-2">
              <div class="min-w-0 flex-1">
                <HighlightText
                  :text="material.name"
                  :highlight="searchTerm"
                  class="break-words text-sm font-medium leading-tight"
                />
              </div>
              <i
                v-if="isCustomMaterial(material.id)"
                class="pi pi-plus-circle flex-shrink-0 text-sm text-blue-600"
                title="Vật tư thêm thủ công"
              />
            </div>
            <button
              @click="removeMaterial(material.id)"
              class="ml-2 flex-shrink-0 text-red-500 opacity-0 transition-opacity hover:text-red-700 group-hover:opacity-100"
              title="Xóa vật tư"
            >
              <i class="pi pi-times text-xs"></i>
            </button>
          </div>

          <!-- Usage cells for each operation -->
          <div
            v-for="operation in operations"
            :key="operation.id"
            :class="[
              'flex min-h-[60px] items-center justify-center px-3 py-2.5',
              isOperationToggled(operation.id) ? 'bg-blue-50' : 'bg-white',
            ]"
          >
            <MaterialUsageCell
              :model-value="getCellValue(material.id, operation.id)"
              :material-id="Number(material.id)"
              :operation-id="operation.id"
              :is-dirty="isCellDirty(material.id, operation.id)"
              :is-multiplied="isOperationToggled(operation.id)"
              :readonly="props.readonly"
              @update:model-value="
                (value) => updateCellValue(material.id, operation.id, value)
              "
              @edit-start="() => handleCellEditStart(material.id, operation.id)"
              @edit-save="() => handleCellEditSave(material.id, operation.id)"
            />
          </div>

          <!-- Total cell -->
          <div class="flex min-h-[60px] items-center justify-center bg-blue-50 px-3 py-2.5">
            <div
              v-if="calculateTotal(Number(material.id)) > 0"
              class="flex items-center justify-center"
            >
              <span class="min-w-[60px] text-center font-mono text-sm font-medium tabular-nums">
                {{ formatTotal(calculateTotal(Number(material.id))) }}
              </span>
              <span class="ml-1 min-w-[40px] text-left text-xs text-gray-600">
                {{ getMaterialUnit(Number(material.id)) }}
              </span>
            </div>
            <span v-else class="font-mono text-sm tabular-nums">-</span>
          </div>
        </template>

        <!-- Add material row -->
        <div
          class="flex min-h-[48px] cursor-pointer items-center justify-center border-t border-gray-300 bg-white px-3 py-2.5 text-center transition-colors hover:bg-gray-50"
          :style="`grid-column: 1 / -1`"
          @click="openMaterialSelector"
        >
          <span class="text-sm font-medium text-blue-600 hover:text-blue-800">
            <i class="pi pi-plus mr-2"></i>
            Thêm vật tư
          </span>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="py-8 text-center text-gray-500">
      <i class="pi pi-hammer mb-4 block text-3xl"></i>
      <h3 class="mb-2 text-lg font-medium">Chưa có operations</h3>
      <p>Vui lòng chọn operations để bắt đầu quản lý vật tư</p>
    </div>

    <!-- Material selector popup -->
    <MaterialSelector
      ref="materialSelectorRef"
      v-model="selectedMaterialsInSelector"
      :selected-material-ids="existingMaterialIds"
      @material-selected="handleMaterialSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import type { Material } from "@/api/bcare-types-v2";
import { useMaterialUsageMatrixStore } from "@/stores/material-usage-matrix-store";
import useMaterial from "@/hooks/useMaterial";
import useMaterialUsage from "@/hooks/useMaterialUsage";
import MaterialUsageCell from "./MaterialUsageCell.vue";
import MaterialSelector from "@/components/Operation/MaterialSelector.vue";

import HighlightText from "@/base-components/HighlightText.vue";
import { fuzzySearch } from "@/utils/string";

interface Props {
  /** Whether the table is readonly */
  readonly?: boolean;
  /** Operations data from parent component - supports both standard (number) and custom (string) operations */
  operations?: { id: number | string; name: string }[];
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  operations: () => [],
});

// Store and composables
const store = useMaterialUsageMatrixStore();
const { materials, getMaterialById } = useMaterial({ autoLoad: false }); // Only for MaterialSelector
const { deleteMaterialUsage } = useMaterialUsage({ autoLoad: false }); // For API deletion in edit mode

// Local state
const searchTerm = ref("");
const materialSelectorRef = ref();
const selectedMaterialsInSelector = ref<number[]>([]);

// Store getters
const {
  selectedOperations,
  usageMatrix,
  isOperationToggled,
  getProductQuantity,
  hasChanges,
  isEditMode,
} = store;

// Store actions
const {
  updateCell,
  getCellValue,
  isCellDirty,
  toggleOperationMultiplier,
  addCustomMaterial: storeAddCustomMaterial,
  removeCustomMaterial: storeRemoveCustomMaterial,
  calculateTotal,
} = store;

/**
 * Get operations data from props or store
 */
const operations = computed(() => {
  // Use operations from props if explicitly provided (including empty array)
  if (props.operations !== undefined) {
    return props.operations;
  }

  return selectedOperations.map((id) => ({
    id,
    name: `Operation ${id}`,
  }));
});

/**
 * Get materials from matrix store (only materials that have data)
 * Sort by: quota materials first, then custom materials at bottom
 */
const filteredMaterials = computed(() => {
  const materialsInMatrix = store.getMaterialsInMatrix();

  // Sort materials: quota materials first, custom materials at bottom
  const sortedMaterials = materialsInMatrix.sort((a, b) => {
    const aIsCustom = isCustomMaterial(a.id);
    const bIsCustom = isCustomMaterial(b.id);
    if (aIsCustom === bIsCustom) {
      return 0;
    }

    return aIsCustom ? 1 : -1;
  });

  if (!searchTerm.value.trim()) {
    return sortedMaterials;
  }

  const query = searchTerm.value;
  return sortedMaterials.filter(
    (material) =>
      fuzzySearch(material.name, query) || (material.code && fuzzySearch(material.code, query)),
  );
});

/**
 * Existing material IDs to show as selected in selector
 * Include all materials currently in the matrix
 */
const existingMaterialIds = computed(() => {
  return Object.keys(store.usageMatrix).map((id) => Number(id));
});

/**
 * Dynamic grid template columns
 * Material column optimized width, operations are fixed (120px), total is fixed (120px)
 */
const gridTemplateColumns = computed(() => {
  const operationColumns = operations.value.length;
  return `grid-template-columns: minmax(160px, 1fr) repeat(${operationColumns}, 120px) 120px`;
});

/**
 * Check if material is custom (manually added)
 */
const isCustomMaterial = (materialId: number): boolean => {
  return store.customMaterials.has(materialId);
};

/**
 * Get material unit for display in totals
 */
const getMaterialUnit = (materialId: number): string => {
  const material = getMaterialById(materialId);
  return material?.unit || "";
};

/**
 * Update cell value in store - supports both standard and custom operations
 */
const updateCellValue = (materialId: number, operationId: number | string, value: number) => {
  updateCell(materialId, operationId, value);
};

/**
 * Handle cell edit start - supports both standard and custom operations
 */
const handleCellEditStart = (materialId: number, operationId: number | string) => {
  // Cell editing is handled by the component
};

/**
 * Handle cell edit save - supports both standard and custom operations
 */
const handleCellEditSave = (materialId: number, operationId: number | string) => {
  // Cell saving is handled by the component
};

/**
 * Open material selector popover
 */
const openMaterialSelector = async (event: MouseEvent) => {
  // Load materials for selector if not loaded
  if (materials.value.length === 0) {
    const { loadMaterials } = useMaterial({ autoLoad: false });
    await loadMaterials();
  }

  materialSelectorRef.value?.popoverRef?.toggle(event);
};

/**
 * Handle material selection from MaterialSelector
 * Toggle add/remove material based on current state
 */
const handleMaterialSelected = (material: Material) => {
  // Check if material is already in matrix using existing computed property
  const isAlreadyInMatrix = existingMaterialIds.value.includes(material.id);

  if (isAlreadyInMatrix) {
    // Material is in matrix, remove it (same flow as clicking X button in table)
    storeRemoveCustomMaterial(material.id);
  } else {
    // Material not in matrix, add it
    storeAddCustomMaterial(material.id);
  }

  // Popover will auto-hide after selection for single mode
};

/**
 * Remove material from table
 * In edit mode: Delete existing usage records via API, then remove from matrix
 * In create mode: Just remove from matrix
 */
const removeMaterial = async (materialId: number) => {
  if (isEditMode) {
    try {
      const materialOperations = store.usageMatrix[materialId];
      if (materialOperations) {
        const deletePromises: Promise<any>[] = [];

        Object.entries(materialOperations).forEach(([operationId, cell]) => {
          const cellData = cell as any;
          if (cellData.existingId) {
            deletePromises.push(
              deleteMaterialUsage({ id: cellData.existingId }).catch((error) => {
                console.error("❌ Failed to delete usage:", cellData.existingId, error);
                throw error;
              }),
            );
          }
        });

        if (deletePromises.length > 0) {
          await Promise.all(deletePromises);
        }
      }

      storeRemoveCustomMaterial(materialId);
    } catch (error) {
      console.error("❌ Failed to remove material:", materialId, error);
    }
  } else {
    storeRemoveCustomMaterial(materialId);
  }
};

/**
 * Format total value for display (remove trailing zeros like MaterialUsageMatrix)
 */
const formatTotal = (value: number): string => {
  return parseFloat(value.toString()).toString();
};

/**
 * Clear material search
 */
const clearMaterialSearch = () => {
  searchTerm.value = "";
};

// Watch for operations changes to update store
watch(
  () => props.operations,
  (newOperations) => {
    const operationIds = newOperations ? newOperations.map((op) => op.id) : [];

    if (isEditMode) {
      // ✅ EDIT MODE: Direct assignment since TreatmentForm already handles the merge logic
      store.selectedOperations = operationIds;

      const currentMatrix = store.usageMatrix;
      Object.keys(currentMatrix).forEach((materialId) => {
        const materialOps = currentMatrix[Number(materialId)];
        Object.keys(materialOps).forEach((operationId) => {
          const opId = Number(operationId);
          if (!operationIds.includes(opId)) {
            delete materialOps[opId];
          }
        });

        if (Object.keys(materialOps).length === 0) {
          delete currentMatrix[Number(materialId)];
        }
      });
    } else {
      // ✅ CREATE MODE: Let TreatmentForm handle the merge logic via handleOperationsChangedV2
      // This watch is mainly for UI updates, the actual store update is handled by TreatmentForm
      // Only update if operations are different to avoid infinite loops
      const currentOps = store.selectedOperations;
      const newOpsSet = new Set(operationIds);
      const currentOpsSet = new Set(currentOps);

      const isDifferent = newOpsSet.size !== currentOpsSet.size ||
        [...newOpsSet].some(id => !currentOpsSet.has(id));

      if (isDifferent) {
        store.selectedOperations = operationIds;
      }
    }
  },
  { immediate: true, deep: true },
);

watch(
  () => hasChanges,
  (_newValue) => {
    // Optional: Handle changes
  },
);
</script>
