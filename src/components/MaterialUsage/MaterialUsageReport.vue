<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import useMaterialUsage from "@/hooks/useMaterialUsage";
import useMaterial from "@/hooks/useMaterial";
import useOperation from "@/hooks/useOperation";
import { useMaterialUsageReport } from "@/composables/useMaterialUsageReport";
import MaterialUsageGrid from "./MaterialUsageGrid.vue";
import type { MaterialUsageResponse } from "@/api/bcare-types-v2";

interface Props {
  attachmentId: number;
  size?: "sm" | "xs";
}

const props = withDefaults(defineProps<Props>(), {
  size: "sm",
});

// ===== DATA FETCHING HOOKS =====
const { listMaterialUsages, isLoading } = useMaterialUsage({ autoLoad: false });
const { getMaterialNameById, getMaterialById, materials } = useMaterial({ autoLoad: true });
const { getOperationById, operationData } = useOperation({ autoLoad: true });

// ===== LOCAL STATE =====
const materialUsages = ref<MaterialUsageResponse[]>([]);

// ===== COMPUTED CONVERSIONS =====
// Convert operationData (object) to operations array for composable
const operations = computed(() => Object.values(operationData.value));

// ===== DATA TRANSFORMATION =====
const { reportData } = useMaterialUsageReport({
  materialUsages,
  materials,
  operations,
  getMaterialNameById,
  getOperationById,
  getMaterialById,
});

// ===== METHODS =====
const loadMaterialUsages = async () => {
  try {
    const response = await listMaterialUsages({
      filter: { attachment_id: props.attachmentId },
    });

    if (response?.material_usages) {
      materialUsages.value = response.material_usages;
    }
  } catch (error) {
    console.error("Error loading material usages:", error);
    materialUsages.value = [];
  }
};

// ===== LIFECYCLE =====
onMounted(() => {
  loadMaterialUsages();
});
</script>

<template>
  <div class="w-full">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center p-4">
      <i class="pi pi-spin pi-spinner text-lg text-gray-400"></i>
      <span class="ml-2 text-sm text-gray-500">Đang tải dữ liệu vật tư...</span>
    </div>

    <!-- No Data State -->
    <div v-else-if="reportData.isEmpty" class="p-4 text-center text-sm text-gray-500">
      Chưa có dữ liệu sử dụng vật tư
    </div>

    <!-- Report Grid -->
    <MaterialUsageGrid
      v-else
      :columns="reportData.columns"
      :rows="reportData.rows"
      :cells="reportData.cells"
      :totals="reportData.totals"
      :size="size"
    />
  </div>
</template>