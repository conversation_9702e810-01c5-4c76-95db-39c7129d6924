<template>
  <div class="relative flex items-center justify-center">
    <!-- Reuse existing QuantityEditor component -->
    <QuantityEditor
      v-model="quantity"
      :max-fraction-digits="5"
      :readonly="readonly"
      size="sm"
      placeholder="0"
      :class="['transition-colors text-center', cellClasses]"
      :aria-label="accessibilityLabel"
      @edit-start="handleEditStart"
      @edit-save="handleEditSave"
    />

    <!-- Dirty indicator - positioned to not affect center alignment -->
    <div
      v-if="isDirty"
      class="absolute right-0 top-0 h-2 w-2 rounded-full bg-yellow-400 translate-x-1 -translate-y-1"
      title="Chưa lưu"
    />

    <!-- Multiplied indicator - positioned to not affect center alignment -->
    <div
      v-if="isMultiplied"
      class="absolute left-0 top-0 h-2 w-2 rounded-full bg-blue-400 -translate-x-1 -translate-y-1"
      title="Đã nhân với số lượng sản phẩm"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import QuantityEditor from "@/base-components/QuantityEditor.vue";

interface Props {
  /** Material ID for this cell */
  materialId: number;
  /** Operation ID for this cell - supports both standard (number) and custom (string) operations */
  operationId: number | string;
  /** Whether this cell has unsaved changes */
  isDirty: boolean;
  /** Visual indicator for multiplied state */
  isMultiplied?: boolean;
  /** Whether the cell is readonly */
  readonly?: boolean;
}

interface Emits {
  /** Emitted when user starts editing */
  (e: "edit-start", value: number): void;
  /** Emitted when user saves edit */
  (e: "edit-save", value: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  isMultiplied: false,
});

const emit = defineEmits<Emits>();

// Use defineModel for v-model support (Vue 3.4+ best practice)
const quantity = defineModel<number>({ required: true });

/**
 * Computed CSS classes based on cell state
 */
const cellClasses = computed(() => {
  const classes = [];

  if (props.isDirty) {
    classes.push("bg-yellow-50", "border-yellow-300");
  } else if (props.isMultiplied) {
    classes.push("bg-blue-50", "border-blue-200");
  } else {
    classes.push("bg-white", "border-gray-300");
  }

  return classes.join(" ");
});

/**
 * Handle edit start event
 * @param value - Current value when edit starts
 */
const handleEditStart = (value: number) => {
  emit("edit-start", value);
};

/**
 * Handle edit save event
 * @param value - New value when edit is saved
 */
const handleEditSave = (value: number) => {
  emit("edit-save", value);
};

/**
 * Get accessibility label for screen readers
 */
const accessibilityLabel = computed(() => {
  let label = `Material ${props.materialId}, Operation ${props.operationId}`;

  if (props.isDirty) {
    label += ", có thay đổi chưa lưu";
  }

  if (props.isMultiplied) {
    label += ", đã nhân với số lượng sản phẩm";
  }

  if (props.readonly) {
    label += ", chỉ đọc";
  }

  return label;
});
</script>
