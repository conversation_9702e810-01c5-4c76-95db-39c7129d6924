<template>
  <div class="card flex justify-center">
    <Button type="button" icon="pi pi-cog" @click="toggle"> </Button>

    <Popover ref="op" :pt="{ root: { style: { '--p-popover-arrow-offset': '15rem' } } }">
      <div class="flex min-w-[15rem] max-w-[25rem] flex-col">
        <span class="block font-medium">{{ title }}</span>

        <div class="flex flex-wrap gap-2">
          <slot name="custom_component" />

          <template v-for="(setting, idx) in settings" :key="idx">
            <div v-if="setting.type !== 'group'" class="w-full">
              <component :is="getComponentType(setting.type)" v-model="settings[idx]" />
            </div>
            <div v-else class="w-full">
              <Fieldset class="pb-3">
                <template #legend>
                  <span class="block">{{ setting.label }}</span>
                </template>
                <div class="space-y-2">
                  <template
                    v-for="(childSetting, childIdx) in setting.children"
                    v-if="Array.isArray(setting.children)"
                    :key="childIdx"
                  >
                    <component
                      :is="getComponentType(childSetting.type)"
                      v-model="setting.children[childIdx]"
                    />
                  </template>
                </div>
              </Fieldset>
            </div>
          </template>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import { UniversalSetting } from "@/api/extend-types";

import SettingMultiNumberInput from "@/components/Settings/SettingMultiNumberInput.vue";
import SettingSelectButton from "@/components/Settings/SettingSelectButton.vue";
import SettingTextInput from "@/components/Settings/SettingTextInput.vue";
import SettingToggleButton from "@/components/Settings/SettingToggleButton.vue";
import { useSettingGlobalStore } from "@/stores/setting-global-store";

import SettingCheckbox from "./SettingCheckbox.vue";

interface Props {
  showFilter?: boolean;
  showDoctor?: boolean;
  title?: string;
  settingKey?: string;
}

const props = withDefaults(defineProps<Props>(), {
  settingKey: "defaultSettingKey",
  title: "Tùy chỉnh",
});

const { title, settingKey } = props;

const getComponentType = (type: UniversalSetting["type"]) => {
  const componentTypes: Record<string, any> = {
    toggle_button: SettingToggleButton,
    select: SettingSelectButton,
    checkbox: SettingCheckbox,
    text: SettingTextInput,
    "multi-number": SettingMultiNumberInput,
    group: "div", // Groups được xử lý riêng
  };
  return componentTypes[type] || "div";
};

const settingGlobalStore = useSettingGlobalStore();
const settings = computed(() => settingGlobalStore.state[settingKey]);

const op = ref();
const toggle = (event: MouseEvent) => {
  op.value.toggle(event);
};
</script>
