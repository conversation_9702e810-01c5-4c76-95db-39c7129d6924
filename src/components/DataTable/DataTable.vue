<template>
  <div ref="tableContainerRef" class="relative w-full">
    <DataTable
      v-model:filters="computedFilters"
      v-model:selection="computedSelection"
      :rows="props.rows ?? 10"
      :lazy="true"
      :columns="props.columns"
      :data="props.data"
      :striped-rows="props.stripedRows ?? false"
      :paginator="props.paginator ?? false"
      :total-records="props.totalRecords"
      :rows-per-page-options="props.rowsPerPageOptions ?? [10, 20, 50, 100]"
      :paginator-template="`${props.rowsPerPageOptions ? ' RowsPerPageDropdown' : ''} FirstPageLink PrevPageLink PageLinks CurrentPageReport NextPageLink LastPageLink JumpToPageInput`"
      :current-page-report-template="'Trang {currentPage} / {totalPages}'"
      :value="props.data"
      filter-display="row"
      @page="onInternalPageChange"
      :selectionMode="props.selectionMode ?? undefined"
      @row-click="onRowClick"
      :size="props.size"
      autoLayout
      :row-class="(data: T) => props.rowClass?.(data)"
      :scrollable="props.scrollable || props.autoHeight"
      :scrollHeight="calculatedScrollHeight"
      :resizableColumns="props.resizableColumns"
      :columnResizeMode="props.columnResizeMode"
      :class="{ 'hide-header-row': props.hideHeaderRow, 'hide-filter-row': props.hideFilterRow }"
      :pt="{
        root: {
          class: [
            '[.p-datatable-header-cell]:!py-3 !rounded-t-lg',
            '[&_.p-datatable-header-cell]:!py-3 !rounded-t-lg',
            '[&_.p-paginator]:!rounded-t-none',
            '[&_:deep(.p-paginator)]:!rounded-t-none !rounded-b-lg',
            '[&_td:first-child]:pl-5',
            '[&_td:last-child]:pr-5',
            '[&_th:first-child]:pl-5',
            '[&_th:last-child]:pr-5',
          ],
        },
        header: {
          class: ['!p-3', '!rounded-t-lg'],
        },
        headerRow: {
          class: props.hideHeaderRow ? ['!py-3', '!rounded-t-lg'] : [],
        },
        headerCell: {
          class: props.hideHeaderRow ? ['!py-3', '!rounded-t-lg'] : [],
        },
        thead: {
          class: [
            props.hideHeaderRow ? '[&>tr:first-child]:hidden' : '',
            props.hideHeaderRow ? '[&>tr:nth-child(2)>th:first-child]:!rounded-tl-lg' : '',
            props.hideHeaderRow ? '[&>tr:nth-child(2)>th:last-child]:!rounded-tr-lg' : '',
            props.hideFilterRow ? '[&>tr:nth-child(2)]:hidden' : '',
            props.hideFilterRow ? '[&>tr:first-child>th:first-child]:!rounded-tl-[0.5rem]' : '',
            props.hideFilterRow ? '[&>tr:first-child>th:last-child]:!rounded-tr-[0.5rem]' : '',
          ],
        },
        pcPaginator: {
          root: {
            class: [
              '[&_.p-inputnumber-input]:!min-w-[10ch]',
              '[&_.p-inputnumber-input]:!text-sm border-t border-x border-b',
              'flex items-center',
              'grid grid-cols-[1fr_auto_1fr] items-center',
              '[&_.p-paginator-left]:justify-self-start',
              '[&_.p-paginator-center]:justify-self-center',
              '[&_.p-paginator-right]:justify-self-end',
              'px-5',
            ],
          },
        },
      }"
    >
      <template #header v-if="props.showHeader">
        <div class="flex flex-wrap items-center justify-between gap-2 px-2">
          <div class="flex items-center gap-2">
            <slot name="left-header">
              <span class="text-base font-medium"
                >{{ props.title }} ({{ props.totalRecords ?? 0 }})</span
              >
            </slot>
          </div>
          <div class="flex items-center gap-2">
            <slot name="right-header" />
          </div>
        </div>
      </template>

      <Column v-if="props.checkbox" selection-mode="multiple" header-style="width: 2rem" />

      <template v-for="col in props.columns" :key="col.field">
        <Column
          v-bind="col"
          :sortable="false"
          :style="col.style || { width: 'auto' }"
          :showClearButton="col.showFilterClear ?? true"
          filterClearIcon="pi pi-times"
          filterClearIconClass="p-column-filter-clear-button"
        >
          <template v-if="$slots[col.field]" #body="slotProps">
            <slot :name="col.field" :data="slotProps.data" :index="slotProps.index" />
          </template>
          <template v-if="!props.hideFilterRow" #filter="{ filterModel, filterCallback }">
            <slot
              v-if="$slots[`${col.field}.filter`]"
              :name="`${col.field}.filter`"
              :filter-model="filterModel"
              :filter-callback="filterCallback"
            />
            <template v-else>
              <IconField v-if="col.filterType === 'text'" class="h-10">
                <InputIcon class="pi pi-search" />
                <InputText
                  :modelValue="trimmedFilterValue(filterModel?.value)"
                  type="text"
                  class="h-full"
                  :placeholder="col.filterPlaceholder || `Filter ${col.header}`"
                  @update:modelValue="
                    (value) => updateFilterValue(value, filterModel, filterCallback)
                  "
                  size="small"
                  fluid
                />
              </IconField>

              <Select
                v-else-if="col.filterType === 'select'"
                v-model="filterModel.value"
                :options="col.filterOptions"
                optionLabel="title"
                filter
                optionValue="value"
                :placeholder="col.filterPlaceholder || `Filter ${col.header}`"
                class="p-column-filter flex h-10 items-center"
                @change="filterCallback()"
                size="small"
                fluid
              />

              <DatePicker
                :key="`${col.field}-${filterModel?.value ? 'hasValue' : 'isNull'}`"
                class="h-10"
                v-else-if="col.filterType === 'dateRange'"
                v-model="filterModel.value"
                show-icon
                selection-mode="range"
                manual-input
                date-format="dd/mm/yy"
                :placeholder="col.filterPlaceholder || `Filter ${col.header}`"
                @date-select="filterCallback()"
                size="small"
                fluid
                show-other-months
                select-other-months
              />
            </template>
          </template>
        </Column>
      </template>

      <Column
        v-if="props.showActions?.edit || props.showActions?.delete || props.showActions?.custom"
        header=""
        key="actions"
      >
        <template #body="slotProps">
          <slot name="actions" :data="slotProps.data">
            <div class="ml-auto flex justify-end">
              <Button
                type="button"
                icon="pi pi-ellipsis-v"
                class="p-button-text p-button-rounded p-button-plain h-8 w-8"
                @click="(event) => toggleMenu(event, slotProps.data)"
                aria-haspopup="true"
              />
              <Menu
                :ref="(el) => setMenuRef(el, slotProps.data.id)"
                :id="`overlay_menu_${slotProps.data.id}`"
                :model="getMenuItems(slotProps.data)"
                :popup="true"
              >
                <template #item="{ item }">
                  <div
                    class="flex cursor-pointer items-center px-2 py-1.5"
                    :class="{
                      'text-danger': item.severity === 'danger',
                      'text-warning': item.severity === 'warning',
                    }"
                  >
                    <i :class="[item.icon, 'mr-2']"></i>
                    {{ item.label }}
                  </div>
                </template>
              </Menu>
            </div>
          </slot>
        </template>
      </Column>

      <template #empty>
        <div class="p-4" v-if="props.loading">
          <Skeleton v-for="i in 10" :key="i" class="mb-2" height="2rem" />
        </div>
        <div
          class="flex items-center justify-center p-4"
          v-if="!props.loading && !props.data?.length"
        >
          <Empty />
        </div>
      </template>

      <template #paginatorstart v-if="showPaginator">
        <div class="p-paginator-left flex h-8 min-w-[300px] items-center gap-2">
          <span class="text-sm font-normal">
            {{ paginatorStartText }}
          </span>
          <slot name="left-footer"></slot>
        </div>
      </template>

      <template #paginatorend v-if="showPaginator">
        <div class="p-paginator-right h-8 min-w-[100px]">
          <slot name="right-footer"></slot>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts" generic="T">
import { debounce } from "lodash";
import Button from "primevue/button";
import Column from "primevue/column";
import DataTable, { DataTableRowClickEvent } from "primevue/datatable";
import DatePicker from "primevue/datepicker";
import IconField from "primevue/iconfield";
import InputIcon from "primevue/inputicon";
import InputText from "primevue/inputtext";
import Menu from "primevue/menu";
import Select from "primevue/select";
import { computed, ref, onUnmounted } from "vue";
import type { MenuItem } from "primevue/menuitem";

import Empty from "@/base-components/Empty";
import { useFullscreenStore } from "@/stores/full-screen-store";

import { ColumnDefinition } from "./constants";

interface ActionOptions {
  edit?: boolean;
  delete?: boolean;
  custom?: boolean;
}

const props = withDefaults(
  defineProps<{
    columns: ColumnDefinition<T>[];
    data?: T[];
    loading?: boolean;
    totalRecords?: number;
    paginator?: boolean;
    rows?: number;
    rowsPerPageOptions?: number[];
    selectionMode?: "single" | "multiple" | null;
    selectedItems?: T | T[];
    filters?: Record<string, any>;
    checkbox?: boolean;
    clickable?: boolean;
    showActions?: ActionOptions;
    customActionItems?: (data: T) => MenuItem[];
    title?: string;
    showHeader?: boolean;
    hideHeaderRow?: boolean;
    hideFilterRow?: boolean;
    size?: "small" | "large";
    fluid?: boolean;
    rowClass?: (data: T) => Record<string, boolean>;
    stripedRows?: boolean;
    scrollable?: boolean;
    scrollHeight?: string;
    autoHeight?: boolean;
    heightOffset?: number;
    resizableColumns?: boolean;
    columnResizeMode?: "fit" | "expand";
  }>(),
  {
    selectedItems: () => [],
    filters: () => ({}),
    checkbox: false,
    clickable: false,
    title: "",
    showHeader: true,
    hideHeaderRow: false,
    hideFilterRow: false,
    size: "large",
    fluid: false,
    rowClass: () => ({}),
    stripedRows: false,
    scrollable: false,
    scrollHeight: undefined,
    autoHeight: false,
    heightOffset: 80,
    resizableColumns: false,
    columnResizeMode: "fit",
    customActionItems: () => [],
  },
);

const emit = defineEmits<{
  (event: "update:selectedItems", data: T | T[]): void;
  (event: "update:filters", data: Record<string, string>): void;
  (event: "page", data: { first: number; rows: number }): void;
  (event: "row-click", data: DataTableRowClickEvent): void;
  (event: "on-edit", data: T): void;
  (event: "on-delete", data: T, e?: MouseEvent): void;
}>();

const computedFilters = computed({
  get: () => props.filters || {},
  set: (value: Record<string, string>) => emit("update:filters", value),
});

const computedSelection = computed({
  get: () => props.selectedItems,
  set: (value: T | T[]) => emit("update:selectedItems", value),
});

const showPaginator = computed(() => (props.paginator ?? false) && !!props.totalRecords);

const onRowClick = (event: DataTableRowClickEvent) => {
  if (props.clickable) {
    const { data } = event;
    emit("row-click", data);
  }
};

const onEdit = (data: T) => {
  emit("on-edit", data);
};

const onDelete = (data: T, event?: MouseEvent) => {
  emit("on-delete", data, event);
};

const trimmedFilterValue = (value: string | undefined | null): string => {
  return typeof value === "string" ? value.trim() : "";
};

// Create a single debounced function for all filter updates
const debouncedFilterCallback = debounce((callback: Function) => {
  callback();
}, 500);

const updateFilterValue = (
  value: string | undefined,
  filterModel: any,
  filterCallback: Function,
) => {
  // Update the model value immediately
  if (filterModel) {
    filterModel.value = value?.trim() || "";
  }

  // Debounce the actual filter callback
  debouncedFilterCallback(filterCallback);
};

// Menu references
const menuRefs = ref<Map<any, any>>(new Map());

// Set menu reference
const setMenuRef = (el: any, id: any) => {
  if (el) {
    menuRefs.value.set(id, el);
  }
};

// Toggle menu
const toggleMenu = (event: Event, data: T) => {
  const id = (data as any).id;
  const menuRef = menuRefs.value.get(id);
  if (menuRef) {
    menuRef.toggle(event);
  }
  event.stopPropagation();
};

// Get menu items based on data
const getMenuItems = (data: T) => {
  const items: MenuItem[] = [];

  // Add custom items first (or last, depending on preference)
  const customItems = props.customActionItems(data);
  if (customItems && customItems.length > 0) {
    items.push(...customItems);
  }

  // Default Edit action
  if (props.showActions?.edit) {
    items.push({
      label: "Sửa",
      icon: "pi pi-pencil",
      command: () => onEdit(data),
    });
  }
  // Default Delete action
  if (props.showActions?.delete) {
    items.push({
      separator: true,
    });
    items.push({
      label: "Xóa",
      icon: "pi pi-trash",
      severity: "danger",
      command: (event: any) => onDelete(data, event.originalEvent as MouseEvent),
    });
  }

  return items.length > 0 ? [{ label: "Actions", items: items }] : [];
};

// Add fullscreen store
const fullscreenStore = useFullscreenStore();

// Update the calculatedScrollHeight computed property
const calculatedScrollHeight = computed(() => {
  if (props.scrollHeight) {
    return props.scrollHeight;
  }
  if (props.autoHeight) {
    // Use dynamic calculation based on fullscreen state
    return fullscreenStore.isFullScreen ? "calc(100vh - 185px)" : "calc(100vh - 350px)";
  }
  return undefined;
});

// Internal state for pagination display
const currentPageFirstRecord = ref(0);

// Calculate text for paginator start
const paginatorStartText = computed(() => {
  if (!props.totalRecords) {
    return "0 items";
  }
  const first = currentPageFirstRecord.value + 1;
  const last = Math.min(currentPageFirstRecord.value + (props.rows ?? 10), props.totalRecords);
  return `Hiển thị ${first} - ${last} của ${props.totalRecords} mục`;
});
// Internal handler for page change
const onInternalPageChange = (event: { first: number; rows: number }) => {
  currentPageFirstRecord.value = event.first;
  emit("page", event); // Still emit the original event
};

// Cleanup on unmount
onUnmounted(() => {
  // Cancel debounced function to prevent memory leaks
  debouncedFilterCallback.cancel();
  
  // Clear menu refs map
  menuRefs.value.clear();
});
</script>
