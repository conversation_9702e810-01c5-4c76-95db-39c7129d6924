import type { SuggestionProps } from '@tiptap/suggestion'
import { <PERSON>ue<PERSON>enderer } from '@tiptap/vue-3'
import tippy, { GetReferenceClientRect, Instance } from 'tippy.js'

import type { PersonResponse } from '@/api/bcare-types-v2'
import use<PERSON>erson from '@/hooks/usePerson'

import MentionPersonList from './MentionPersonList.vue'

export function createPersonSuggestion() {
  const { listPersons } = usePerson()
  
  // Single AbortController to manage ongoing requests
  let abortController: AbortController | null = null

  return {
    char: '#',
    // Allow spaces in mention queries for multi-word search
    allowSpaces: true,
    // Add debounce to prevent firing on every keystroke
    debounce: 500,
    // Use items function for all async logic - this fixes the race condition
    items: async ({ query }: { query: string }): Promise<PersonResponse[]> => {
      // Cancel previous request if still running
      if (abortController) {
        abortController.abort()
      }
      abortController = new AbortController()

      // Normalize query: trim and handle multiple spaces
      const normalizedQuery = query.trim().replace(/\s+/g, ' ')
      
      if (!normalizedQuery) {
        return []
      }

      try {
        const result = await listPersons({ 
          search: normalizedQuery, 
          page: 1, 
          page_size: 20 
        })
        
        // Check if request was aborted after completion
        if (abortController.signal.aborted) {
          return []
        }
        
        // Handle null result - listPersons returns PersonListResponse | null
        if (!result) {
          return []
        }
        
        return result.persons || []
      } catch (error) {
        console.error('Error searching persons:', error)
        return []
      }
    },

    render: () => {
      let component: VueRenderer | null = null
      let popup: Instance | null = null

      const getClientRect = (props: SuggestionProps): DOMRect | null => {
        return typeof props.clientRect === 'function' ? props.clientRect() : null
      }

      const createReferenceRect = (props: SuggestionProps): GetReferenceClientRect => {
        return () => getClientRect(props) || new DOMRect()
      }

      const createTippyInstance = (props: SuggestionProps) => {
        const rect = getClientRect(props)
        if (!rect || !component?.element) return null

        return tippy('body', {
          getReferenceClientRect: createReferenceRect(props),
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
          theme: 'light',
          arrow: false,
          maxWidth: 'none',
          offset: [0, 10],
          zIndex: 9999,
          animation: 'fade',
          duration: 150,
          popperOptions: {
            strategy: 'fixed',
            modifiers: [
              {
                name: 'flip',
                options: {
                  fallbackPlacements: ['top-start', 'bottom-end', 'top-end'],
                },
              },
            ],
          },
        })[0]
      }

      return {
        onStart: (props: SuggestionProps) => {
          component = new VueRenderer(MentionPersonList, {
            props: {
              ...props,
              // TipTap now manages the items array directly
              items: props.items || [],
              query: props.query || '',
            },
            editor: props.editor,
          })
          popup = createTippyInstance(props)
        },

        onUpdate(props: SuggestionProps) {
          component?.updateProps({
            ...props,
            // Update items from TipTap's managed state
            items: props.items || [],
            query: props.query || '',
          })
          popup?.setProps({
            getReferenceClientRect: createReferenceRect(props),
          })
        },

        onKeyDown(props: { event: KeyboardEvent }) {
          if (!component || !popup) return false

          if (props.event.key === 'Escape') {
            popup.hide()
            return true
          }

          return component.ref?.onKeyDown(props) ?? false
        },

        onExit() {
          if (popup && !popup.state.isDestroyed) {
            popup.destroy()
            popup = null
          }

          if (component) {
            component.destroy()
            component = null
          }
        },

        onDestroy() {
          // Cleanup for memory leak prevention
          if (popup && !popup.state.isDestroyed) {
            popup.destroy()
            popup = null
          }

          if (component) {
            component.destroy()
            component = null
          }
        },
      }
    },
  }
}