import Mention from '@tiptap/extension-mention'
import type { MentionOptions } from '@tiptap/extension-mention'
import { PluginKey } from 'prosemirror-state'

// UserMention extension for @users
export const UserMention = Mention.extend({
  name: 'userMention',

  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {
        class: 'bg-warning/10 font-medium rounded-md text-warning px-1.5 py-0.5 box-decoration-clone',
      },
      suggestion: {
        ...this.parent?.().suggestion,
        char: '@',
        pluginKey: new PluginKey('userSuggestion'),
      },
    }
  },
})

// PersonMention extension for #persons
export const PersonMention = Mention.extend<MentionOptions>({
  name: 'personMention',

  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {
        class: 'bg-info/10 font-medium rounded-md text-info px-1.5 py-0.5 box-decoration-clone cursor-pointer hover:bg-info/20 transition-colors',
      },
      suggestion: {
        ...this.parent?.().suggestion,
        char: '#',
        pluginKey: new PluginKey('personSuggestion'),
      },
    }
  },

  renderHTML({ node, HTMLAttributes }) {
    const personId = node.attrs.id
    const href = `/customer/profile/${personId}`
    
    return [
      'a',
      {
        ...HTMLAttributes,
        href,
        target: '_blank',
        rel: 'noopener noreferrer',
        'data-type': this.name,
        'data-id': node.attrs.id,
        'data-label': node.attrs.label,
        class: 'bg-info/10 font-medium rounded-md text-info px-1.5 py-0.5 box-decoration-clone cursor-pointer hover:bg-info/20 transition-colors',
      },
      `#${node.attrs.label}`,
    ]
  },
})