<template>
  <div ref="editorContainer" class="relative overflow-hidden rounded-lg border bg-white">
    <!-- Template Manager Icon (only visible when Ctrl key is pressed) -->
    <div v-if="isCtrlPressed" class="animate-fade-in absolute right-2 top-2 z-10">
      <i
        class="pi pi-wrench mr-3 cursor-pointer text-lg text-gray-300 transition-colors hover:text-primary"
        @click="showTemplateManager = true"
        title="Template Manager"
      />
    </div>

    <bubble-menu
      v-if="editor"
      :editor="editor"
      :tippy-options="{
        duration: 100,
        zIndex: 9999,
        appendTo: getDocumentBody,
        placement: 'top',
        theme: 'bubble-menu',
      }"
      class="flex gap-1 rounded-lg border border-gray-200 bg-white p-1 shadow-lg"
    >
      <button
        :class="[buttonClass, { [buttonActiveClass]: editor.isActive('bold') }]"
        @click="editor.chain().focus().toggleBold().run()"
      >
        <svg
          class="size-4"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8" />
        </svg>
      </button>

      <button
        :class="[buttonClass, { [buttonActiveClass]: editor.isActive('italic') }]"
        @click="editor.chain().focus().toggleItalic().run()"
      >
        <svg
          class="size-4"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <line x1="19" x2="10" y1="4" y2="4" />
          <line x1="14" x2="5" y1="20" y2="20" />
          <line x1="15" x2="9" y1="4" y2="20" />
        </svg>
      </button>

      <button
        :class="[buttonClass, { [buttonActiveClass]: editor.isActive('bulletList') }]"
        @click="editor.chain().focus().toggleBulletList().run()"
      >
        <svg
          class="size-4"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M3 12h.01" />
          <path d="M3 18h.01" />
          <path d="M3 6h.01" />
          <path d="M8 12h13" />
          <path d="M8 18h13" />
          <path d="M8 6h13" />
        </svg>
      </button>

      <button
        :class="[buttonClass, { [buttonActiveClass]: editor.isActive('orderedList') }]"
        @click="editor.chain().focus().toggleOrderedList().run()"
      >
        <svg
          class="size-4"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M10 12h11" />
          <path d="M10 18h11" />
          <path d="M10 6h11" />
          <path d="M4 10h2" />
          <path d="M4 6h1v4" />
          <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1" />
        </svg>
      </button>
    </bubble-menu>

    <editor-content
      :editor="editor"
      class="prose max-w-none p-2 px-2.5 text-sm prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:max-w-1/2 prose-img:rounded-md"
    />

    <!-- Upload Progress -->
    <div v-if="isUploading" class="absolute inset-0 flex items-center justify-center bg-black/10">
      <div class="rounded bg-white p-4 shadow-lg">
        <div class="mb-2 text-sm">Đang tải lên... {{ progress }}%</div>
        <div class="h-2 w-48 rounded-full bg-gray-200">
          <div
            :style="{ width: `${progress}%` }"
            class="h-full rounded-full bg-blue-500 transition-all duration-300"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Template Manager Dialog -->
  <TemplateManager v-model:visible="showTemplateManager" />
</template>

<script lang="ts" setup>
import Image from "@tiptap/extension-image";
import StarterKit from "@tiptap/starter-kit";
import { BubbleMenu, EditorContent, useEditor } from "@tiptap/vue-3";
import { FileHandler } from "@tiptap-pro/extension-file-handler";
import { onClickOutside } from "@vueuse/core";
import { computed, defineAsyncComponent, onBeforeUnmount, onMounted, ref, watch } from "vue";

import { UserMention, PersonMention } from "@/components/WysiwgEditor/mention-extensions";
import { createUserSuggestion } from "@/components/WysiwgEditor/userSuggestion";
import { createPersonSuggestion } from "@/components/WysiwgEditor/personSuggestion";
import { createTemplatePlugin } from "@/components/WysiwgEditor/template";
import { useUpload } from "@/hooks/useUpload";
import { useAuthStore } from "@/stores/auth-store";
import { getLocalResourceUri } from "@/utils/helper";

// Lazy load TemplateManager
const TemplateManager = defineAsyncComponent(
  () => import("@/components/WysiwgEditor/TemplateManager.vue"),
);

interface Props {
  modelValue: string;
  autofocus?: boolean;
  placeholder?: string;
  entityId?: number;
  entityType?: string;
  usageType?: string;
  readonly?: boolean;
  minHeight?: string;
}

const props = withDefaults(defineProps<Props>(), {
  autofocus: false,
  placeholder: "Nhập nội dung...",
  readonly: false,
  entityId: undefined,
  minHeight: "60px",
});

// Template manager state
const showTemplateManager = ref(false);
const isCtrlPressed = ref(false);

// Track Ctrl key state
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === "Control") {
    isCtrlPressed.value = true;
  }
};

const handleKeyUp = (e: KeyboardEvent) => {
  if (e.key === "Control") {
    isCtrlPressed.value = false;
  }
};

// Add and remove event listeners
onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
  window.addEventListener("keyup", handleKeyUp);
});

onBeforeUnmount(() => {
  window.removeEventListener("keydown", handleKeyDown);
  window.removeEventListener("keyup", handleKeyUp);
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "blur"): void;
  (e: "ready"): void;
}>();

const { currentUser } = useAuthStore();
const effectiveEntityId = computed(() => props.entityId ?? currentUser?.id);

const buttonBaseClass =
  "p-1 rounded focus:outline-none size-6 cursor-pointer hover:bg-gray-100 text-gray-500";
const buttonActiveClass = "bg-gray-200 text-gray-900";
const buttonClass = buttonBaseClass;

const editorContainer = ref<HTMLElement | null>(null);

const { uploadFile, isUploading, progress } = useUpload({
  maxFileSize: 5,
  allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  entity: effectiveEntityId.value
    ? {
        id: effectiveEntityId.value,
        type: props.entityType || "image",
        usageType: props.usageType || "note",
      }
    : undefined,
});

const handleFiles = async (files: File[], editor: any) => {
  for (const file of files) {
    try {
      const response = await uploadFile(file);
      // Assuming response contains the FileUsageResponse
      const imageUrl = getLocalResourceUri(response);
      editor.chain().focus().setImage({ src: imageUrl }).run();
    } catch (error) {
      console.error("Error handling file:", error);
    }
  }
};

// Create the template plugin
const templatePlugin = createTemplatePlugin();
console.log("Template plugin created:", templatePlugin);

const editor = useEditor({
  extensions: [
    StarterKit,
    Image,
    UserMention.configure({
      suggestion: createUserSuggestion(),
    }),
    PersonMention.configure({
      suggestion: createPersonSuggestion(),
    }),
    templatePlugin,
    FileHandler.configure({
      allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
      onPaste: async (editor, files) => {
        await handleFiles(files, editor);
      },
      onDrop: async (editor, files) => {
        await handleFiles(files, editor);
      },
    }),
  ],
  content: props.modelValue,
  autofocus: props.autofocus,
  editable: !props.readonly,
  onUpdate: ({ editor }) => {
    emit("update:modelValue", editor.getHTML());
  },
});

const isPopupElement = (el: Element) => {
  const popupSelectors = [".tippy-box", ".mention-suggestion", ".mention-person-list", ".template-list"];
  return popupSelectors.some((selector) => el.matches(selector) || el.closest(selector) !== null);
};

onClickOutside(editorContainer, (event) => {
  const targetElement = event.target as Element;
  if (isPopupElement(targetElement)) return;
  emit("blur");
});

watch(
  () => props.modelValue,
  (value) => {
    const isSame = editor.value?.getHTML() === value;
    if (isSame) return;
    editor.value?.commands.setContent(value, false);
  },
);

onBeforeUnmount(() => {
  editor.value?.destroy();
});

const getDocumentBody = () => document.body;
</script>

<style>
.tiptap img.ProseMirror-selectednode {
  outline: 2px solid rgb(var(--color-warning));
  outline-offset: 2px;
}

.ProseMirror:focus {
  outline: none;
}

.ProseMirror {
  min-height: v-bind(minHeight);
}

/* Animation for template manager icon */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

/* Bubble menu styles */
.tippy-box[data-theme~="bubble-menu"] {
  z-index: 9999 !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.tippy-box[data-theme~="bubble-menu"] .tippy-arrow {
  display: none !important;
}

.tippy-box[data-theme~="bubble-menu"] .tippy-content {
  background-color: transparent !important;
  padding: 0 !important;
}

.bubble-menu button {
  color: inherit;
}
</style>
