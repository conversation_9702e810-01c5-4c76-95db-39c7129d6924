<template>
  <div class="group relative">
    <div
      v-if="!isEditing"
      :class="[
        'flex items-center rounded-lg transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700',
        { 'border dark:border-gray-700': displayValue },
      ]"
    >
      <div
        v-if="displayValue"
        ref="contentRef"
        :class="[
          'prose relative w-full max-w-none p-2 px-2.5 prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:max-w-1/2 prose-img:rounded-md',
          sizeClasses
        ]"
      ></div>
      <div v-else class="flex items-center text-gray-500" @click="startEditing">
        <div
          class="flex h-[30px] w-[30px] items-center justify-center rounded-full border border-dashed border-gray-300 text-xs font-medium"
        >
          <i class="pi pi-pen-to-square text-gray-400" />
        </div>
        <span :class="['ml-2 text-gray-400', sizeClasses]">{{ props.placeholder }}</span>
      </div>

      <!-- Edit button -->
      <button
        v-if="displayValue"
        class="absolute right-2 top-2 flex aspect-square size-8 items-center justify-center rounded-full opacity-0 shadow transition-opacity duration-200 hover:bg-gray-100 group-hover:opacity-100 dark:hover:bg-gray-600"
        @click="startEditing"
      >
        <i class="pi pi-pencil text-gray-500" />
      </button>
    </div>

    <div v-else>
      <Tiptap 
        v-model="editedNote" 
        :autofocus="true" 
        @blur="props.alwaysEditing ? undefined : saveNote"
        @update:modelValue="props.alwaysEditing ? emit('update:modelValue', $event) : undefined"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useVModel } from "@vueuse/core";
import { computed, defineAsyncComponent, nextTick, onMounted, ref, watch } from "vue";

import { useDynamicLightbox } from "@/composables/useDynamicLightbox";

const Tiptap = defineAsyncComponent(() => import("@/components/WysiwgEditor/Tiptap.vue"));

const isEmptyNote = (note: string) => {
  return note === "" || note === "<p></p>" || note === "<p><br></p>";
};

const props = withDefaults(
  defineProps<{
    modelValue: string;
    placeholder?: string;
    alwaysEditing?: boolean;
    size?: 'xs' | 'sm' | 'base' | 'lg';
    class?: string;
  }>(),
  {
    placeholder: "Thêm nội dung",
    alwaysEditing: false,
    size: 'sm',
    class: '',
  },
);

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "save", value: string): void;
}>();

const isEditing = ref(props.alwaysEditing);
const editedNote = useVModel(props, "modelValue", emit);
const contentRef = ref<HTMLElement | null>(null);

const { initializeLightbox } = useDynamicLightbox(contentRef);

const displayValue = computed(() => {
  return editedNote.value && !isEmptyNote(editedNote.value) ? editedNote.value : "";
});

const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'text-xs',
    sm: 'text-sm', 
    base: 'text-base',
    lg: 'text-lg'
  };
  return `${sizeMap[props.size]} ${props.class}`.trim();
});

const updateContent = async () => {
  if (contentRef.value && displayValue.value) {
    // Xử lý nội dung để giữ lại các dòng trống
    // Xử lý trường hợp có khoảng trắng
    contentRef.value.innerHTML = displayValue.value
      .replace(/<p><\/p>/g, "<p><br></p>") // Thay thế <p></p> bằng <p><br></p>
      .replace(/<p>\s*<\/p>/g, "<p><br></p>");
    await nextTick();
    initializeLightbox();
  }
};

const startEditing = () => {
  isEditing.value = true;
};

const saveNote = async () => {
  if (!props.alwaysEditing) {
    isEditing.value = false;
  }
  const trimmedNote = editedNote.value.trim();
  const finalNote = isEmptyNote(trimmedNote) ? "" : trimmedNote;
  emit("save", finalNote);
  emit("update:modelValue", finalNote);

  await nextTick();
  updateContent();
};

// Watchers and lifecycle hooks
watch(() => displayValue.value, updateContent, { immediate: true });

onMounted(() => {
  updateContent();
});
</script>
