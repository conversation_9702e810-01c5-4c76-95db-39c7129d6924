<template>
  <div class="mention-person-list max-h-[300px] min-w-[280px] overflow-y-auto rounded-lg border-0 bg-white p-0 shadow-xl">
    <!-- Results List -->
    <template v-if="items.length">
      <div
        v-for="(item, index) in itemsList"
        :key="item.id"
        :class="[
          'flex w-full items-center gap-3 px-3 py-2 text-left border-b border-gray-100 transition-colors duration-150 hover:bg-slate-50 cursor-pointer',
          { 'bg-slate-100': index === selectedIndex }
        ]"
        @click="selectItem(index)"
      >
        <PersonAvatar :person="item" :size="28" shape="circle" class="flex-shrink-0" />
        <div class="flex flex-col min-w-0 flex-1">
          <div class="flex items-center gap-2">
            <span class="font-medium text-gray-800 truncate text-sm">
              <HighlightText :highlight="currentQuery || ''" :text="item.full_name" />
            </span>
            <span v-if="item.phone" class="text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">
              {{ item.phone }}
            </span>
          </div>
          <div v-if="item.email" class="text-xs text-gray-500 truncate">
            <HighlightText :highlight="currentQuery || ''" :text="item.email" />
          </div>
        </div>
      </div>
    </template>

    <!-- No Results -->
    <div v-else-if="showNoResults" class="px-3 py-2 text-sm text-gray-500 flex items-center justify-center border-t">
      Không tìm thấy kết quả
    </div>

    <!-- Initial State -->
    <div v-else-if="showInitial" class="px-3 py-2 text-sm text-gray-500 flex items-center justify-center border-t">
      Nhập tên để tìm kiếm khách hàng
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

import type { PersonResponse } from '@/api/bcare-types-v2'
import HighlightText from '@/base-components/HighlightText.vue'
import PersonAvatar from '@/components/Person/PersonAvatar.vue'

interface Props {
  items: PersonResponse[]
  command: (params: { id: string, label: string }) => void
  query?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['select'])

const selectedIndex = ref(0)

// Computed properties for UI states - using plain props
const showNoResults = computed(() => {
  return props.query?.trim() && props.items.length === 0
})

const showInitial = computed(() => {
  return !props.query?.trim()
})

const itemsList = computed(() => props.items)
const currentQuery = computed(() => props.query)

// Watch items for selection reset only
watch(() => props.items, () => {
  selectedIndex.value = 0
})

const selectItem = (index: number) => {
  const items = props.items
  const item = items[index]
  if (item) {
    props.command({ id: item.id.toString(), label: item.full_name })
    emit('select', item)
  }
}

const onKeyDown = ({ event }: { event: KeyboardEvent }) => {
  const items = props.items
  if (event.key === 'ArrowUp') {
    selectedIndex.value = ((selectedIndex.value + items.length) - 1) % items.length
    return true
  }

  if (event.key === 'ArrowDown') {
    selectedIndex.value = (selectedIndex.value + 1) % items.length
    return true
  }

  if (event.key === 'Enter') {
    selectItem(selectedIndex.value)
    return true
  }

  return false
}

defineExpose({
  onKeyDown
})
</script>