<script setup lang="ts">
import But<PERSON> from "primevue/button";
import { computed, ref } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import { usePermissions } from "@/composables/usePermissions";
import { useAuthStore } from "@/stores/auth-store";

const props = withDefaults(
  defineProps<{
    state?: TaskStateEnum | string;
    size?: "small" | "normal" | "large";
    customText?: string;
    showDot?: boolean;
    editable?: boolean;
    useCustomTrigger?: boolean;
    taskOwnerId?: number;
    primaryAssigneeIds?: number[];
    isFilter?: boolean;
  }>(),
  {
    size: "normal",
    showDot: true,
    editable: false,
    useCustomTrigger: false,
    taskOwnerId: undefined,
    primaryAssigneeIds: () => [],
    isFilter: false,
  },
);

const emit = defineEmits<{
  (e: "update:state", value: TaskStateEnum): void;
  (e: "filter", value: TaskStateEnum | null): void;
}>();

const { canManageOwnResource, isDepartmentManager, onlyAdmin } = usePermissions();
const authStore = useAuthStore();

const stateMap = {
  [TaskStateEnum.NEW_TASK]: { class: "bg-yellow-500 text-white", text: "Mới tạo", shortText: "Mới" },
  [TaskStateEnum.IN_PROGRESS]: { class: "bg-green-500 text-white", text: "Đang thực hiện", shortText: "Đang" },
  [TaskStateEnum.AWAITING_APPROVAL]: { class: "bg-purple-400 text-white", text: "Chờ phê duyệt", shortText: "Chờ" },
  [TaskStateEnum.COMPLETED]: { class: "bg-blue-500 text-white", text: "Hoàn thành", shortText: "Xong" },
  [TaskStateEnum.CANCELLED]: { class: "bg-red-500 text-white", text: "Đã hủy", shortText: "Hủy" },
  [TaskStateEnum.COMPLETED_EARLY]: { class: "bg-teal-500 text-white", text: "Hoàn thành sớm", shortText: "Xong sớm" },
};

const sizeMap = {
  small: { class: "px-2 py-0.5 text-xs", dotClass: "w-1.5 h-1.5" },
  large: { class: "text-base py-2 px-4", dotClass: "w-3 h-3" },
  normal: { class: "text-sm py-1 px-3", dotClass: "w-2 h-2" },
};

const stateInfo = computed(() => {
  const state = props.state as TaskStateEnum;
  return stateMap[state] ?? { class: "bg-gray-500 text-white", text: "Không xác định", shortText: "N/A" };
});

const sizeInfo = computed(() => sizeMap[props.size]);

const displayText = computed(() => props.customText ?? stateInfo.value.shortText);

const op = ref();

const hasEditPermission = computed(() => {
  if (onlyAdmin()) return true;

  if (isDepartmentManager()) return true;

  if (props.taskOwnerId && canManageOwnResource(props.taskOwnerId)) return true;

  const currentUserId = authStore.currentUser?.id;
  if (currentUserId && props.primaryAssigneeIds.includes(currentUserId)) return true;

  return false;
});

const isEditable = computed(() => props.editable && hasEditPermission.value);

const handleStateChange = (newState: TaskStateEnum) => {
  if (props.isFilter) {
    emit("filter", newState);
  } else if (isEditable.value) {
    emit("update:state", newState);
  }
  op.value.hide();
};

const clearFilter = () => {
  if (props.isFilter) {
    emit("filter", null);
    op.value.hide();
  }
};

const toggleDropdown = (event: Event) => {
  if (!isEditable.value) return;
  op.value.toggle(event);
};
</script>

<template>
  <div class="relative inline-block">
    <template v-if="useCustomTrigger">
      <slot name="trigger" :toggle="toggleDropdown" :disabled="!isEditable">
        <Button @click="toggleDropdown" :disabled="!isEditable">Custom Trigger</Button>
      </slot>
    </template>
    <template v-else-if="isFilter">
      <div
        @click="toggleDropdown"
        class="p-inputtext p-component flex !h-10 cursor-pointer items-center gap-2 !p-2.5 hover:border-[#94a3b8]"
      >
        <div class="flex items-center gap-2">
          <Chip
            v-if="state"
            :class="[stateInfo.class, 'inline-flex items-center rounded-full px-2 py-0.5 text-sm']"
          >
            <span
              v-if="showDot"
              :class="['mr-1 flex-shrink-0 rounded-full bg-white', sizeMap['small'].dotClass]"
            />
            <span class="whitespace-nowrap">{{ stateInfo.shortText }}</span>
          </Chip>
          <span v-else class="text-sm text-gray-400">{{ customText || "Chọn trạng thái" }}</span>
        </div>
        <i class="pi pi-chevron-down ml-auto text-xs text-gray-400" />
      </div>
    </template>
    <Chip
      v-else
      :class="[
        stateInfo.class,
        sizeInfo.class,
        'inline-flex items-center rounded-full',
        {
          'cursor-pointer': isEditable,
          'cursor-not-allowed': props.editable && !hasEditPermission,
        },
      ]"
      @click="toggleDropdown"
    >
      <span
        v-if="showDot"
        :class="['mr-1 flex-shrink-0 rounded-full bg-white', sizeInfo.dotClass]"
      />
      <span class="whitespace-nowrap">{{ displayText }}</span>
      <i v-if="isEditable" class="pi pi-chevron-down ml-1 text-xs"></i>
    </Chip>

    <Popover ref="op">
      <div class="text-sm font-medium">{{ props.isFilter ? "Chọn trạng thái" : "Trạng thái" }}</div>
      <ul class="m-0 list-none p-0">
        <li
          v-for="(info, value) in stateMap"
          :key="value"
          class="cursor-pointer rounded-md p-2 hover:bg-gray-100"
          @click="handleStateChange(value as TaskStateEnum)"
        >
          <Chip
            :class="[info.class, sizeMap['normal'].class, 'inline-flex items-center rounded-full']"
          >
            <span
              v-if="showDot"
              :class="['mr-1 flex-shrink-0 rounded-full bg-white', sizeMap['normal'].dotClass]"
            />
            <span class="whitespace-nowrap">{{ info.text }}</span>
          </Chip>
        </li>
      </ul>
    </Popover>
  </div>
</template>
