<script setup lang="ts">
import { TransitionRoot } from "@headlessui/vue";
import <PERSON>ie from "js-cookie";
import _ from "lodash";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import { UserResponse } from "@/api/bcare-types-v2";
import { COOKIE_ENUM } from "@/enums/cookie-enum";
import useDepartment from "@/hooks/useDepartment";
import { useAuth } from "@/pages/auth/config/auth-composable";
import { toObject } from "@/utils/helper";
import Breadcrumb from "../../base-components/Breadcrumb";
import { FormInput } from "../../base-components/Form";
import { Menu, Popover } from "../../base-components/Headless";
import Lucide from "../../base-components/Lucide";
import config from "../../config/config";
import fakerData from "../../utils/faker";
import { UserAvatar } from "../User";
import { NotificationPopover } from "../Notification";

import logoUrl from "../../assets/images/logo.svg";

const props = withDefaults(
  defineProps<{
    layout?: "side-menu" | "simple-menu" | "top-menu" | "template-menu";
    visible?: boolean;
  }>(),
  {
    visible: true,
  },
);

const emit = defineEmits(["update:visible"]);

const localIsTopbarVisible = ref(props.visible);

watch(
  () => props.visible,
  (newValue) => {
    localIsTopbarVisible.value = newValue;
  },
);

let lastScrollPosition = 0;
const topThreshold = 50; // Ngưỡng để xác định đầu trang

const handleScroll = () => {
  const currentScrollPosition = window.scrollY || document.documentElement.scrollTop;

  // Kiểm tra xem người dùng đã cuộn đến đầu trang chưa
  const isAtTop = currentScrollPosition <= topThreshold;

  // Chỉ cập nhật trạng thái khi có sự thay đổi
  if (isAtTop !== localIsTopbarVisible.value) {
    localIsTopbarVisible.value = isAtTop;
    emit("update:visible", isAtTop);
  }

  lastScrollPosition = currentScrollPosition;
};

const { logout } = useAuth();
const router = useRouter();
const userItem = ref<UserResponse>();
const handleLogout = async () => {
  await logout();
  await router.push({ name: "login" });
};

const searchDropdown = ref(false);
const showSearchDropdown = () => {
  searchDropdown.value = true;
};
const hideSearchDropdown = () => {
  searchDropdown.value = false;
};
const route = useRoute();
const getBreadcrumb = computed(() => {
  const matched = route.matched.filter((match) => match.meta && match.meta.breadcrumb);
  const crumbs = matched.map((match) => {
    return {
      name: match.meta.breadcrumb,
      path: match.path,
    };
  });
  return crumbs;
});

const { getDepartmentNameById } = useDepartment();

const clearCache = () => {
  localStorage.clear();
  window.location.reload();
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  const getUser = Cookie.get(COOKIE_ENUM.USER);
  if (getUser) userItem.value = toObject(getUser);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<template>
  <div
    :class="[
      { '-translate-y-[132%]': !localIsTopbarVisible },
      'relative z-50 -mx-3 mt-12 h-[70px] border-b border-white/[0.08] px-3 transition-all duration-300 ease-in-out sm:-mx-8 sm:px-8 md:fixed md:inset-x-0 md:top-0 md:-mx-0 md:mt-0 md:h-[65px] md:border-b-0 md:bg-gradient-to-b md:from-slate-100 md:to-transparent md:px-10 md:pt-10 dark:md:from-darkmode-700',
      props.layout == 'top-menu' && 'dark:md:from-darkmode-800',
      'before:absolute before:inset-0 before:top-0 before:mx-7 before:mt-3 before:hidden before:h-[65px] before:rounded-xl before:bg-primary/30 before:content-[\'\'] before:dark:bg-darkmode-600/30 before:md:block',
      'after:absolute after:inset-0 after:mx-3 after:mt-5 after:hidden after:h-[65px] after:rounded-xl after:bg-primary after:shadow-md after:content-[\'\'] after:dark:bg-darkmode-600 after:md:block',
    ]"
  >
    <div class="flex h-full items-center">
      <!-- BEGIN: Logo -->
      <RouterLink
        :to="{ name: 'top-menu-dashboard-overview-1' }"
        :class="[
          '-intro-x hidden md:flex',
          props.layout == 'side-menu' && 'xl:w-[180px]',
          props.layout == 'simple-menu' && 'xl:w-auto',
          props.layout == 'top-menu' && 'w-auto',
          props.layout == 'template-menu' && 'w-auto',
        ]"
      >
        <img alt="Enigma Tailwind HTML Admin Template" class="w-6" :src="logoUrl" />
        <span
          :class="[
            'ml-3 text-lg text-white',
            props.layout == 'side-menu' && 'hidden xl:block',
            props.layout == 'simple-menu' && 'hidden',
          ]"
        >
          {{ config.appLogo }}
        </span>
      </RouterLink>
      <!-- END: Logo -->
      <!-- BEGIN: Breadcrumb -->
      <Breadcrumb
        light
        :class="[
          '-intro-x mr-auto h-[45px] border-white/[0.08] dark:border-white/[0.08] md:ml-10 md:border-l',
          '-intro-x mr-auto h-[45px] border-white/[0.08] dark:border-white/[0.08] md:ml-10 md:border-l',
          props.layout != 'top-menu' && 'md:pl-6',
          props.layout == 'top-menu' && 'md:pl-10',
        ]"
      >
        <Breadcrumb.Link to="/">Trang chủ</Breadcrumb.Link>
        <template v-for="(crumb, index) in getBreadcrumb" :key="index">
          <Breadcrumb.Link
            class="relative ml-5 pl-0.5 text-white/70 before:absolute before:inset-y-0 before:my-auto before:-ml-[1.125rem] before:h-[14px] before:w-[14px] before:bg-bredcrumb-chevron-light before:bg-[length:100%] before:content-[''] dark:before:bg-bredcrumb-chevron-darkmode"
            :to="crumb.path"
            :active="index === getBreadcrumb.length - 1"
          >
            {{ crumb.name }}
          </Breadcrumb.Link>
        </template>
      </Breadcrumb>
      <!-- END: Breadcrumb -->
      <!-- BEGIN: Search -->
      <div class="intro-x relative mr-3 sm:mr-6">
        <div class="relative hidden sm:block">
          <FormInput
            type="text"
            class="w-56 rounded-full border-transparent bg-slate-200 pr-8 shadow-none transition-[width] duration-300 ease-in-out focus:w-72 focus:border-transparent dark:bg-darkmode-400"
            placeholder="Search..."
            @focus="showSearchDropdown"
            @blur="hideSearchDropdown"
          />
          <Lucide
            icon="Search"
            class="absolute inset-y-0 right-0 my-auto mr-3 h-5 w-5 text-slate-600 dark:text-slate-500"
          />
        </div>
        <a class="relative text-white/70 sm:hidden" href="">
          <Lucide icon="Search" class="h-5 w-5 dark:text-slate-500" />
        </a>
        <TransitionRoot
          as="template"
          :show="searchDropdown"
          enter="transition-all ease-linear duration-150"
          enter-from="mt-5 invisible opacity-0 translate-y-1"
          enter-to="mt-[3px] visible opacity-100 translate-y-0"
          entered="mt-[3px]"
          leave="transition-all ease-linear duration-150"
          leave-from="mt-[3px] visible opacity-100 translate-y-0"
          leave-to="mt-5 invisible opacity-0 translate-y-1"
        >
          <div class="absolute right-0 z-10 mt-[3px]">
            <div class="box w-[450px] p-5">
              <div class="mb-2 font-medium">Pages</div>
              <div class="mb-5">
                <a href="" class="flex items-center">
                  <div
                    class="flex h-8 w-8 items-center justify-center rounded-full bg-success/20 text-success dark:bg-success/10"
                  >
                    <Lucide icon="Inbox" class="h-4 w-4" />
                  </div>
                  <div class="ml-3">Mail Settings</div>
                </a>
                <a href="" class="mt-2 flex items-center">
                  <div
                    class="flex h-8 w-8 items-center justify-center rounded-full bg-pending/10 text-pending"
                  >
                    <Lucide icon="Users" class="h-4 w-4" />
                  </div>
                  <div class="ml-3">Users & Permissions</div>
                </a>
                <a href="" class="mt-2 flex items-center">
                  <div
                    class="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary/80 dark:bg-primary/20"
                  >
                    <Lucide icon="CreditCard" class="h-4 w-4" />
                  </div>
                  <div class="ml-3">Transactions Report</div>
                </a>
              </div>
              <div class="mb-2 font-medium">Users</div>
              <div class="mb-5">
                <a
                  v-for="(faker, fakerKey) in _.take(fakerData, 4)"
                  :key="fakerKey"
                  href=""
                  class="mt-2 flex items-center"
                >
                  <div class="image-fit h-8 w-8">
                    <img
                      alt="Midone Tailwind HTML Admin Template"
                      class="rounded-full"
                      :src="faker.photos[0]"
                    />
                  </div>
                  <div class="ml-3">{{ faker.users[0].name }}</div>
                  <div class="ml-auto w-48 truncate text-right text-xs text-slate-500">
                    {{ faker.users[0].email }}
                  </div>
                </a>
              </div>
              <div class="mb-2 font-medium">Products</div>
              <a
                v-for="(faker, fakerKey) in _.take(fakerData, 4)"
                :key="fakerKey"
                href=""
                class="mt-2 flex items-center"
              >
                <div class="image-fit h-8 w-8">
                  <img
                    alt="Midone Tailwind HTML Admin Template"
                    class="rounded-full"
                    :src="faker.images[0]"
                  />
                </div>
                <div class="ml-3">{{ faker.products[0].name }}</div>
                <div class="ml-auto w-48 truncate text-right text-xs text-slate-500">
                  {{ faker.products[0].category }}
                </div>
              </a>
            </div>
          </div>
        </TransitionRoot>
      </div>
      <!-- END: Search -->
      <!-- BEGIN: Notifications -->
      <NotificationPopover class="relative" />
      <!-- END: Notifications -->
      <!-- BEGIN: Account Menu -->
      <Menu>
        <Menu.Button class="intro-x flex h-8 w-8 items-center justify-center">
          <UserAvatar :user="userItem" />
        </Menu.Button>
        <Menu.Items
          class="relative mt-px w-56 bg-primary/80 text-white before:absolute before:inset-0 before:z-[-1] before:block before:rounded-md before:bg-black"
        >
          <Menu.Header class="font-normal">
            <div class="flex items-center gap-2">
              <div class="font-medium">{{ userItem?.name ?? "" }}</div>
              <div class="mt-0.5 text-xs text-white/70 dark:text-slate-500">
                @{{ userItem?.username ?? "" }}
              </div>
            </div>
            <div class="mt-0.5 text-xs text-white/70 dark:text-slate-500">
              {{ userItem?.department_id ? getDepartmentNameById(userItem.department_id) : "" }}
            </div>
          </Menu.Header>
          <Menu.Divider class="bg-white/[0.08]" />
          <Menu.Item
            class="hover:bg-white/5"
            @click="
              router.push({
                name: 'top-menu-dashboard-user',
              })
            "
          >
            <Lucide icon="User" class="mr-2 h-4 w-4" /> Hồ sơ
          </Menu.Item>
          <Menu.Item
            class="hover:bg-white/5"
            @click="
              router.push({
                name: 'top-menu-user-update',
                params: { id: userItem?.id ?? 0 },
              })
            "
          >
            <Lucide icon="Lock" class="mr-2 h-4 w-4" /> Đổi mật khẩu
          </Menu.Item>
          <Menu.Item class="hover:bg-white/5">
            <Lucide icon="HelpCircle" class="mr-2 h-4 w-4" /> Hướng dẫn
          </Menu.Item>
          <Menu.Item class="hover:bg-white/5" @click="clearCache">
            <Lucide icon="Trash2" class="mr-2 h-4 w-4" /> Clear cache
          </Menu.Item>
          <Menu.Divider class="bg-white/[0.08]" />
          <Menu.Item class="hover:bg-white/5" @click="handleLogout()">
            <Lucide icon="ToggleRight" class="mr-2 h-4 w-4" /> Đăng xuất
          </Menu.Item>
        </Menu.Items>
      </Menu>
      <!-- END: Account Menu -->
    </div>
  </div>
</template>
