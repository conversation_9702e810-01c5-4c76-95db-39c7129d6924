<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";

import { UniversalSetting } from "@/api/extend-types";
import Empty from "@/base-components/Empty";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useOperation from "@/hooks/useOperation";

interface Operation {
  id: number | string;
  name: string;
}

interface Props {
  productIds?: number[]; // Changed to an array for multiple product IDs
  single?: boolean; // Single selection mode
  placeholder?: string; // Placeholder text for single mode
  hasDefaultOpts?: boolean; // Enable default options functionality
}

const props = withDefaults(defineProps<Props>(), {
  productIds: () => [],
  single: false,
  placeholder: "Chọn công việc...",
  hasDefaultOpts: false,
});

// Use defineModel for two-way binding
const model = defineModel<Operation[] | number | string | null>({
  default: () => [],
});

const {
  operationsByProducts,
  loadOperations,
  filteredOperations,
  updateOperationFilter,
  selectedProductIds,
  getAllOperations,
} = useOperation({ autoLoad: true });

// Settings for default options
const settingName = "operation_select_default_opts";
const settingsSchema: UniversalSetting[] = [
  {
    type: "multi-number",
    label: "Mặc định",
    field_name: "operation_ids",
    value: [],
  },
];

const { currentSettingValue, refreshCurrentSetting } = useComponentSetting(settingName);

watch(
  () => props.productIds,
  (newProductIds) => {
    if (newProductIds) {
      selectedProductIds.value = newProductIds;
    }
  },
  { immediate: true },
);

// Computed properties for single mode
const selectedOperationId = computed(() => {
  if (!props.single) return null;
  return typeof model.value === "number" || typeof model.value === "string" ? model.value : null;
});

const selectedOperation = computed(() => {
  if (!props.single || !selectedOperationId.value) return null;
  return getAllOperations.value.find((op) => op.id === selectedOperationId.value) || null;
});

const multipleModelValue = computed(() => {
  if (props.single) return [];
  return Array.isArray(model.value) ? model.value : [];
});

const operationFilter = ref("");
const customOperations = ref<Operation[]>([]);

// Get default operation IDs from settings
const defaultIds = computed(() => currentSettingValue.value?.operation_ids ?? []);

const displayedOperations = computed(() => {
  const operations = props.single ? getAllOperations.value : operationsByProducts.value;

  let filteredOps = operations;

  if (operationFilter.value) {
    if (props.single) {
      const query = operationFilter.value.toLowerCase();
      filteredOps = operations.filter((operation) => operation.name.toLowerCase().includes(query));
    } else {
      updateOperationFilter(operationFilter.value);
      filteredOps = filteredOperations.value;
    }
  }

  if (!props.hasDefaultOpts || defaultIds.value.length === 0) {
    return filteredOps;
  }

  const allOps = getAllOperations.value;

  const defaultOps = allOps.filter((op) => defaultIds.value.includes(Number(op.id)));

  const filteredOpIds = new Set(filteredOps.map((op) => op.id));

  const additionalDefaultOps = defaultOps.filter((op) => !filteredOpIds.has(op.id));

  const defaultOpsInFiltered = filteredOps.filter((op) => defaultIds.value.includes(Number(op.id)));
  const otherOps = filteredOps.filter((op) => !defaultIds.value.includes(Number(op.id)));

  return [...defaultOpsInFiltered, ...additionalDefaultOps, ...otherOps];
});

const onFilterInput = () => {
  // Filter is handled by the computed property and updateOperationFilter
};

const toggleOperation = (operation: Operation) => {
  operationFilter.value = "";

  if (props.single) {
    model.value = operation.id;
    popoverRef.value?.hide();
  } else {
    const currentValue = multipleModelValue.value;
    const index = currentValue.findIndex((op) => op.id === operation.id);
    if (index > -1) {
      const newValue = [...currentValue];
      newValue.splice(index, 1);
      model.value = newValue;
    } else {
      model.value = [...currentValue, operation];
    }
  }
};

const clearSelection = () => {
  if (props.single) {
    model.value = null;
  }
};

const removeOperation = (operationId: number | string) => {
  if (props.single) {
    model.value = null;
  } else {
    const newValue = multipleModelValue.value.filter((op) => op.id !== operationId);
    model.value = newValue;
  }

  // Clear custom operation nếu bị remove
  customOperations.value = customOperations.value.filter((op) => op.id !== operationId);
};

const isOperationActive = (operationId: number | string) => {
  if (props.single) {
    return selectedOperationId.value === operationId;
  }
  return multipleModelValue.value.some((op) => op.id === operationId);
};

const clearFilter = () => {
  operationFilter.value = "";
};

const addCustomOperation = () => {
  if (
    operationFilter.value &&
    !customOperations.value.some((op) => op.name === operationFilter.value)
  ) {
    const newOperation = { id: operationFilter.value, name: operationFilter.value };
    customOperations.value.push(newOperation);

    if (props.single) {
      model.value = newOperation.id;
    } else {
      model.value = [...multipleModelValue.value, newOperation];
    }
    operationFilter.value = "";
  }
};

onMounted(async () => {
  await loadOperations();
});

const popoverRef = ref();

defineExpose({
  popoverRef,
});

// Thêm ref để track việc đã submit/remove
const isSubmitted = ref(false);

// Clear khi hide popover
const onHide = () => {
  clearFilter();
  if (isSubmitted.value) {
    customOperations.value = [];
    isSubmitted.value = false;
  }
};

// Thêm watch để detect khi modelValue empty (đã submit)
watch(
  () => model.value,
  (newVal) => {
    if (props.single) {
      if (!newVal) {
        isSubmitted.value = true;
      }
    } else {
      if (Array.isArray(newVal) && newVal.length === 0) {
        isSubmitted.value = true;
      }
    }
  },
  { deep: true },
);
</script>
<template>
  <!-- Single mode: PrimeVue Select-like trigger -->
  <div v-if="single" class="relative">
    <div
      class="flex w-full cursor-pointer items-center justify-between rounded-md border border-surface-300 bg-surface-0 p-2 text-sm transition-colors hover:border-surface-400 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
      @click="popoverRef?.toggle($event)"
    >
      <div class="flex flex-1 items-center">
        <span v-if="selectedOperation" class="hyphens-auto">{{ selectedOperation.name }}</span>
        <span v-else class="text-surface-500">{{ placeholder }}</span>
      </div>

      <div class="ml-2 flex items-center gap-1">
        <Button
          v-if="selectedOperation"
          icon="pi pi-times"
          variant="text"
          size="small"
          severity="secondary"
          class="size-5"
          rounded
          @click.stop="clearSelection"
        />
        <i class="pi pi-chevron-down text-surface-500"></i>
      </div>
    </div>

    <!-- Settings button for single mode -->
    <div v-if="props.hasDefaultOpts" class="absolute right-8 top-1/2 -translate-y-1/2">
      <DynamicSetting
        :settingName="settingName"
        :settingsSchema="settingsSchema"
        title="Cài đặt mặc định"
        @popover-close="refreshCurrentSetting"
      />
    </div>
  </div>

  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @hide="onHide()">
    <div class="relative w-[25rem]">
      <!-- Settings button for popover (multiple mode) -->
      <div v-if="props.hasDefaultOpts && !props.single" class="absolute right-2 top-2 z-10">
        <DynamicSetting
          :settingName="settingName"
          :settingsSchema="settingsSchema"
          title="Cài đặt mặc định"
          @popover-close="refreshCurrentSetting"
        />
      </div>

      <!-- Main content area with fixed height -->
      <div class="h-[15rem] overflow-hidden">
        <div v-if="displayedOperations.length > 0" class="h-full p-2">
          <ul
            class="m-0 h-full snap-y scroll-py-1 list-none space-y-1 overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="operation in displayedOperations"
              :key="operation.id"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isOperationActive(operation.id),
              }"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="toggleOperation(operation)"
            >
              <div class="flex flex-1 items-center gap-2">
                <!-- Pin icon for default operations -->

                <span class="hyphens-auto">
                  {{ operation.name }}
                </span>
                <i
                  v-if="props.hasDefaultOpts && defaultIds.includes(Number(operation.id))"
                  class="pi pi-thumbtack text-xs text-warning"
                ></i>
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="isOperationActive(operation.id)"
                  :class="{ 'bg-primary': isOperationActive(operation.id) }"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                ></div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="h-full p-2">
          <Empty class="h-full" />
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <div v-if="!single && multipleModelValue.length" class="mb-2 flex max-w-lg flex-wrap gap-1">
          <Chip
            v-for="operation in multipleModelValue"
            :key="operation.id"
            :label="operation.name"
            removable
            @remove="removeOperation(operation.id)"
          />
        </div>
        <div class="flex items-center gap-2">
          <IconField class="flex-grow">
            <InputText
              v-model="operationFilter"
              autofocus
              class="w-full text-sm"
              placeholder="Tìm kiếm"
              type="text"
              @input="onFilterInput"
              @keyup.enter="addCustomOperation"
            />
            <InputIcon
              :class="operationFilter ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="clearFilter"
            />
          </IconField>
          <Button
            class="text-sm"
            label="OK"
            @click="
              () => {
                addCustomOperation();
                popoverRef?.hide();
                clearFilter();
              }
            "
          />
        </div>
      </div>
    </div>
  </Popover>
</template>
