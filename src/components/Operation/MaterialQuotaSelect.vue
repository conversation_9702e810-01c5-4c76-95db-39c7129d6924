<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";

import Empty from "@/base-components/Empty";
import useMaterialQuota from "@/hooks/useMaterialQuota";
import useMaterial from "@/hooks/useMaterial";
import type { Material, OperationMaterial } from "@/api/bcare-types-v2";

// Extended interface for UI display with name
interface MaterialQuotaWithName extends OperationMaterial {
  name: string;
}

interface Props {
  operationId: number;
  modelValue: MaterialQuotaWithName[];
}

const props = defineProps<Props>();
const emit = defineEmits(["update:modelValue"]);

const { getMaterialQuotasForOperation, loadMaterialQuotasForOperation, isLoading } =
  useMaterialQuota({ autoLoad: false });

const { getMaterialNameById, materials: allMaterials } = useMaterial({ autoLoad: true });

const materialFilter = ref("");
const materialQuotas = ref<MaterialQuotaWithName[]>([]);
const selectedFilterType = ref("operation"); // "operation" or "all"

// Filter type options
const filterTypeOptions = [
  { name: "Vật tư cơ bản", value: "operation" },
  { name: "Thêm vật tư", value: "all" },
];

const displayedMaterials = computed(() => {
  let materials: MaterialQuotaWithName[] = [];

  if (selectedFilterType.value === "operation") {
    materials = materialQuotas.value;
  } else {
    materials = Object.values(allMaterials.value).map((material: Material) => ({
      id: material.id,
      operation_id: 0, // Default for new materials
      material_id: material.id,
      quantity: 1,
      status: material.status,
      version: material.version,
      created_at: material.created_at,
      updated_at: material.updated_at,
      deleted_at: material.deleted_at,
      name: material.name,
    }));
  }

  if (!materialFilter.value) return materials;

  const query = materialFilter.value.toLowerCase();
  return materials.filter((material) => material.name.toLowerCase().includes(query));
});

const onFilterInput = () => {};

const toggleMaterial = (material: MaterialQuotaWithName) => {
  materialFilter.value = "";

  const index = props.modelValue.findIndex((m) => m.id === material.id);
  if (index > -1) {
    const newValue = [...props.modelValue];
    newValue.splice(index, 1);
    emit("update:modelValue", newValue);
  } else {
    emit("update:modelValue", [...props.modelValue, material]);
  }
};

const removeMaterial = (materialId: number) => {
  const newValue = props.modelValue.filter((m) => m.id !== materialId);
  emit("update:modelValue", newValue);
};

const isMaterialActive = (materialId: number) => {
  return props.modelValue.some((m) => m.id === materialId);
};

const clearFilter = () => {
  materialFilter.value = "";
};

const onFilterTypeChange = () => {};

const loadMaterials = async () => {
  if (!props.operationId) {
    materialQuotas.value = [];
    return;
  }

  try {
    await loadMaterialQuotasForOperation(props.operationId);
    const materials = getMaterialQuotasForOperation(props.operationId);

    materialQuotas.value = materials.map((mq) => ({
      ...mq, // Spread all OperationMaterial properties
      name: getMaterialNameById(mq.material_id) || `Vật tư #${mq.material_id}`,
    }));
  } catch (error) {
    console.error("Error loading material quotas:", error);
    materialQuotas.value = [];
  }
};

watch(
  () => props.operationId,
  () => {
    loadMaterials();
  },
  { immediate: true },
);

const popoverRef = ref();

defineExpose({
  popoverRef,
});

const onHide = () => {
  clearFilter();
};
</script>

<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @hide="onHide()">
    <div class="w-[25rem]">
      <!-- Main content area with fixed height -->
      <div class="h-[15rem] overflow-hidden">
        <div v-if="displayedMaterials.length > 0" class="h-full p-2">
          <ul
            class="m-0 h-full snap-y scroll-py-1 list-none space-y-1 overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="material in displayedMaterials"
              :key="material.id"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isMaterialActive(material.id),
              }"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="toggleMaterial(material)"
            >
              <div class="flex-1">
                <div class="font-medium">{{ material.name }}</div>
                <div class="text-xs text-gray-500">Số lượng: {{ material.quantity }}</div>
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="isMaterialActive(material.id)"
                  :class="{ 'bg-primary': isMaterialActive(material.id) }"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                ></div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else-if="isLoading" class="flex h-full items-center justify-center p-2">
          <i class="pi pi-spin pi-spinner text-2xl text-gray-400"></i>
        </div>
        <div v-else class="h-full p-2">
          <Empty class="h-full" />
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <div v-if="modelValue.length" class="mb-2 flex max-w-lg flex-wrap gap-1">
          <Chip
            v-for="material in modelValue"
            :key="material.id"
            :label="`${material.name} (${material.quantity})`"
            removable
            @remove="removeMaterial(material.id)"
          />
        </div>
        <!-- Filter Type SelectButton -->
        <div class="mb-2">
          <SelectButton
            v-model="selectedFilterType"
            :options="filterTypeOptions"
            optionLabel="name"
            optionValue="value"
            :pt="{ root: { class: 'w-full grid grid-cols-2' } }"
            @change="onFilterTypeChange"
          />
        </div>

        <div class="flex items-center gap-2">
          <IconField class="flex-grow">
            <InputText
              v-model="materialFilter"
              autofocus
              class="w-full text-sm"
              placeholder="Tìm kiếm vật tư"
              type="text"
              @input="onFilterInput"
            />
            <InputIcon
              :class="materialFilter ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="clearFilter"
            />
          </IconField>
          <Button class="text-sm" label="OK" @click="popoverRef?.hide()" />
        </div>
      </div>
    </div>
  </Popover>
</template>
