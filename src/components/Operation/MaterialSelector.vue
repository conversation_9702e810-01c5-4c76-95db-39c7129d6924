<script lang="ts" setup>
import { computed, ref } from "vue";
import Empty from "@/base-components/Empty";
import useMaterial from "@/hooks/useMaterial";
import type { Material } from "@/api/bcare-types-v2";

interface Props {
  selectedMaterialIds?: number[]; // Materials already selected (to show as selected)
}

interface Emits {
  (e: "materialSelected", material: Material): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedMaterialIds: () => [],
});

const emit = defineEmits<Emits>();

// Use defineModel for v-model support (Vue 3.4+ best practice)
const model = defineModel<number[]>({
  default: () => [],
});

const { materials: allMaterials, isLoading, getMaterialById } = useMaterial({ autoLoad: true });

const materialFilter = ref("");

const availableMaterials = computed(() => {
  // Show ALL materials, don't exclude any - just mark as selected
  const materials = allMaterials.value;

  if (!materialFilter.value) return materials;

  const query = materialFilter.value.toLowerCase();
  return materials.filter((material) => 
    material.name.toLowerCase().includes(query) ||
    (material.code && material.code.toLowerCase().includes(query))
  );
});

const selectMaterial = (material: Material) => {
  materialFilter.value = "";
  
  // Toggle in model (for v-model sync)
  const current = model.value;
  const index = current.indexOf(material.id);
  
  if (index > -1) {
    // Remove from selection
    model.value = current.filter(id => id !== material.id);
  } else {
    // Add to selection
    model.value = [...current, material.id];
  }
  
  // Also emit for backward compatibility
  emit("materialSelected", material);
};

const isMaterialSelected = (materialId: number): boolean => {
  // Check BOTH model value AND prop (for comprehensive selection state)
  return model.value.includes(materialId) || props.selectedMaterialIds.includes(materialId);
};

const clearFilter = () => {
  materialFilter.value = "";
};

const popoverRef = ref();

defineExpose({
  popoverRef,
});

const onHide = () => {
  clearFilter();
};
</script>

<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @hide="onHide()">
    <div class="w-[20rem]">
      <!-- Main content area with fixed height -->
      <div class="h-[12rem] overflow-hidden">
        <div v-if="availableMaterials.length > 0" class="h-full p-2">
          <ul
            class="m-0 h-full snap-y scroll-py-1 list-none space-y-1 overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="material in availableMaterials"
              :key="material.id"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isMaterialSelected(material.id),
              }"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="selectMaterial(material)"
            >
              <div class="flex flex-1 items-center gap-2">
                <span class="hyphens-auto">{{ material.name }}</span>
                <div class="text-xs text-gray-500">#{{ material.id }}</div>
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="isMaterialSelected(material.id)"
                  :class="{ 'bg-primary': isMaterialSelected(material.id) }"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                ></div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else-if="isLoading" class="flex h-full items-center justify-center p-2">
          <i class="pi pi-spin pi-spinner text-2xl text-gray-400"></i>
        </div>
        <div v-else class="h-full p-2">
          <Empty class="h-full" />
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <div class="flex items-center gap-2">
          <IconField class="flex-grow">
            <InputText
              v-model="materialFilter"
              autofocus
              class="w-full text-sm"
              placeholder="Tìm kiếm vật tư để thêm"
              type="text"
            />
            <InputIcon
              :class="materialFilter ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="clearFilter"
            />
          </IconField>
          <Button class="text-sm" label="OK" @click="popoverRef?.hide()" />
        </div>
      </div>
    </div>
  </Popover>
</template>
