<template>
  <div class="relative w-full">
    <div
      ref="toggleContainer"
      @click="toggle"
      :class="[
        'cursor-pointer',
        !props.usePrimeVueInput
          ? 'flex w-full items-center rounded-md p-2 transition-colors duration-200 hover:bg-gray-100'
          : '',
        props.usePrimeVueInput
          ? 'p-inputtext p-component py-0 px-1 flex !h-10 hover:border-primary/50'
          : '',
      ]"
    >
      <div class="flex flex-wrap gap-1 items-center justify-center">
        <UserAvatar
          v-for="user in selectedUsers.slice(0, maxDisplay)"
          :key="user.id"
          :user="user"
          :size="avatarSize"
          v-if="selectedUsers.length > 0"
        />
        <div
          v-if="selectedUsers.length > maxDisplay"
          class="flex items-center justify-center rounded-full bg-gray-200 text-xs font-medium"
          :style="{
            width: `${avatarSize}px`,
            height: `${avatarSize}px`,
          }"
        >
          +{{ selectedUsers.length - maxDisplay }}
        </div>
      </div>

      <span v-if="selectedUsers.length === 0" class="flex items-center text-gray-500">
        <div
          class="flex items-center justify-center rounded-full border border-dashed border-gray-300 text-xs font-medium"
          :style="{
            width: `${avatarSize}px`,
            height: `${avatarSize}px`,
          }"
        >
          <i class="pi pi-user-plus text-gray-400" />
        </div>
        <span class="ml-2 overflow-hidden text-ellipsis whitespace-nowrap text-sm text-gray-400">{{
          placeholder
        }}</span>
      </span>
    </div>

    <Popover ref="popover">
      <div :style="{ width: `${width}px`, minWidth: `300px` }">
        <InputText
          ref="searchInput"
          v-model="searchQuery"
          placeholder="Tìm kiếm người dùng..."
          class="mb-2 w-full"
          size="small"
        />
        <div class="max-h-60 overflow-y-auto scroll-smooth">
          <div
            v-if="Object.keys(groupedUsers).length === 0"
            class="flex flex-col items-center justify-center py-8 text-center"
          >
            <i class="pi pi-search mb-2 text-2xl text-gray-400"></i>
            <p class="text-sm text-gray-500">
              {{
                searchQuery
                  ? `Không tìm thấy kết quả cho "${searchQuery}"`
                  : "Không có người dùng nào"
              }}
            </p>
            <p v-if="searchQuery" class="mt-1 text-xs text-gray-400">
              Vui lòng thử tìm kiếm với từ khóa khác
            </p>
          </div>

          <ul v-else class="m-0 list-none p-0">
            <li
              v-for="([departmentId, group], index) in groupedUsers"
              :key="departmentId"
              class="relative"
            >
              <div
                @click="toggleDepartmentSelection(Number(departmentId))"
                class="sticky top-0 z-10 flex cursor-pointer items-center justify-between bg-white/95 p-2 text-primary-500 shadow-sm backdrop-blur-sm hover:bg-gray-100"
              >
                <span class="font-medium">
                  {{ `${index + 1}. ${getDepartmentName(Number(departmentId))}` }}
                  <span class="ml-2 text-sm text-gray-500">
                    (<span
                      :class="{
                        'font-medium text-blue-500':
                          getSelectedCountInDepartment(Number(departmentId)) > 0,
                      }"
                    >
                      {{ getSelectedCountInDepartment(Number(departmentId)) }} </span
                    >/{{ group.length }})
                  </span>
                </span>
                <i :class="getDepartmentIconClass(Number(departmentId))" />
              </div>

              <ul class="mt-1 snap-y snap-mandatory pb-4">
                <li
                  v-for="user in group"
                  :key="user.id"
                  @click="handleUserClick(user, Number(departmentId))"
                  :class="[
                    'flex snap-start items-center rounded-md p-2',
                    props.onlyCurrentDepartment && !canInteractWithAllDepartments
                      ? currentUser?.department_id === Number(departmentId)
                        ? 'cursor-pointer hover:bg-gray-100'
                        : 'cursor-not-allowed opacity-75'
                      : 'cursor-pointer hover:bg-gray-100',
                  ]"
                >
                  <UserAvatar :user="user" size="small" class="mr-2" />
                  <span class="flex-1">
                    <HighlightText :text="user.name" :highlight="searchQuery" />
                    <small
                      v-if="user.status === CommonStatus.INACTIVE"
                      class="ml-1 text-xs text-red-500"
                      >(Inactive)</small
                    >
                  </span>
                  <i v-if="isUserSelected(user)" class="pi pi-check ml-auto text-blue-500"></i>
                </li>
              </ul>
            </li>
          </ul>
        </div>

        <div class="flex justify-end border-t border-gray-200 pt-2">
          <Button
            label="Xóa chọn"
            severity="danger"
            text
            @click="clearSelectedUsers"
            size="small"
          />
        </div>
      </div>
    </Popover>

    <DynamicSetting
      setting-name="user_multi_assign"
      :settings-schema="departmentSettingSchema"
      title="Cấu hình phòng ban"
    />
  </div>
</template>

<script setup lang="ts">
import { useElementSize } from "@vueuse/core";
import { isEqual } from "lodash";
import groupBy from "lodash/groupBy";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Popover from "primevue/popover";
import { computed, nextTick, ref, watch } from "vue";

import { CommonStatus } from "@/api/bcare-enum";
import { UserResponse, UserShort } from "@/api/bcare-types-v2";
import HighlightText from "@/base-components/HighlightText.vue";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useDepartment from "@/hooks/useDepartment";
import useUser from "@/hooks/useUser";
import { useAuthStore } from "@/stores/auth-store";
import { normalizeVietnamese } from "@/utils/string";

import UserAvatar from "./UserAvatar.vue";

interface PrimeVueInputText {
  $el: HTMLInputElement;
}

interface Props {
  modelValue: UserResponse[] | UserShort[];
  maxDisplay?: number;
  placeholder?: string;
  usePrimeVueInput?: boolean;
  onlyCurrentDepartment?: boolean;
  showInactiveUsers?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplay: 8,
  placeholder: "Chọn người dùng",
  usePrimeVueInput: false,
  onlyCurrentDepartment: false,
  showInactiveUsers: false,
});

const emit = defineEmits(["update:modelValue"]);
const { users } = useUser({ autoLoad: true });
const { getDepartmentNameById } = useDepartment();
const { currentUser } = useAuthStore();
const { currentSettingValue } = useComponentSetting("user_multi_assign");

const popover = ref();
const searchInput = ref<PrimeVueInputText | null>(null);
const searchQuery = ref("");
const selectedUsers = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const filteredUsers = computed(() => {
  let filteredList = users.value;

  if (!props.showInactiveUsers) {
    filteredList = filteredList.filter((user) => user.status !== CommonStatus.INACTIVE);
  }

  const query = normalizeVietnamese(searchQuery.value.trim());
  if (!query) return filteredList;

  return filteredList.filter(
    (user) =>
      normalizeVietnamese(user.name).includes(query) ||
      normalizeVietnamese(user.username).includes(query) ||
      (user.email && normalizeVietnamese(user.email).includes(query)) ||
      normalizeVietnamese(getDepartmentName(user.department_id)).includes(query),
  );
});

const groupedUsers = computed(() => {
  const grouped = groupBy(filteredUsers.value, "department_id");
  const entries: [string, typeof filteredUsers.value][] = [];

  if (props.onlyCurrentDepartment && currentUser?.department_id != null) {
    const currentDeptId = String(currentUser.department_id);
    if (grouped[currentDeptId]) {
      entries.push([currentDeptId, grouped[currentDeptId]]);
    }

    Object.entries(grouped).forEach(([deptId, users]) => {
      if (deptId !== String(currentUser.department_id)) {
        entries.push([deptId, users]);
      }
    });

    return entries;
  }

  if (currentUser?.department_id) {
    const currentDeptId = String(currentUser.department_id);
    if (grouped[currentDeptId]) {
      entries.push([currentDeptId, grouped[currentDeptId]]);
    }
  }

  Object.entries(grouped).forEach(([deptId, users]) => {
    if (!currentUser?.department_id || deptId !== String(currentUser.department_id)) {
      entries.push([deptId, users]);
    }
  });

  return entries;
});

const toggle = async (event: Event) => {
  popover.value.toggle(event);
  await nextTick();
  if (searchInput.value?.$el) {
    searchInput.value.$el.focus();
  }
};

const toggleUserSelection = (user: UserResponse | UserShort) => {
  const index = selectedUsers.value.findIndex((u) => u.id === user.id);
  if (index === -1) {
    selectedUsers.value = [...selectedUsers.value, user];
  } else {
    selectedUsers.value = selectedUsers.value.filter((u) => u.id !== user.id);
  }
};

const isUserSelected = (user: UserResponse | UserShort) => {
  return selectedUsers.value.some((u) => u.id === user.id);
};

const toggleDepartmentSelection = (departmentId: number) => {
  const departmentUsers = filteredUsers.value.filter((user) => user.department_id === departmentId);
  const state = departmentSelectionStates.value.get(departmentId);

  if (state === "all") {
    // Unselect all users in department
    selectedUsers.value = selectedUsers.value.filter(
      (user) => (user as UserResponse).department_id !== departmentId,
    );
  } else {
    // Select all unselected users in department
    const newUsers = departmentUsers.filter((user) => !isUserSelected(user));
    selectedUsers.value = [...selectedUsers.value, ...newUsers];
  }
};

const getDepartmentName = (departmentId: number) => {
  return getDepartmentNameById(departmentId);
};

const avatarSize = 29;

const clearSelectedUsers = () => {
  selectedUsers.value = [];
};

const toggleContainer = ref<HTMLElement | null>(null);
const { width } = useElementSize(toggleContainer);

const departmentSelectionStates = computed(() => {
  const states = new Map<number, "none" | "partial" | "all">();

  Object.entries(groupBy(filteredUsers.value, "department_id")).forEach(([deptId, users]) => {
    const departmentId = Number(deptId);
    if (users.length === 0) {
      states.set(departmentId, "none");
      return;
    }

    const selectedCount = users.filter((user) => isUserSelected(user)).length;

    if (selectedCount === 0) states.set(departmentId, "none");
    else if (selectedCount === users.length) states.set(departmentId, "all");
    else states.set(departmentId, "partial");
  });
  return states;
});

const getSelectedCountInDepartment = (departmentId: number) => {
  const departmentUsers = filteredUsers.value.filter((user) => user.department_id === departmentId);
  return departmentUsers.filter((user) => isUserSelected(user)).length;
};

const getDepartmentIconClass = (departmentId: number) => {
  const state = departmentSelectionStates.value.get(departmentId);
  return {
    "pi-check-square": state === "all",
    "pi-minus-circle": state === "partial",
    "text-blue-500": true,
    pi: true,
    "ml-auto": true,
  };
};

const canInteractWithAllDepartments = computed(() => {
  const settings = currentSettingValue.value;
  if (!settings?.allowed_departments?.length || !currentUser?.department_id) return false;

  return settings.allowed_departments.includes(currentUser?.department_id);
});

const handleUserClick = (user: UserResponse | UserShort, departmentId: number) => {
  if (
    !props.onlyCurrentDepartment ||
    currentUser?.department_id === departmentId ||
    canInteractWithAllDepartments.value
  ) {
    toggleUserSelection(user);
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (!isEqual(newValue, selectedUsers.value)) {
      selectedUsers.value = newValue;
    }
  },
  { deep: true },
);

const departmentSettingSchema = [
  {
    type: "multi-number" as const,
    label: "Phòng ban được phép chọn user từ mọi phòng ban",
    field_name: "allowed_departments",
    value: [],
  },
];
</script>
