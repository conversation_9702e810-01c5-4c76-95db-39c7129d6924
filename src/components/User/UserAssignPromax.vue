<template>
  <div class="relative w-full">
    <DynamicSetting
      :settingName="settingName"
      :settingsSchema="settingsSchema"
      class="absolute right-0"
      title="Cài đặt"
      @popover-close="refreshCurrentSetting"
    >
    </DynamicSetting>
    <div
      ref="toggleContainer"
      :class="[
        'cursor-pointer',
        !props.usePrimeVueInput
          ? 'break-words text-gray-700 decoration-1 underline-offset-2 hover:underline hover:decoration-dotted'
          : 'flex w-full items-center ',
      ]"
      @click="toggle"
    >
      <!-- Single Selection Display -->
      <template v-if="!multiple || (multiple && selectedUsers.length === 1)">
        <div v-if="displayedUser" class="flex items-center gap-2">
          <UserAvatar :size="avatarSize" :user="displayedUser" />
          <span class="text-sm">{{ displayedUser.name }}</span>
        </div>
        <div v-else class="flex items-center gap-2 text-gray-500">
          <span class="text-sm text-gray-400">{{ placeholder }}</span>
          <i class="pi pi-user-plus text-gray-400" />
        </div>
      </template>
      <!-- Multiple Selection Display -->
      <template v-else>
        <div
          class="inline-flex flex-wrap items-center gap-1 rounded px-1.5 py-0.5 transition-colors hover:bg-gray-100/50"
        >
          <template v-if="selectedUsers.length > 0">
            <UserAvatar
              v-for="user in selectedUsers.slice(0, maxDisplay)"
              :key="user.id"
              :size="avatarSize"
              :user="user"
              class="transition-transform hover:scale-110"
            />
            <div
              v-if="selectedUsers.length > maxDisplay"
              :style="{ width: `${avatarSize}px`, height: `${avatarSize}px` }"
              class="flex items-center justify-center rounded-full bg-gray-200 text-xs font-medium transition-transform hover:scale-110"
            >
              +{{ selectedUsers.length - maxDisplay }}
            </div>
          </template>
          <div v-else class="flex items-center gap-2 text-gray-500">
            <span class="text-sm text-gray-400">{{ placeholder }}</span>
            <i class="pi pi-user-plus text-gray-400" />
          </div>
        </div>
      </template>
    </div>
    <Popover ref="popover">
      <div :style="{ width: `${width}px`, minWidth: '300px' }">
        <InputText
          ref="searchInput"
          v-model="searchQuery"
          class="mb-2 w-full"
          placeholder="Tìm kiếm người dùng..."
          size="small"
        />
        <div class="relative">
          <!-- Empty State -->
          <div
            v-if="filteredUsers.length === 0"
            class="flex flex-col items-center justify-center py-8 text-center"
          >
            <i class="pi pi-search mb-2 text-2xl text-gray-400" />
            <p class="text-sm text-gray-500">
              {{
                searchQuery ? `Không tìm thấy kết quả cho "${searchQuery}"` : "Không có người dùng"
              }}
            </p>
          </div>

          <!-- Department Groups with Snap Scroll -->
          <div class="max-h-60 overflow-y-auto scroll-smooth">
            <ul class="m-0 list-none p-0">
              <li
                v-for="([departmentId, group], index) in groupedUsers"
                :key="departmentId"
                class="relative"
              >
                <!-- Department Header (Sticky) -->
                <div
                  class="sticky top-0 z-10 flex cursor-pointer items-center justify-between bg-white/95 p-2 text-primary-500 shadow-sm backdrop-blur-sm hover:bg-gray-100"
                  @click="toggleAllUsers(Number(departmentId))"
                >
                  <span class="font-medium">
                    {{ `${index + 1}. ${getDepartmentNameById(Number(departmentId))}` }}
                    <span class="ml-2 text-sm text-gray-500">
                      (<span
                        :class="{
                          'font-medium text-blue-500':
                            getSelectedCountInDepartment(Number(departmentId)) > 0,
                        }"
                      >
                        {{ getSelectedCountInDepartment(Number(departmentId)) }} </span
                      >/{{ group.length }})
                    </span>
                  </span>
                  <i v-if="multiple" :class="getDepartmentIconClass(Number(departmentId))" />
                </div>

                <!-- User List with Snap Scroll -->
                <div class="snap-y snap-mandatory">
                  <ul class="ml-1 mt-1 pb-4">
                    <li
                      v-for="user in group"
                      :key="user.id"
                      class="flex cursor-pointer snap-start items-center rounded-md p-2 hover:bg-gray-100"
                      @click="selectUser(user)"
                    >
                      <UserAvatar :size="avatarSize" :user="user" class="mr-2" />
                      <span class="flex-1">
                        <HighlightText :highlight="searchQuery" :text="user.name" />
                        <small
                          v-if="user.status === CommonStatus.INACTIVE"
                          class="ml-1 text-xs text-red-500"
                          >(Inactive)</small
                        >
                      </span>
                      <i v-if="isUserSelected(user)" class="pi pi-check ml-auto text-blue-500" />
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- Footer for multiple selection -->
        <div v-if="multiple" class="flex justify-end border-t border-gray-200 pt-2">
          <Button label="Xóa chọn" severity="danger" size="small" text @click="clearSelection" />
        </div>
      </div>
    </Popover>
  </div>
</template>

<script lang="ts" setup>
import { useElementSize } from "@vueuse/core";
import groupBy from "lodash/groupBy";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Popover from "primevue/popover";
import { computed, nextTick, ref } from "vue";

import { CommonStatus } from "@/api/bcare-enum";
import { UserResponse } from "@/api/bcare-types-v2";
import { UniversalSetting } from "@/api/extend-types";
import HighlightText from "@/base-components/HighlightText.vue";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useDepartment from "@/hooks/useDepartment";
import useUser from "@/hooks/useUser";
import { normalizeVietnamese } from "@/utils/string";

import UserAvatar from "./UserAvatar.vue";

const settingsSchema: UniversalSetting[] = [
  {
    type: "multi-number",
    label: "Phòng ban",
    field_name: "department_ids",
    value: null,
  },
];

interface Props {
  modelValue?: number | number[];
  placeholder?: string;
  departmentId?: number;
  multiple?: boolean;
  maxDisplay?: number;
  globalUniqueName?: string;
  showInactiveUsers?: boolean;
  usePrimeVueInput?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  placeholder: "Chọn người dùng",
  multiple: false,
  maxDisplay: 8,
  globalUniqueName: "shared",
  showInactiveUsers: false,
  usePrimeVueInput: false,
});

const settingName = `user_assign__${props.globalUniqueName}`;
const { currentSettingValue, refreshCurrentSetting } = useComponentSetting(settingName);

const departmentId = computed(() => {
  const settingValue = currentSettingValue.value;
  if (settingValue && settingValue.department_ids?.length > 0) {
    return settingValue.department_ids[0];
  }
  return null;
});

const emit = defineEmits<{ "update:modelValue": [value: number | number[] | undefined] }>();

// Composables
const { users } = useUser({ autoLoad: true });
const { getDepartmentNameById } = useDepartment();

// Refs
const popover = ref();
const searchInput = ref();
const searchQuery = ref("");
const toggleContainer = ref<HTMLElement | null>(null);
const { width } = useElementSize(toggleContainer);

// Constants
const avatarSize = 30;

// Computed
const departmentName = computed(() => getDepartmentNameById(departmentId.value));
const departmentUsers = computed(() => {
  const departmentIds = currentSettingValue.value?.department_ids;
  let filteredUsers = users.value;

  // Filter out inactive users if showInactiveUsers is false
  if (!props.showInactiveUsers) {
    filteredUsers = filteredUsers.filter((user) => user.status !== CommonStatus.INACTIVE);
  }

  if (!departmentIds?.length) {
    return filteredUsers;
  }
  return filteredUsers.filter((user) => departmentIds.includes(user.department_id));
});
const filteredUsers = computed(() => {
  const query = normalizeVietnamese(searchQuery.value.trim().toLowerCase());
  if (!query) return departmentUsers.value;
  return departmentUsers.value.filter(
    (user) =>
      normalizeVietnamese(user.name.toLowerCase()).includes(query) ||
      normalizeVietnamese(user.username.toLowerCase()).includes(query) ||
      (user.email && normalizeVietnamese(user.email.toLowerCase()).includes(query)),
  );
});
const groupedUsers = computed(() => {
  const grouped = groupBy(filteredUsers.value, "department_id");
  const entries: [string, typeof filteredUsers.value][] = [];

  const departmentIds = currentSettingValue.value?.department_ids;
  if (!departmentIds?.length) {
    Object.entries(grouped).forEach(([deptId, users]) => {
      entries.push([deptId, users]);
    });
  } else {
    departmentIds.forEach((deptId: number) => {
      if (grouped[deptId]) {
        entries.push([String(deptId), grouped[deptId]]);
      }
    });
  }

  return entries;
});

// Single selection computed
const selectedUser = computed(() =>
  props.multiple ? null : departmentUsers.value.find((user) => user.id === props.modelValue),
);

// Multiple selection computed
const selectedUsers = computed(() => {
  if (!props.multiple) return [];
  const selectedIds = Array.isArray(props.modelValue) ? props.modelValue : [];
  return departmentUsers.value.filter((user) => selectedIds.includes(user.id));
});

const getSelectedCountInDepartment = (departmentId: number) => {
  const deptUsers = filteredUsers.value.filter((user) => user.department_id === departmentId);
  if (props.multiple) {
    const selectedIds = Array.isArray(props.modelValue) ? props.modelValue : [];
    return deptUsers.filter((user) => selectedIds.includes(user.id)).length;
  }
  return 0;
};

const getDepartmentIconClass = (departmentId: number) => {
  const deptUsers = filteredUsers.value.filter((user) => user.department_id === departmentId);
  const selectedCount = getSelectedCountInDepartment(departmentId);

  return {
    "pi-check-square": selectedCount === deptUsers.length,
    "pi-minus-circle": selectedCount > 0 && selectedCount < deptUsers.length,
    "pi-square": selectedCount === 0,
    pi: true,
    "text-blue-500": true,
    "ml-auto": true,
  };
};

// Add new computed for displayed user
const displayedUser = computed(() => {
  if (!props.multiple) {
    return selectedUser.value;
  }
  // When multiple=true and exactly 1 user is selected
  return selectedUsers.value.length === 1 ? selectedUsers.value[0] : null;
});

// Methods
const toggle = async (event: Event) => {
  popover.value.toggle(event);
  await nextTick();
  if (searchInput.value?.$el) {
    searchInput.value.$el.focus();
  }
};

const isUserSelected = (user: UserResponse): boolean => {
  if (props.multiple) {
    return Array.isArray(props.modelValue) && props.modelValue.includes(user.id);
  }
  return user.id === props.modelValue;
};

const selectUser = (user: UserResponse) => {
  if (props.multiple) {
    const currentValue = Array.isArray(props.modelValue) ? props.modelValue : [];
    const newValue = isUserSelected(user)
      ? currentValue.filter((id) => id !== user.id)
      : [...currentValue, user.id];
    emit("update:modelValue", newValue);
  } else {
    // Emit undefined nếu click vào user đã chọn
    emit("update:modelValue", isUserSelected(user) ? undefined : user.id);
    popover.value.hide();
  }
  searchQuery.value = "";
};

const toggleAllUsers = (departmentId: number) => {
  if (!props.multiple) return;

  const deptUsers = filteredUsers.value.filter((user) => user.department_id === departmentId);
  const currentValue = Array.isArray(props.modelValue) ? props.modelValue : [];
  const allSelected = deptUsers.every((user) => currentValue.includes(user.id));

  if (allSelected) {
    // Bỏ chọn tất cả users trong phòng ban này
    emit(
      "update:modelValue",
      currentValue.filter((id) => !deptUsers.some((user) => user.id === id)),
    );
  } else {
    // Chọn tất cả users trong phòng ban này
    const newIds = deptUsers.map((user) => user.id);
    emit("update:modelValue", [...new Set([...currentValue, ...newIds])]);
  }
};

const clearSelection = () => {
  emit("update:modelValue", props.multiple ? [] : undefined);
  searchQuery.value = "";
};
</script>
