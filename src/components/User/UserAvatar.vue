<!-- UserAvatar.vue -->
<template>
  <div v-tooltip.top="tooltipText" class="relative inline-flex items-center gap-2">
    <Avatar
      class="cursor-pointer text-sm font-medium"
      v-bind="$attrs"
      :image="avatarUrl"
      :label="avatarUrl ? undefined : avatarLabel"
      shape="circle"
      :style="avatarStyle"
      :class="[sizeClass, { 'opacity-60 grayscale': isInactive }]"
    >
      <template v-if="avatarUrl" #default>
        <img
          :src="avatarUrl"
          class="h-full w-full object-cover"
          :class="{ 'opacity-60 grayscale': isInactive }"
          :alt="resolvedUser?.name"
        />
      </template>
    </Avatar>
    <div
      v-if="online && !isInactive"
      class="absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white bg-green-500"
    ></div>
    <div
      v-if="showName"
      class="flex flex-col"
      :class="[additionalClass, textSizeClass, { 'text-slate-400': isInactive }]"
    >
      <span class="font-medium">
        {{ resolvedUser?.name }}
        <small v-if="isInactive" class="text-xs text-red-500">(Inactive)</small>
      </span>
      <slot name="subtitle" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Avatar from "primevue/avatar";
import { computed } from "vue";

import { CommonStatus } from "@/api/bcare-enum";
import { UserResponse, UserShort } from "@/api/bcare-types-v2";
import { getContrastTextColor, useHashColor } from "@/composables/useHashColor";
import useUser from "@/hooks/useUser";
import { getAvatarLabel } from "@/utils/avatar";
import { getLocalAvatarUri } from "@/utils/helper";

interface Props {
  user?: UserResponse | UserShort;
  userId?: number;
  name?: string;
  online?: boolean;
  size?: "small" | "normal" | "large" | number;
  muted?: boolean;
  showName?: boolean;
  additionalClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  online: false,
  size: "normal",
  muted: false,
  showName: false,
  additionalClass: "",
});

defineOptions({
  inheritAttrs: false,
});

const { getUserById } = useUser({ autoLoad: true });

const resolvedUser = computed(() => {
  if (props.user) {
    return props.user;
  }
  if (props.userId) {
    return getUserById(props.userId);
  }
  return undefined;
});

const isInactive = computed(() => {
  const user = resolvedUser.value as UserResponse | UserShort | undefined;
  return user && "status" in user ? user.status === CommonStatus.INACTIVE : false;
});

const tooltipText = computed(() => {
  if (resolvedUser.value) {
    const status = isInactive.value ? " (Inactive)" : "";
    return resolvedUser.value.name + status;
  }
  return props.name || "";
});

const avatarLabel = computed(() => {
  if (resolvedUser.value) {
    return getAvatarLabel(resolvedUser.value.name, 2).toUpperCase();
  }
  if (props.name) {
    return getAvatarLabel(props.name, 2).toUpperCase();
  }
  return "?";
});

const avatarColor = computed(() => {
  const name = resolvedUser.value?.name || props.name || "";
  return useHashColor(name, "UserAvatar");
});

const textColor = computed(() => getContrastTextColor(avatarColor.value));

const sizeClasses = {
  small: "size-8", // 32px - phù hợp với mobile
  normal: "size-10", // 40px
  large: "size-16", // 64px
};

const textSizeClasses = {
  small: "text-sm",
  normal: "text-sm",
  large: "text-base",
};

const sizeClass = computed(() => {
  if (typeof props.size === "number") {
    return `size-[${props.size}px]`;
  }
  return sizeClasses[props.size as keyof typeof sizeClasses];
});

const textSizeClass = computed(() => {
  if (typeof props.size === "number") {
    return "text-sm";
  }
  return textSizeClasses[props.size as keyof typeof textSizeClasses];
});

const avatarStyle = computed(() => ({
  backgroundColor: props.muted || isInactive.value ? "bg-slate-300" : avatarColor.value,
  color: props.muted || isInactive.value ? "#64748b" : textColor.value,
  opacity: props.muted || isInactive.value ? "0.75" : "1",
  ...(typeof props.size === "number"
    ? { width: `${props.size}px`, height: `${props.size}px` }
    : {}),
}));

const avatarUrl = computed(() => {
  if (resolvedUser.value?.profile_image) {
    return getLocalAvatarUri(resolvedUser.value.profile_image);
  }
  return undefined;
});
</script>
