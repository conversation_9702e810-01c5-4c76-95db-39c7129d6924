<template>
  <div class="relative w-full">
    <!-- Header Section -->
    <div v-if="showHeader && (title || $slots['left-header'] || $slots['right-header'])" class="mb-4">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center gap-2">
          <slot name="left-header">
            <h2 v-if="title" class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ title }}
            </h2>
          </slot>
        </div>
        <div class="flex items-center gap-2">
          <slot name="right-header" />
        </div>
      </div>
    </div>

    <!-- DataTable Component -->
    <DataTable
      v-bind="$attrs"
      :value="data"
      :loading="loading"
      :totalRecords="totalRecords"
      :lazy="lazy"
      :paginator="paginator"
      :rows="rows"
      :rowsPerPageOptions="rowsPerPageOptions"
      v-model:filters="computedFilters"
      v-model:selection="computedSelection"
      @page="onPageEvent"
      @row-click="onRowClick"
      @row-select="onRowSelect"
      @row-unselect="onRowUnselect"
      :pt="passthroughConfig"
      :class="tableClasses"
    >
      <!-- Forward all slots to the underlying DataTable -->
      <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
        <slot :name="slotName" v-bind="slotProps" />
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts" generic="T">
import { computed } from 'vue';
import DataTable from 'primevue/datatable';
import type { DataTablePageEvent, DataTableRowClickEvent, DataTableRowSelectEvent, DataTableRowUnselectEvent } from 'primevue/datatable';

// Define props interface
interface Props {
  // Data props
  data?: T[];
  loading?: boolean;
  totalRecords?: number;
  
  // Pagination props
  lazy?: boolean;
  paginator?: boolean;
  rows?: number;
  rowsPerPageOptions?: number[];
  
  // Selection props
  selection?: T | T[];
  selectionMode?: 'single' | 'multiple' | null;
  
  // Filter props
  filters?: Record<string, any>;
  
  // UI props
  title?: string;
  showHeader?: boolean;
  size?: 'small' | 'large';
  stripedRows?: boolean;
  showGridlines?: boolean;
  
  // Styling props
  tableClass?: string;
}

// Define emits
interface Emits {
  (event: 'update:selection', value: T | T[]): void;
  (event: 'update:filters', value: Record<string, any>): void;
  (event: 'page', value: DataTablePageEvent): void;
  (event: 'row-click', value: DataTableRowClickEvent): void;
  (event: 'row-select', value: DataTableRowSelectEvent): void;
  (event: 'row-unselect', value: DataTableRowUnselectEvent): void;
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  totalRecords: 0,
  lazy: false,
  paginator: false,
  rows: 10,
  rowsPerPageOptions: () => [10, 20, 50, 100],
  selection: () => [],
  selectionMode: null,
  filters: () => ({}),
  title: '',
  showHeader: true,
  size: 'large',
  stripedRows: false,
  showGridlines: false,
  tableClass: ''
});

const emit = defineEmits<Emits>();

// Computed properties for two-way binding
const computedSelection = computed({
  get: () => props.selection,
  set: (value: T | T[]) => emit('update:selection', value)
});

const computedFilters = computed({
  get: () => props.filters,
  set: (value: Record<string, any>) => emit('update:filters', value)
});

// Event handlers
const onPageEvent = (event: DataTablePageEvent) => {
  emit('page', event);
};

const onRowClick = (event: DataTableRowClickEvent) => {
  emit('row-click', event);
};

const onRowSelect = (event: DataTableRowSelectEvent) => {
  emit('row-select', event);
};

const onRowUnselect = (event: DataTableRowUnselectEvent) => {
  emit('row-unselect', event);
};

// Styling configuration
const tableClasses = computed(() => [
  'datatable-v2',
  props.tableClass,
  {
    'datatable-v2--small': props.size === 'small',
    'datatable-v2--striped': props.stripedRows,
    'datatable-v2--gridlines': props.showGridlines
  }
]);

// PrimeVue Passthrough configuration for consistent styling
const passthroughConfig = computed(() => ({
  root: {
    class: [
      // Header styling
      '[&_.p-datatable-header-cell]:!py-3 !rounded-t-lg',
      '[&_.p-datatable-header-cell]:!py-3 !rounded-t-lg',
      
      // Paginator styling
      '[&_.p-paginator]:!rounded-t-none',
      '[&_:deep(.p-paginator)]:!rounded-t-none !rounded-b-lg',
      
      // Cell padding
      '[&_td:first-child]:pl-5',
      '[&_td:last-child]:pr-5',
      '[&_th:first-child]:pl-5',
      '[&_th:last-child]:pr-5',
      
      // Size variants
      props.size === 'small' && '[&_td]:!py-2 [&_th]:!py-2',
      
      // Additional styling
      'border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden'
    ]
  },
  table: {
    class: 'min-w-full'
  },
  thead: {
    class: 'bg-gray-50 dark:bg-gray-800'
  },
  tbody: {
    class: 'bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700'
  }
}));
</script>

<style scoped>
.datatable-v2 {
  @apply w-full;
}

.datatable-v2--small :deep(.p-datatable-tbody > tr > td) {
  @apply py-2 px-3;
}

.datatable-v2--small :deep(.p-datatable-thead > tr > th) {
  @apply py-2 px-3;
}
</style>
