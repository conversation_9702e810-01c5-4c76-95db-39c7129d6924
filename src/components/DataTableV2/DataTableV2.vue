<template>
  <div
    class="relative w-full"
    role="region"
    :aria-label="title || 'Data table'"
    :aria-describedby="title ? `${title.toLowerCase().replace(/\s+/g, '-')}-description` : undefined"
  >
    <!-- Screen reader description -->
    <div
      v-if="title"
      :id="`${title.toLowerCase().replace(/\s+/g, '-')}-description`"
      class="sr-only"
    >
      {{ title }} containing {{ totalRecords }} items. Use arrow keys to navigate between cells.
    </div>

    <!-- Header Section -->
    <div v-if="showHeader && (title || $slots['left-header'] || $slots['right-header'])" class="mb-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center gap-2">
          <slot name="left-header">
            <h2 v-if="title" class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ title }}
            </h2>
          </slot>
        </div>
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <slot name="right-header" />
        </div>
      </div>
    </div>

    <!-- DataTable Component -->
    <DataTable
      v-bind="$attrs"
      :value="data"
      :loading="loading"
      :totalRecords="totalRecords"
      :lazy="lazy"
      :paginator="paginator"
      :rows="rows"
      :rowsPerPageOptions="rowsPerPageOptions"
      v-model:filters="computedFilters"
      v-model:selection="computedSelection"
      @page="onPageEvent"
      @row-click="onRowClick"
      @row-dblclick="onRowDblClick"
      @row-contextmenu="onRowContextMenu"
      @row-select="onRowSelect"
      @row-unselect="onRowUnselect"
      @row-select-all="onRowSelectAll"
      @row-unselect-all="onRowUnselectAll"
      @row-expand="onRowExpand"
      @row-collapse="onRowCollapse"
      @row-edit-save="onRowEditSave"
      @row-edit-cancel="onRowEditCancel"
      @row-edit-init="onRowEditInit"
      @column-resize-end="onColumnResizeEnd"
      @column-reorder="onColumnReorder"
      @row-reorder="onRowReorder"
      @sort="onSort"
      @filter="onFilter"
      @rowgroup-expand="onRowGroupExpand"
      @rowgroup-collapse="onRowGroupCollapse"
      :pt="passthroughConfig"
      :class="tableClasses"
    >
      <!-- Standard DataTable slots with fallbacks -->
      <template #header v-if="$slots.header || showHeader">
        <slot name="header">
          <div class="flex flex-wrap items-center justify-between gap-4 p-4">
            <div class="flex items-center gap-2">
              <slot name="left-header">
                <h2 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-white">
                  {{ title }}
                  <span v-if="totalRecords > 0" class="text-sm font-normal text-gray-500 dark:text-gray-400">
                    ({{ totalRecords }})
                  </span>
                </h2>
              </slot>
            </div>
            <div class="flex items-center gap-2">
              <slot name="right-header" />
            </div>
          </div>
        </slot>
      </template>

      <template #footer v-if="$slots.footer">
        <slot name="footer" />
      </template>

      <template #empty v-if="$slots.empty">
        <slot name="empty">
          <div class="flex flex-col items-center justify-center py-12 text-center">
            <div class="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">No data available</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">There are no items to display at this time.</p>
          </div>
        </slot>
      </template>

      <template #loading v-if="$slots.loading">
        <slot name="loading">
          <div class="flex items-center justify-center py-8">
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
              <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.1s"></div>
              <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </slot>
      </template>

      <!-- Forward all other slots to the underlying DataTable -->
      <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
        <template v-if="!['header', 'footer', 'empty', 'loading', 'left-header', 'right-header'].includes(String(slotName))">
          <slot :name="slotName" v-bind="slotProps" />
        </template>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts" generic="T">
import { computed } from 'vue';
import DataTable from 'primevue/datatable';
import type { DataTablePageEvent, DataTableRowClickEvent, DataTableRowSelectEvent, DataTableRowUnselectEvent, DataTableSortEvent, DataTableFilterEvent } from 'primevue/datatable';

// Define props interface
interface Props {
  // Data props
  data?: T[];
  loading?: boolean;
  totalRecords?: number;
  
  // Pagination props
  lazy?: boolean;
  paginator?: boolean;
  rows?: number;
  rowsPerPageOptions?: number[];
  
  // Selection props
  selection?: T | T[];
  selectionMode?: 'single' | 'multiple' | null;
  
  // Filter props
  filters?: Record<string, any>;
  
  // UI props
  title?: string;
  showHeader?: boolean;
  size?: 'small' | 'large';
  stripedRows?: boolean;
  showGridlines?: boolean;
  
  // Styling props
  tableClass?: string;
}

// Define emits - comprehensive list of DataTable events
interface Emits {
  // Two-way binding events
  (event: 'update:selection', value: T | T[]): void;
  (event: 'update:filters', value: Record<string, any>): void;
  (event: 'update:expandedRows', value: Record<string, boolean>): void;
  (event: 'update:editingRows', value: Record<string, boolean>): void;

  // Pagination events
  (event: 'page', value: DataTablePageEvent): void;

  // Row interaction events
  (event: 'row-click', value: DataTableRowClickEvent): void;
  (event: 'row-dblclick', value: DataTableRowClickEvent): void;
  (event: 'row-contextmenu', value: DataTableRowClickEvent): void;
  (event: 'row-select', value: DataTableRowSelectEvent): void;
  (event: 'row-unselect', value: DataTableRowUnselectEvent): void;
  (event: 'row-select-all', value: { originalEvent: Event; data: T[] }): void;
  (event: 'row-unselect-all', value: { originalEvent: Event }): void;

  // Row expansion events
  (event: 'row-expand', value: { originalEvent: Event; data: T }): void;
  (event: 'row-collapse', value: { originalEvent: Event; data: T }): void;

  // Row editing events
  (event: 'row-edit-save', value: { originalEvent: Event; data: T; newData: T; field: string; index: number }): void;
  (event: 'row-edit-cancel', value: { originalEvent: Event; data: T; index: number }): void;
  (event: 'row-edit-init', value: { originalEvent: Event; data: T; newData: T; field: string; index: number }): void;

  // Column events
  (event: 'column-resize-end', value: { element: HTMLElement; delta: number }): void;
  (event: 'column-reorder', value: { originalEvent: Event; dragIndex: number; dropIndex: number }): void;

  // Row reordering events
  (event: 'row-reorder', value: { originalEvent: Event; value: T[]; dragIndex: number; dropIndex: number }): void;

  // Sorting events
  (event: 'sort', value: DataTableSortEvent): void;

  // Filter events
  (event: 'filter', value: DataTableFilterEvent): void;

  // Group events
  (event: 'rowgroup-expand', value: { originalEvent: Event; data: any }): void;
  (event: 'rowgroup-collapse', value: { originalEvent: Event; data: any }): void;
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  totalRecords: 0,
  lazy: false,
  paginator: false,
  rows: 10,
  rowsPerPageOptions: () => [10, 20, 50, 100],
  selection: () => [],
  selectionMode: null,
  filters: () => ({}),
  title: '',
  showHeader: true,
  size: 'large',
  stripedRows: false,
  showGridlines: false,
  tableClass: ''
});

const emit = defineEmits<Emits>();

// Computed properties for two-way binding
const computedSelection = computed({
  get: () => props.selection,
  set: (value: T | T[]) => emit('update:selection', value)
});

const computedFilters = computed({
  get: () => props.filters,
  set: (value: Record<string, any>) => emit('update:filters', value)
});

// Event handlers - comprehensive forwarding of all DataTable events
const onPageEvent = (event: DataTablePageEvent) => {
  emit('page', event);
};

const onRowClick = (event: DataTableRowClickEvent) => {
  emit('row-click', event);
};

const onRowDblClick = (event: DataTableRowClickEvent) => {
  emit('row-dblclick', event);
};

const onRowContextMenu = (event: DataTableRowClickEvent) => {
  emit('row-contextmenu', event);
};

const onRowSelect = (event: DataTableRowSelectEvent) => {
  emit('row-select', event);
};

const onRowUnselect = (event: DataTableRowUnselectEvent) => {
  emit('row-unselect', event);
};

const onRowSelectAll = (event: { originalEvent: Event; data: T[] }) => {
  emit('row-select-all', event);
};

const onRowUnselectAll = (event: { originalEvent: Event }) => {
  emit('row-unselect-all', event);
};

const onRowExpand = (event: { originalEvent: Event; data: T }) => {
  emit('row-expand', event);
};

const onRowCollapse = (event: { originalEvent: Event; data: T }) => {
  emit('row-collapse', event);
};

const onRowEditSave = (event: { originalEvent: Event; data: T; newData: T; field: string; index: number }) => {
  emit('row-edit-save', event);
};

const onRowEditCancel = (event: { originalEvent: Event; data: T; index: number }) => {
  emit('row-edit-cancel', event);
};

const onRowEditInit = (event: { originalEvent: Event; data: T; newData: T; field: string; index: number }) => {
  emit('row-edit-init', event);
};

const onColumnResizeEnd = (event: { element: HTMLElement; delta: number }) => {
  emit('column-resize-end', event);
};

const onColumnReorder = (event: { originalEvent: Event; dragIndex: number; dropIndex: number }) => {
  emit('column-reorder', event);
};

const onRowReorder = (event: { originalEvent: Event; value: T[]; dragIndex: number; dropIndex: number }) => {
  emit('row-reorder', event);
};

const onSort = (event: DataTableSortEvent) => {
  emit('sort', event);
};

const onFilter = (event: DataTableFilterEvent) => {
  emit('filter', event);
};

const onRowGroupExpand = (event: { originalEvent: Event; data: any }) => {
  emit('rowgroup-expand', event);
};

const onRowGroupCollapse = (event: { originalEvent: Event; data: any }) => {
  emit('rowgroup-collapse', event);
};

// Styling configuration
const tableClasses = computed(() => [
  'datatable-v2',
  props.tableClass,
  {
    'datatable-v2--small': props.size === 'small',
    'datatable-v2--striped': props.stripedRows,
    'datatable-v2--gridlines': props.showGridlines
  }
]);

// PrimeVue Passthrough configuration for professional enterprise styling
const passthroughConfig = computed(() => ({
  root: {
    class: [
      // Professional table container
      'bg-white dark:bg-gray-900',
      'border border-gray-200 dark:border-gray-700',
      'rounded-lg',
      'shadow-sm',
      'overflow-hidden',

      // Responsive design
      'overflow-x-auto',
      'min-w-full'
    ],
    // Accessibility attributes
    'aria-label': props.title || 'Data table',
    'role': 'table'
  },
  header: {
    class: ['!p-3', '!rounded-t-lg']
  },
  headerRow: {
    class: []
  },
  headerCell: {
    class: [
      // Professional header styling
      'px-4 py-3 sm:px-6',
      'text-left text-xs font-semibold text-gray-700 dark:text-gray-300',
      'uppercase tracking-wider',
      'bg-gray-50 dark:bg-gray-800',

      // Hover states for sortable columns
      'hover:bg-gray-100 dark:hover:bg-gray-700',
      'transition-colors duration-150',

      // Focus states for accessibility
      'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset',

      // Size variants
      props.size === 'small' && 'px-3 py-2 sm:px-4',
      props.size === 'large' && 'px-6 py-4 sm:px-8'
    ]
  },
  thead: {
    class: [
      'bg-gray-50 dark:bg-gray-800',
      // Filter row handling
      props.showGridlines && 'border-b border-gray-200 dark:border-gray-700'
    ]
  },
  filterRow: {
    class: [
      'bg-white dark:bg-gray-900',
      'border-b border-gray-200 dark:border-gray-700',
      '[&_.p-column-filter]:w-full',
      '[&_.p-inputtext]:w-full [&_.p-inputtext]:text-sm',
      '[&_.p-select]:w-full [&_.p-select]:text-sm',
      '[&_.p-calendar]:w-full [&_.p-calendar]:text-sm'
    ]
  },
  filterCell: {
    class: [
      'px-4 py-2 sm:px-6',
      'bg-white dark:bg-gray-900',

      // Size variants for filter cells
      props.size === 'small' && 'px-3 py-1.5 sm:px-4',
      props.size === 'large' && 'px-6 py-3 sm:px-8'
    ]
  },
  tbody: {
    class: [
      'bg-white dark:bg-gray-900',
      props.stripedRows && '[&>tr:nth-child(even)]:bg-gray-50 dark:[&>tr:nth-child(even)]:bg-gray-800/50',
      props.showGridlines && 'divide-y divide-gray-200 dark:divide-gray-700'
    ]
  },
  bodyRow: {
    class: [
      // Professional row styling with subtle hover effects
      'hover:bg-gray-50 dark:hover:bg-gray-800/50',
      'transition-colors duration-150 ease-in-out',
      'border-b border-gray-200 dark:border-gray-700 last:border-b-0',

      // Focus states for accessibility
      'focus-within:bg-gray-50 dark:focus-within:bg-gray-800/50',

      // Selection states
      'data-[selected=true]:bg-primary-50 dark:data-[selected=true]:bg-primary-900/20',
      'data-[selected=true]:border-primary-200 dark:data-[selected=true]:border-primary-700'
    ]
  },
  bodyCell: {
    class: [
      // Professional cell styling
      'px-4 py-3 sm:px-6',
      'text-sm text-gray-900 dark:text-gray-100',
      'align-top',

      // Typography for different data types
      '[&:has(input[type="number"])]:text-right [&:has(input[type="number"])]:font-mono',
      '[&:has(.currency)]:text-right [&:has(.currency)]:font-mono',
      '[&:has(.percentage)]:text-right [&:has(.percentage)]:font-mono',

      // Size variants
      props.size === 'small' && 'px-3 py-2 sm:px-4',
      props.size === 'large' && 'px-6 py-4 sm:px-8',

      // Vertical alignment for multi-line content
      'leading-5'
    ]
  },
  table: {
    class: 'min-w-full table-auto'
  },
  pcPaginator: {
    root: {
      class: [
        '[&_.p-inputnumber-input]:!min-w-[10ch]',
        '[&_.p-inputnumber-input]:!text-sm border-t border-x border-b',
        'flex items-center',
        'grid grid-cols-[1fr_auto_1fr] items-center',
        '[&_.p-paginator-left]:justify-self-start',
        '[&_.p-paginator-center]:justify-self-center',
        '[&_.p-paginator-right]:justify-self-end',
        'px-5 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700'
      ]
    }
  }
}));
</script>

<style scoped>
.datatable-v2 {
  width: 100%;
  position: relative;
}

/* Responsive table wrapper */
.datatable-v2 :deep(.p-datatable-wrapper) {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Small size variant */
.datatable-v2--small :deep(.p-datatable-tbody > tr > td) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.datatable-v2--small :deep(.p-datatable-thead > tr > th) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .datatable-v2 :deep(.p-datatable-thead > tr > th) {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .datatable-v2 :deep(.p-datatable-tbody > tr > td) {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .datatable-v2 :deep(.p-paginator) {
    flex-direction: column;
    gap: 0.5rem;
  }

  .datatable-v2 :deep(.p-paginator-left),
  .datatable-v2 :deep(.p-paginator-right) {
    justify-content: center;
  }
}

/* Loading state styling */
.datatable-v2 :deep(.p-datatable-loading-overlay) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.datatable-v2 :deep(.p-datatable-loading-icon) {
  color: #6366f1;
  font-size: 1.5rem;
}

/* Empty state styling */
.datatable-v2 :deep(.p-datatable-emptymessage) {
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

/* Focus styles for accessibility */
.datatable-v2 :deep(.p-datatable-thead > tr > th:focus),
.datatable-v2 :deep(.p-datatable-tbody > tr > td:focus) {
  outline: 2px solid #6366f1;
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .datatable-v2 :deep(.p-datatable-thead) {
    background: #000;
    color: #fff;
  }

  .datatable-v2 :deep(.p-datatable-tbody > tr:nth-child(even)) {
    background: #f0f0f0;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .datatable-v2 :deep(.p-datatable-tbody > tr) {
    transition: none;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
