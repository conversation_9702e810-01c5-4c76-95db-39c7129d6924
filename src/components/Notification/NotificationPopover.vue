<script setup lang="ts">
import { useRouter } from "vue-router";

import type { Notification } from "@/api/bcare-types-v2";
import { useNotificationNavigation } from "@/composables/useNotificationNavigation";
import useNotification from "@/hooks/useNotification";
import { useNotificationStore } from "@/stores/notification-store";
import { formatRelativeTime } from "@/utils/time-helper";

const router = useRouter();
const popover = ref();
const headerMenu = ref();
const hasNewNotification = ref(false);
const notificationStore = useNotificationStore();
const {
  navigateToNotification,
  TaskUpdateForm,
  isTaskDrawerVisible,
  currentTaskId,
  closeTaskDrawer
} = useNotificationNavigation();

const {
  notifications,
  unreadCount,
  isLoading,
  getUnreadNotifications,
  loadNotifications,
  loadMore,
  markAsRead,
  markAllAsRead,
  handleNotificationClick,
  getCategoryStyle,
  getNotificationIcon,
  hasMorePages,
} = useNotification({
  autoLoad: false, // Changed to false - will load when popup opens
  autoLoadUnreadCount: true,
  pageSize: 10,
});

// Watch for new notifications from WebSocket
watch(() => notificationStore.unreadCount, (newCount, oldCount) => {
  if (newCount > oldCount) {
    hasNewNotification.value = true;
    // Remove animation after 3 seconds
    setTimeout(() => {
      hasNewNotification.value = false;
    }, 3000);
  }
});

// Handle popover open
const handlePopoverOpen = () => {
  // Load notifications when popup opens
  if (notifications.value.length === 0) {
    loadNotifications();
  }
  // Clear new notification indicator
  hasNewNotification.value = false;
};

// Format time for display
const formatTime = (date: string) => {
  return formatRelativeTime(date);
};

// Get notification category color
const getCategoryColor = (type: string) => {
  const style = getCategoryStyle(type);
  const colorMap: Record<string, string> = {
    blue: "bg-blue-500",
    green: "bg-success",
    red: "bg-danger",
    orange: "bg-warning",
    yellow: "bg-yellow-500",
    amber: "bg-amber-500",
    gray: "bg-slate-400",
  };
  return colorMap[style] || "bg-slate-400";
};

// Get PrimeVue icon class
const getNotificationIconClass = (type: string) => {
  const icon = getNotificationIcon(type);
  const iconMap: Record<string, string> = {
    UserPlus: "pi-user-plus",
    CheckCircle: "pi-check-circle",
    AlertCircle: "pi-exclamation-circle",
    Clock: "pi-clock",
    RefreshCw: "pi-refresh",
    AlertTriangle: "pi-exclamation-triangle",
    Bell: "pi-bell",
  };
  return iconMap[icon] || "pi-bell";
};

// Handle notification click with navigation
const onNotificationClick = async (notification: Notification) => {
  popover.value?.hide();

  await handleNotificationClick(notification);
};

const onNavigateToEntity = async (notification: Notification, event: Event) => {
  event.stopPropagation();
  popover.value?.hide();
  await navigateToNotification(notification);
};

const viewAllNotifications = () => {
  popover.value?.hide();
  router.push({ name: "top-menu-dashboard-user" });
};

// Toggle header menu
const toggleHeaderMenu = (event: Event) => {
  headerMenu.value?.toggle(event);
};

// Header menu items
const headerMenuItems = [
  {
    label: 'Đánh dấu tất cả đã đọc',
    icon: 'pi pi-eye',
    command: () => {
      markAllAsRead();
    }
  }
];
</script>

<template>
  <div class="intro-x mr-5">
    <div class="relative inline-block">
      <Button
        @click="popover.toggle($event); handlePopoverOpen()"
        variant="text"
        :class="[
          'text-white/70 hover:bg-transparent size-7',
          hasNewNotification && 'animate-shake text-red-500'
        ]"
        size="small"
        text
      >
        <i class="pi pi-bell text-lg" />
      </Button>
      <!-- Unread count badge -->
      <span
        v-if="notificationStore.unreadCount > 0"
        class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white"
      >
        {{ notificationStore.unreadCount > 99 ? '99+' : notificationStore.unreadCount }}
      </span>
    </div>

    <!-- Popover Content -->
    <Popover ref="popover" :showArrow="false" class="before:hidden after:hidden" >
      <div class=" w-[320px] sm:w-[400px]">
        <!-- Header -->
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-lg font-medium">Thông báo</h3>
          <div class="flex items-center gap-2">
            <Button
              v-if="getUnreadNotifications.length > 0"
              type="button"
              icon="pi pi-ellipsis-v"
              @click="toggleHeaderMenu"
              text
              rounded
              size="small"
              aria-haspopup="true"
              aria-controls="header_menu"
            />
            <Menu
              ref="headerMenu"
              id="header_menu"
              :model="headerMenuItems"
              :popup="true"
            />
          </div>
        </div>

        <!-- Loading state -->
        <div v-if="isLoading && notifications.length === 0" class="py-8 text-center">
          <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
          <div class="mt-2 text-sm text-slate-500">Đang tải thông báo...</div>
        </div>

        <!-- Empty state -->
        <div v-else-if="!isLoading && notifications.length === 0" class="py-8 text-center">
          <i class="pi pi-bell mx-auto text-5xl text-slate-300" />
          <div class="mt-2 text-sm text-slate-500">Không có thông báo nào</div>
        </div>

        <!-- Notifications list -->
        <div v-else class="max-h-[400px] space-y-2 overflow-y-auto">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            @click="onNotificationClick(notification)"
            :class="[
              'relative flex cursor-pointer items-start gap-3 rounded-lg p-3 transition-colors',
              notification.is_read
                ? 'hover:bg-slate-50 dark:hover:bg-darkmode-700'
                : 'bg-primary/5 hover:bg-primary/10 dark:bg-primary/10 dark:hover:bg-primary/20'
            ]"
          >
            <!-- Icon -->
            <div
              :class="[
                'mt-0.5 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full text-white',
                getCategoryColor(notification.type)
              ]"
            >
              <i :class="['pi', getNotificationIconClass(notification.type), 'text-sm']" />
            </div>

            <!-- Content -->
            <div class="min-w-0 flex-1">
              <p
                :class="[
                  'text-sm',
                  !notification.is_read && 'font-medium'
                ]"
              >
                {{ notification.message }}
              </p>
              <p class="mt-1 text-xs text-slate-500">
                {{ formatTime(notification.created_at) }}
              </p>
            </div>

            <!-- Action button -->
            <Button
              v-if="notification.entity_type && notification.entity_id"
              @click="onNavigateToEntity(notification, $event)"
              text
              size="small"
              class="text-primary hover:bg-primary/10 size-7 self-center"
              :class="{ 'opacity-70': notification.is_read }"
            >
              <i class="pi pi-external-link text-xs" />
            </Button>

            <!-- Unread indicator -->
            <div
              v-if="!notification.is_read"
              class="absolute right-2 top-2 h-2 w-2 rounded-full bg-primary"
            />
          </div>
        </div>

        <!-- Footer -->
        <div class="mt-4 flex items-center justify-between border-t pt-3">
          <Button
            v-if="hasMorePages"
            @click="loadMore"
            :disabled="isLoading"
            text
            size="small"
            class="text-primary"
          >
            <ProgressSpinner v-if="isLoading" style="width: 20px; height: 20px" strokeWidth="4" />
            <span v-else>Tải thêm</span>
          </Button>

          <Button
            @click="viewAllNotifications"
            outlined
            severity="secondary"
            size="small"
            class="ml-auto text-primary"
          >
            Xem tất cả thông báo →
          </Button>
        </div>
      </div>
    </Popover>

    <!-- Task Drawer for notifications -->
    <TaskUpdateForm
      v-if="isTaskDrawerVisible && currentTaskId"
      :taskId="currentTaskId"
      @close="closeTaskDrawer"
      @reload-data="() => {}"
    />
  </div>
</template>
