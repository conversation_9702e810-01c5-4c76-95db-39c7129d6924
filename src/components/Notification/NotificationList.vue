<script setup lang="ts">
import { useRouter } from "vue-router";

import type { Notification } from "@/api/bcare-types-v2";
import { useNotificationNavigation } from "@/composables/useNotificationNavigation";
import useNotification from "@/hooks/useNotification";
import { formatSmartDateTime } from "@/utils/time-helper";

const { navigateToNotification } = useNotificationNavigation();

const props = defineProps<{
  embedded?: boolean;
  hideTitle?: boolean;
  hideHeaderButtons?: boolean;
  maxHeight?: string;
  paginationMode?: "loadMore" | "paginator";
  pageSize?: number;
}>();

// Notification state
const {
  notifications,
  unreadCount,
  isLoading,
  getUnreadNotifications,
  getReadNotifications,
  loadNotifications,
  loadMore,
  goToPage,
  markAsRead,
  markAllAsRead,
  handleNotificationClick,
  getCategoryStyle,
  getNotificationIcon,
  hasMorePages,
  totalNotifications,
  currentPage,
  totalPages,
  pageSize: currentPageSize,
} = useNotification({
  autoLoad: true,
  autoLoadUnreadCount: true,
  pageSize: props.pageSize || 20,
  paginationMode: props.paginationMode || (props.embedded ? "loadMore" : "paginator"),
});

// UI state
const filterType = ref<"all" | "unread" | "read">("all");
const searchQuery = ref("");
const sortBy = ref<"newest" | "oldest">("newest");

// Pagination mode
const usePaginator = computed(
  () => (props.paginationMode || (props.embedded ? "loadMore" : "paginator")) === "paginator",
);

// Pagination handlers
const handlePageChange = async (event: any) => {
  if (!goToPage) return;

  // Handle page navigation
  const page = event.page + 1; // PrimeVue uses 0-based indexing
  const onlyUnread = filterType.value === "unread";
  await goToPage(page, onlyUnread);
};

// Paginator text
const paginatorText = computed(() => {
  const total = totalNotifications.value || 0;
  const page = currentPage.value || 1;
  const size = currentPageSize.value || 20;

  if (total === 0) return "0 thông báo";

  const first = (page - 1) * size + 1;
  const last = Math.min(first + size - 1, total);

  return `Hiển thị ${first} - ${last} của ${total} thông báo`;
});

// Filtered notifications
const filteredNotifications = computed(() => {
  let result = notifications.value;

  // Filter by read status
  if (filterType.value === "unread") {
    result = getUnreadNotifications.value;
  } else if (filterType.value === "read") {
    result = getReadNotifications.value;
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter((n) => n.message.toLowerCase().includes(query));
  }

  // Sort
  if (sortBy.value === "oldest") {
    result = [...result].reverse();
  }

  return result;
});

// Watch for filter changes and reload data when using paginator
watch([filterType], async () => {
  if (usePaginator.value && goToPage) {
    const onlyUnread = filterType.value === "unread";
    await goToPage(1, onlyUnread);
  }
});

const handleNotificationClicked = async (notification: Notification) => {
  // If embedded, only mark as read without navigation
  if (props.embedded) {
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }
  } else {
    // Full handling including navigation
    await handleNotificationClick(notification);
  }
};

// Navigate to entity page
const onNavigateToEntity = async (notification: Notification, event: Event) => {
  event.stopPropagation();
  await navigateToNotification(notification);
};

// Get notification category color
const getCategoryColor = (type: string) => {
  const style = getCategoryStyle(type);
  const colorMap: Record<string, string> = {
    blue: "bg-blue-500",
    green: "bg-success",
    red: "bg-danger",
    orange: "bg-warning",
    yellow: "bg-yellow-500",
    amber: "bg-amber-500",
    gray: "bg-slate-400",
  };
  return colorMap[style] || "bg-slate-400";
};

// Get PrimeVue icon class
const getNotificationIconClass = (type: string) => {
  const icon = getNotificationIcon(type);
  const iconMap: Record<string, string> = {
    UserPlus: "pi-user-plus",
    CheckCircle: "pi-check-circle",
    AlertCircle: "pi-exclamation-circle",
    Clock: "pi-clock",
    RefreshCw: "pi-refresh",
    AlertTriangle: "pi-exclamation-triangle",
    Bell: "pi-bell",
  };
  return iconMap[icon] || "pi-bell";
};
</script>

<template>
  <div class="notification-list flex h-full flex-col">
    <!-- Fixed Header -->
    <div
      v-if="!hideTitle || !hideHeaderButtons"
      class="flex-shrink-0 bg-white dark:bg-darkmode-700"
    >
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div v-if="!hideTitle" class="mb-4">
          <h2 class="text-xl font-medium">Thông báo</h2>
          <p class="text-sm text-slate-500">
            Bạn có {{ unreadCount }} thông báo chưa đọc trong tổng số {{ totalNotifications }}
          </p>
        </div>
      </div>

      <!-- Filters -->
      <div class="mb-4 flex flex-col gap-3 sm:flex-row sm:items-center">
        <!-- Filter buttons -->
        <div v-if="!hideHeaderButtons" class="flex w-full items-center justify-between">
          <div class="flex gap-2">
            <Button
              :severity="filterType === 'all' ? 'primary' : 'secondary'"
              :outlined="filterType !== 'all'"
              @click="filterType = 'all'"
            >
              Tất cả
            </Button>
            <Button
              :severity="filterType === 'unread' ? 'primary' : 'secondary'"
              :outlined="filterType !== 'unread'"
              @click="filterType = 'unread'"
            >
              Chưa đọc ({{ unreadCount }})
            </Button>
            <Button
              :severity="filterType === 'read' ? 'primary' : 'secondary'"
              :outlined="filterType !== 'read'"
              @click="filterType = 'read'"
            >
              Đã đọc
            </Button>
          </div>

          <Button
            v-if="getUnreadNotifications.length > 0"
            outlined
            severity="warn"
            @click="markAllAsRead"
          >
            <i class="pi pi-check-circle mr-2" />
            Đánh dấu tất cả đã đọc
          </Button>
        </div>

        <div class="flex items-center gap-4">
          <!-- Sort and Pagination -->
          <Select
            v-model="sortBy"
            :options="[
              { label: 'Mới nhất', value: 'newest' },
              { label: 'Cũ nhất', value: 'oldest' },
            ]"
            optionLabel="label"
            optionValue="value"
            class="w-auto"
          />
        </div>
      </div>
    </div>

    <!-- Scrollable Content -->
    <ScrollPanel :style="`width: 100%; ${maxHeight ? `height: ${maxHeight}` : ''}`">
      <!-- Loading state -->
      <div v-if="isLoading && notifications.length === 0" class="py-20 text-center">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <div class="mt-3 text-sm text-slate-500">Đang tải thông báo...</div>
      </div>

      <!-- Empty state -->
      <div v-else-if="!isLoading && filteredNotifications.length === 0" class="py-20 text-center">
        <i class="pi pi-bell mb-3 block text-6xl text-slate-300" />
        <h3 class="mb-1 text-lg font-medium">Không có thông báo</h3>
        <p class="text-sm text-slate-500">
          {{ searchQuery ? "Không tìm thấy thông báo phù hợp" : "Bạn chưa có thông báo nào" }}
        </p>
      </div>

      <!-- Notifications list -->
      <div v-else class="space-y-2">
        <!-- Notification items -->
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          :class="[
            'relative flex items-start gap-3 p-3 transition-colors hover:bg-slate-50 dark:hover:bg-darkmode-700',
            !notification.is_read && 'bg-primary/5 dark:bg-primary/10',
          ]"
        >
          <!-- Icon -->
          <div
            :class="[
              'flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full text-white',
              getCategoryColor(notification.type),
            ]"
          >
            <i :class="['pi', getNotificationIconClass(notification.type)]" />
          </div>

          <!-- Content -->
          <div
            class="min-w-0 flex-1 cursor-pointer"
            @click="handleNotificationClicked(notification)"
          >
            <p :class="['text-sm', !notification.is_read && 'font-medium']">
              {{ notification.message }}
            </p>
            <div class="mt-1 flex items-center gap-3 text-xs text-slate-500">
              <span>{{ formatSmartDateTime(notification.created_at) }}</span>
              <span v-if="notification.entity_type" class="capitalize">
                • {{ notification.entity_type }}
              </span>
            </div>
          </div>

          <!-- Action button -->
          <Button
            v-if="notification.entity_type && notification.entity_id"
            @click="onNavigateToEntity(notification, $event)"
            text
            size="small"
            class="size-8 flex-shrink-0 self-center text-primary hover:bg-primary/10"
            :class="{ 'opacity-70': notification.is_read }"
          >
            <i class="pi pi-external-link text-sm" />
          </Button>

          <!-- Unread indicator -->
          <div v-if="!notification.is_read" class="absolute left-0 top-0 h-full w-1 bg-primary" />
        </div>

        <!-- Load more (for embedded mode or loadMore pagination) -->
        <div v-if="!usePaginator && hasMorePages" class="p-4 text-center">
          <Button severity="primary" outlined @click="loadMore" :disabled="isLoading">
            <ProgressSpinner v-if="isLoading" style="width: 20px; height: 20px" strokeWidth="4" />
            <span v-else>Tải thêm</span>
          </Button>
        </div>
      </div>
    </ScrollPanel>

    <!-- Paginator (for non-embedded mode) -->
    <div
      v-if="usePaginator && totalNotifications > 0"
      class="relative flex items-center justify-center border-t border-gray-200 bg-white px-4 pt-4 dark:bg-darkmode-700"
    >
      <span class="absolute left-4 whitespace-nowrap text-sm text-slate-500">
        {{ paginatorText }}
      </span>

      <div
        class="[&_.p-paginator-content]:flex [&_.p-paginator-content]:w-max [&_.p-paginator-content]:items-center [&_.p-paginator-content]:gap-2 [&_.p-paginator-icon]:!text-sm [&_.p-paginator-next:hover]:bg-slate-100 [&_.p-paginator-next]:!size-10 [&_.p-paginator-next]:!min-w-0 [&_.p-paginator-next]:rounded-full [&_.p-paginator-next]:!p-0 [&_.p-paginator-next]:!text-sm [&_.p-paginator-page.p-highlight]:!bg-blue-500 [&_.p-paginator-page.p-highlight]:!text-white [&_.p-paginator-page:hover]:bg-slate-100 [&_.p-paginator-page]:!size-10 [&_.p-paginator-page]:!min-w-0 [&_.p-paginator-page]:rounded-full [&_.p-paginator-page]:!p-0 [&_.p-paginator-page]:!text-sm [&_.p-paginator-prev:hover]:bg-slate-100 [&_.p-paginator-prev]:!size-10 [&_.p-paginator-prev]:!min-w-0 [&_.p-paginator-prev]:rounded-full [&_.p-paginator-prev]:!p-0 [&_.p-paginator-prev]:!text-sm [&_.p-paginator]:border-0 [&_.p-paginator]:bg-transparent [&_.p-paginator]:p-0"
      >
        <Paginator
          :first="(currentPage - 1) * currentPageSize"
          :rows="currentPageSize"
          :totalRecords="totalNotifications"
          @page="handlePageChange"
          template="PrevPageLink PageLinks NextPageLink"
        />
      </div>
    </div>
  </div>
</template>
