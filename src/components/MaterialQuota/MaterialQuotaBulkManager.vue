<template>
  <div class="flex flex-col gap-6">
    <!-- Header -->
    <div class="flex flex-col gap-4">
      <div>
        <h3 class="text-lg font-semibold">Qu<PERSON>n lý vật tư hàng loạt</h3>
        <p class="text-muted-foreground mt-1 text-sm">
          Thi<PERSON><PERSON> lập định mức vật tư cho thao tác một cách nhanh chóng
        </p>
      </div>

      <!-- Operation Selection -->
      <div class="flex flex-col gap-2">
        <label class="text-sm font-medium">
          Chọn thao tác <span class="text-danger">*</span>
        </label>
        <Select
          v-model="selectedOperationId"
          :options="operationOptions"
          option-label="name"
          option-value="id"
          placeholder="Chọn thao tác để quản lý vật tư"
          class="w-full"
          @change="handleOperationChange"
        />
      </div>
    </div>

    <!-- Material Management Section -->
    <div v-if="selectedOperationId" class="flex flex-col gap-4">
      <!-- Add Material Section -->
      <div class="rounded-lg bg-gray-50 p-4">
        <h4 class="text-md mb-3 font-medium">Thêm vật tư</h4>
        <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
          <Select
            v-model="newMaterial.material_id"
            :options="availableMaterials"
            option-label="name"
            option-value="id"
            placeholder="Chọn vật tư"
            class="w-full"
            filter
            show-clear
          />
          <InputNumber
            v-model="newMaterial.quantity"
            placeholder="Số lượng"
            class="w-full"
            :min="0"
            :max-fraction-digits="2"
            :step="0.1"
          />
          <Button
            label="Thêm"
            icon="pi pi-plus"
            @click="addMaterial"
            :disabled="!newMaterial.material_id || !newMaterial.quantity"
            class="p-button-primary"
          />
        </div>
      </div>

      <!-- Current Materials List -->
      <div class="rounded-lg border border-gray-200">
        <div class="border-b border-gray-200 bg-gray-50 p-4">
          <h4 class="text-md font-medium">Vật tư hiện tại ({{ currentMaterials.length }})</h4>
        </div>

        <div v-if="currentMaterials.length === 0" class="p-8 text-center">
          <i class="pi pi-box text-muted-foreground mb-3 text-3xl"></i>
          <p class="text-muted-foreground">Chưa có vật tư nào được thiết lập</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="(material, index) in currentMaterials"
            :key="`${material.material_id}-${index}`"
            class="flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <div class="flex items-center gap-3">
              <i class="pi pi-box text-primary"></i>
              <div>
                <div class="font-medium">
                  {{ getMaterialName(material.material_id) }}
                </div>
                <div class="text-muted-foreground text-sm">ID: {{ material.material_id }}</div>
              </div>
            </div>

            <div class="flex items-center gap-3">
              <InputNumber
                v-model="material.quantity"
                class="w-24"
                :min="0"
                :max-fraction-digits="2"
                :step="0.1"
                size="small"
              />
              <Button
                icon="pi pi-trash"
                class="p-button-text p-button-sm p-button-danger"
                @click="removeMaterial(index)"
                v-tooltip.top="'Xóa'"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-3 border-t border-gray-200 pt-4">
      <Button label="Hủy" class="p-button-text" @click="$emit('cancel')" />
      <Button
        label="Lưu thay đổi"
        class="p-button-primary"
        @click="handleSave"
        :disabled="!selectedOperationId"
        :loading="isLoading"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

import type { OperationMaterialSetItem } from "@/api/bcare-types-v2";
import useMaterialQuota from "@/hooks/useMaterialQuota";
import useOperation from "@/hooks/useOperation";
import useMaterial from "@/hooks/useMaterial";

interface Props {
  operationId?: number | null;
}

interface Emits {
  (e: "save"): void;
  (e: "cancel"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Composables
const {
  bulkSetMaterialQuotas,
  getMaterialQuotasForOperation,
  isLoading
} = useMaterialQuota({ autoLoad: false });
const { getAllOperations } = useOperation();
const { materials, getMaterialNameById } = useMaterial();

// State
const selectedOperationId = ref<number | null>(props.operationId || null);
const currentMaterials = ref<OperationMaterialSetItem[]>([]);
const newMaterial = ref<OperationMaterialSetItem>({
  material_id: 0,
  quantity: 1,
});

// Computed
const operationOptions = computed(() => getAllOperations.value);

const availableMaterials = computed(() => {
  const usedMaterialIds = currentMaterials.value.map((m) => m.material_id);
  return materials.value.filter((material) => !usedMaterialIds.includes(material.id));
});

// Methods
const getMaterialName = (materialId: number): string => {
  return getMaterialNameById(materialId) || `Vật tư #${materialId}`;
};

const loadCurrentMaterials = () => {
  if (!selectedOperationId.value) {
    currentMaterials.value = [];
    return;
  }

  const materialQuotas = getMaterialQuotasForOperation(selectedOperationId.value);
  currentMaterials.value = materialQuotas.map(mq => ({
    material_id: mq.material_id,
    quantity: mq.quantity,
  }));
};

const handleOperationChange = () => {
  loadCurrentMaterials();
};

const addMaterial = () => {
  if (!newMaterial.value.material_id || !newMaterial.value.quantity) {
    return;
  }

  // Check if material already exists
  const existingIndex = currentMaterials.value.findIndex(
    (m) => m.material_id === newMaterial.value.material_id,
  );

  if (existingIndex >= 0) {
    // Update existing material quantity
    currentMaterials.value[existingIndex].quantity = newMaterial.value.quantity;
  } else {
    // Add new material
    currentMaterials.value.push({ ...newMaterial.value });
  }

  // Reset form
  newMaterial.value = {
    material_id: 0,
    quantity: 1,
  };
};

const removeMaterial = (index: number) => {
  currentMaterials.value.splice(index, 1);
};

const handleSave = async () => {
  if (!selectedOperationId.value) {
    return;
  }

  try {
    await bulkSetMaterialQuotas({
      operation_id: selectedOperationId.value,
      materials: currentMaterials.value.filter((m) => m.quantity > 0),
    });
    emit("save");
  } catch (error) {
    console.error("Error saving bulk material quotas:", error);
  }
};

// Watchers
watch(
  () => props.operationId,
  (newOperationId) => {
    selectedOperationId.value = newOperationId || null;
    loadCurrentMaterials();
  },
  { immediate: true },
);

// Lifecycle
onMounted(() => {
  loadCurrentMaterials();
});
</script>
