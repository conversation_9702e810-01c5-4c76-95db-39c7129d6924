<template>
  <form @submit.prevent="handleSubmit" class="flex flex-col gap-6">
    <!-- Operation Selection -->
    <div class="flex flex-col gap-2">
      <label class="text-sm font-medium"> <PERSON><PERSON> tác <span class="text-danger">*</span> </label>
      <Select
        v-model="formData.operation_id"
        :options="operationOptions"
        option-label="name"
        option-value="id"
        placeholder="Chọn thao tác"
        class="w-full"
        :class="{ 'p-invalid': errors.operation_id }"
        :disabled="isEdit"
      />
      <small v-if="errors.operation_id" class="text-xs text-danger">
        {{ errors.operation_id }}
      </small>
    </div>

    <!-- Material Selection -->
    <div class="flex flex-col gap-2">
      <label class="text-sm font-medium"> Vật tư <span class="text-danger">*</span> </label>
      <Select
        v-model="formData.material_id"
        :options="materialOptions"
        option-label="name"
        option-value="id"
        placeholder="Chọn vật tư"
        class="w-full"
        :class="{ 'p-invalid': errors.material_id }"
        filter
        show-clear
      />
      <small v-if="errors.material_id" class="text-xs text-danger">
        {{ errors.material_id }}
      </small>
    </div>

    <!-- Quantity Input -->
    <div class="flex flex-col gap-2">
      <label class="text-sm font-medium"> Số lượng <span class="text-danger">*</span> </label>
      <InputNumber
        v-model="formData.quantity"
        placeholder="Nhập số lượng"
        class="w-full"
        :class="{ 'p-invalid': errors.quantity }"
        :min="0"
        :max-fraction-digits="2"
        :step="0.1"
      />
      <small v-if="errors.quantity" class="text-xs text-danger">
        {{ errors.quantity }}
      </small>
    </div>

    <!-- Status Selection -->
    <div class="flex flex-col gap-2">
      <label class="text-sm font-medium">Trạng thái</label>
      <SelectButton
        v-model="formData.status"
        :options="statusOptions"
        option-label="name"
        option-value="value"
        class="w-full"
      />
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end gap-3 border-t border-gray-200 pt-4">
      <Button type="button" label="Hủy" class="p-button-text" @click="$emit('cancel')" />
      <Button
        type="submit"
        :label="isEdit ? 'Cập nhật' : 'Thêm mới'"
        class="p-button-primary"
        :loading="isLoading"
      />
    </div>
  </form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

import type {
  OperationMaterialAddRequest,
  OperationMaterialResponse,
  OperationMaterialUpdateRequest,
} from "@/api/bcare-types-v2";
import { DEFAULT_OPERATION_MATERIAL, OPERATION_MATERIAL_STATUS_OPTIONS } from "@/api/bcare-enum";
import { useFormValidation } from "@/composables/useFormValidation";
import useMaterialQuota from "@/hooks/useMaterialQuota";
import useOperation from "@/hooks/useOperation";
import useMaterial from "@/hooks/useMaterial";

interface Props {
  materialQuota?: OperationMaterialResponse | null;
  operationId?: number | null;
}

interface Emits {
  (e: "save"): void;
  (e: "cancel"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Composables
const { addMaterialQuota, updateMaterialQuota, isLoading } = useMaterialQuota({ autoLoad: false });
const { getAllOperations } = useOperation();
const { materials } = useMaterial();

// Form data
const formData = ref<OperationMaterialAddRequest>({
  operation_id: props.operationId || DEFAULT_OPERATION_MATERIAL.operation_id,
  material_id: DEFAULT_OPERATION_MATERIAL.material_id,
  quantity: DEFAULT_OPERATION_MATERIAL.quantity,
  status: DEFAULT_OPERATION_MATERIAL.status,
});

// Computed
const isEdit = computed(() => !!props.materialQuota);
const operationOptions = computed(() => getAllOperations.value);
const materialOptions = computed(() => materials.value);
const statusOptions = computed(() => OPERATION_MATERIAL_STATUS_OPTIONS);

// Validation rules
const validationRules = {
  operation_id: [
    {
      validate: (value: any) => !!value && value > 0,
      message: "Vui lòng chọn thao tác",
    },
  ],
  material_id: [
    {
      validate: (value: any) => !!value && value > 0,
      message: "Vui lòng chọn vật tư",
    },
  ],
  quantity: [
    {
      validate: (value: any) => value !== null && value !== undefined && value > 0,
      message: "Số lượng phải lớn hơn 0",
    },
  ],
};

const { errors, validateForm, clearErrors } = useFormValidation(validationRules);

// Methods
const resetForm = () => {
  formData.value = {
    operation_id: props.operationId || DEFAULT_OPERATION_MATERIAL.operation_id,
    material_id: DEFAULT_OPERATION_MATERIAL.material_id,
    quantity: DEFAULT_OPERATION_MATERIAL.quantity,
    status: DEFAULT_OPERATION_MATERIAL.status,
  };
  clearErrors();
};

const loadFormData = () => {
  if (props.materialQuota) {
    formData.value = {
      operation_id: props.materialQuota.operation_id,
      material_id: props.materialQuota.material_id,
      quantity: props.materialQuota.quantity,
      status: props.materialQuota.status,
    };
  } else {
    resetForm();
  }
  clearErrors();
};

const handleSubmit = async () => {
  if (!validateForm(formData.value)) {
    return;
  }

  try {
    if (isEdit.value && props.materialQuota) {
      const updateRequest: OperationMaterialUpdateRequest = {
        id: props.materialQuota.id,
        operation_id: formData.value.operation_id,
        material_id: formData.value.material_id,
        quantity: formData.value.quantity,
        status: formData.value.status,
      };
      await updateMaterialQuota(updateRequest);
    } else {
      await addMaterialQuota(formData.value);
    }
    emit("save");
  } catch (error) {
    console.error("Error saving material quota:", error);
  }
};

// Watchers
watch(
  () => props.materialQuota,
  () => {
    loadFormData();
  },
  { immediate: true },
);

watch(
  () => props.operationId,
  (newOperationId) => {
    if (newOperationId && !isEdit.value) {
      formData.value.operation_id = newOperationId;
    }
  },
  { immediate: true },
);

// Lifecycle
onMounted(() => {
  loadFormData();
});
</script>
