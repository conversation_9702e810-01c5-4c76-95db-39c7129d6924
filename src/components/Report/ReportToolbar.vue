<script setup lang="ts">
import DatePicker from "primevue/datepicker";
import <PERSON><PERSON><PERSON>ield from "primevue/iconfield";
import InputIcon from "primevue/inputicon";
import InputText from "primevue/inputtext";
import { computed } from "vue";

interface Props {
  title: string;
  totalRecords?: number;
  showSearch?: boolean;
  showDateRange?: boolean;
  searchPlaceholder?: string;
  dateRangePlaceholder?: string;
}

interface FilterValues {
  search?: string;
  dateRange?: Date[] | null;
}

const props = withDefaults(defineProps<Props>(), {
  totalRecords: 0,
  showSearch: true,
  showDateRange: true,
  searchPlaceholder: "Tìm kiếm...",
  dateRangePlaceholder: "Chọn khoảng thời gian",
});

const modelValue = defineModel<FilterValues>({
  default: () => ({
    search: "",
    dateRange: null,
  }),
});

// Computed properties for v-model binding
const searchValue = computed({
  get: () => modelValue.value.search || "",
  set: (value: string) => {
    modelValue.value = {
      ...modelValue.value,
      search: value,
    };
  },
});

const dateRangeValue = computed({
  get: () => modelValue.value.dateRange || null,
  set: (value: Date[] | null) => {
    modelValue.value = {
      ...modelValue.value,
      dateRange: value,
    };
  },
});
</script>

<template>
  <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
    <!-- Left side: Title and record count -->
    <div class="flex items-center gap-2 border-r border-surface-200 pr-4">
      <Button severity="primary" icon="pi pi-list" label="Tất cả" />
    </div>

    <!-- Right side: Filter controls -->
    <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
      <!-- Search input -->
      <div v-if="showSearch" class="w-full sm:w-auto">
        <IconField class="h-10 w-full sm:w-64">
          <InputIcon class="pi pi-search" />
          <InputText
            v-model="searchValue"
            type="text"
            class="h-full w-full"
            :placeholder="searchPlaceholder"
            size="small"
            fluid
          />
        </IconField>
      </div>

      <!-- Date range picker -->
      <div v-if="showDateRange" class="w-full sm:w-auto">
        <DatePicker
          v-model="dateRangeValue"
          class="h-10 w-full sm:w-64"
          show-icon
          selection-mode="range"
          manual-input
          date-format="dd/mm/yy"
          :placeholder="dateRangePlaceholder"
          size="small"
          fluid
          show-other-months
          select-other-months
        />
      </div>

      <!-- Additional filter slot -->
      <slot name="additional-filters" />
    </div>
  </div>
</template>

<style scoped>
/* Custom styles if needed */
</style>
