import { defineStore } from "pinia";
import { computed, ref, shallowRef } from "vue";

import { 
  cashflowList,
  cashflowSummary,
  cashflowAdd,
  cashflowUpdate,
  cashflowDelete,
  cashflowUpdateState,
  cashflowGet,
  cashflow_noteAdd,
  cashflow_noteUpdate,
  cashflow_noteDelete,
  cashflow_noteList,
  cashflowExport
} from "@/api/bcare-v2";
import type {
  CashFlow,
  CashFlowAddRequest,
  CashFlowDeleteRequest,
  CashFlowGetRequest,
  CashFlowListRequest,
  CashFlowNoteAddRequest,
  CashFlowNoteDeleteRequest,
  CashFlowNoteListRequest,
  CashFlowNoteUpdateRequest,
  CashFlowReportRequest,
  CashFlowSummaryReport,
  CashFlowUpdateRequest,
  CashFlowUpdateStateRequest,
  CashFlowWithRelations,
} from "@/api/bcare-types-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useCashFlowStore = defineStore("cashFlow", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const cashflows = shallowRef<CashFlowWithRelations[]>([]);
  const currentCashFlow = shallowRef<CashFlowWithRelations | null>(null);
  const summary = ref<CashFlowSummaryReport>({
    total_income: 0,
    total_expense: 0,
    net_amount: 0,
    pending_approval_count: 0,
    pending_approval_amount: 0,
    total_cash_all: 0,
    total_credit_card_all: 0,
    total_mpos_all: 0,
    total_bank_all: 0,
    total_momo_all: 0,
    pending_cash: 0,
    pending_credit_card: 0,
    pending_mpos: 0,
    pending_bank: 0,
    pending_momo: 0,
    income_by_category: [],
    expense_by_category: [],
  });
  const totalRecords = ref<number | null>(null);
  const totalPages = ref<number | null>(null);

  // Getters
  const getCashFlowCount = computed(() => cashflows.value.length);
  const getCashFlowById = computed(
    () => (id: number) => cashflows.value.find((cashflow) => cashflow.id === id),
  );
  const getPendingCashFlows = computed(() =>
    cashflows.value.filter((cashflow) => cashflow.state === "PENDING"),
  );
  const getApprovedCashFlows = computed(() =>
    cashflows.value.filter((cashflow) => cashflow.state === "APPROVED"),
  );

  // Actions
  function fetchCashFlows(req: CashFlowListRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowList(req);
      if (response.data) {
        setCashFlows(response.data.cash_flows as CashFlowWithRelations[]);
        setTotalRecords(response.data.total);
        setTotalPages(response.data.total_page);
      }
      return response;
    });
  }

  function fetchCashFlowSummary(req?: CashFlowReportRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowSummary(req || {});
      if (response.data) {
        setSummary({
          total_income: response.data.total_income,
          total_expense: response.data.total_expense,
          net_amount: response.data.net_amount,
          pending_approval_count: response.data.pending_approval_count,
          pending_approval_amount: response.data.pending_approval_amount,
          total_cash_all: response.data.total_cash_all || 0,
          total_credit_card_all: response.data.total_credit_card_all || 0,
          total_mpos_all: response.data.total_mpos_all || 0,
          total_bank_all: response.data.total_bank_all || 0,
          total_momo_all: response.data.total_momo_all || 0,
          pending_cash: response.data.pending_cash || 0,
          pending_credit_card: response.data.pending_credit_card || 0,
          pending_mpos: response.data.pending_mpos || 0,
          pending_bank: response.data.pending_bank || 0,
          pending_momo: response.data.pending_momo || 0,
          income_by_category: response.data.income_by_category,
          expense_by_category: response.data.expense_by_category,
        });
      }
      return response;
    });
  }

  function addCashFlow(req: CashFlowAddRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowAdd(req);
      if (response.data) {
        // Refresh the list after adding
        await fetchCashFlows({ page: 1, page_size: 20 });
      }
      return response;
    });
  }

  function updateCashFlow(req: CashFlowUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowUpdate(req);
      if (response.data) {
        // Update the local state
        const index = cashflows.value.findIndex((cf) => cf.id === req.id);
        if (index !== -1) {
          // Fetch updated data
          const updatedCashFlow = await getCashFlow(req.id);
          if (updatedCashFlow.data) {
            cashflows.value[index] = updatedCashFlow.data as CashFlowWithRelations;
          }
        }
      }
      return response;
    });
  }

  function deleteCashFlow(req: CashFlowDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowDelete(req);
      if (response.data) {
        // Remove from local state
        cashflows.value = cashflows.value.filter((cf) => cf.id !== req.id);
        if (totalRecords.value) {
          setTotalRecords(totalRecords.value - 1);
        }
      }
      return response;
    });
  }

  function updateCashFlowState(req: CashFlowUpdateStateRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowUpdateState(req);
      if (response.data) {
        // Update the local state
        const index = cashflows.value.findIndex((cf) => cf.id === req.id);
        if (index !== -1) {
          const updatedCashFlow = await getCashFlow(req.id);
          if (updatedCashFlow.data) {
            cashflows.value[index] = updatedCashFlow.data as CashFlowWithRelations;
          }
        }
        // Update current cash flow if it's the same one
        if (currentCashFlow.value?.id === req.id) {
          setCurrentCashFlow(cashflows.value[index]);
        }
      }
      return response;
    });
  }

  function getCashFlow(id: number, includeRelation: boolean = true) {
    return performAsyncAction(async () => {
      const req: CashFlowGetRequest = { id, include_relation: includeRelation };
      const response = await cashflowGet(req);
      if (response.data) {
        setCurrentCashFlow(response.data as CashFlowWithRelations);
      }
      return response;
    });
  }

  // Note Actions
  function addCashFlowNote(req: CashFlowNoteAddRequest) {
    return performAsyncAction(async () => {
      const response = await cashflow_noteAdd(req);
      return response;
    });
  }

  function updateCashFlowNote(req: CashFlowNoteUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await cashflow_noteUpdate(req);
      return response;
    });
  }

  function deleteCashFlowNote(req: CashFlowNoteDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await cashflow_noteDelete(req);
      return response;
    });
  }

  function fetchCashFlowNotes(req: CashFlowNoteListRequest) {
    return performAsyncAction(async () => {
      const response = await cashflow_noteList(req);
      return response;
    });
  }

  function exportCashFlows(req: CashFlowListRequest) {
    return performAsyncAction(async () => {
      const response = await cashflowExport(req);
      return response;
    });
  }

  // Helper functions for state management
  function setCashFlows(newCashFlows: CashFlowWithRelations[]) {
    cashflows.value = newCashFlows;
  }

  function setCurrentCashFlow(cashflow: CashFlowWithRelations | null) {
    currentCashFlow.value = cashflow;
  }

  function setSummary(newSummary: CashFlowSummaryReport) {
    summary.value = newSummary;
  }

  function setTotalRecords(total: number) {
    totalRecords.value = total;
  }

  function setTotalPages(pages: number) {
    totalPages.value = pages;
  }

  function clearCashFlows() {
    cashflows.value = [];
    setCurrentCashFlow(null);
    setTotalRecords(0);
    setTotalPages(0);
  }

  return {
    // State
    cashflows,
    currentCashFlow,
    summary,
    totalRecords,
    totalPages,
    isLoading,
    error,

    // Getters
    getCashFlowCount,
    getCashFlowById,
    getPendingCashFlows,
    getApprovedCashFlows,

    // Actions
    fetchCashFlows,
    fetchCashFlowSummary,
    addCashFlow,
    updateCashFlow,
    deleteCashFlow,
    updateCashFlowState,
    getCashFlow,

    // Note Actions
    addCashFlowNote,
    updateCashFlowNote,
    deleteCashFlowNote,
    fetchCashFlowNotes,

    // Export Actions
    exportCashFlows,

    // Helper functions
    setCashFlows,
    setCurrentCashFlow,
    setSummary,
    setTotalRecords,
    setTotalPages,
    clearCashFlows,
  };
});
