import { defineStore } from "pinia";
import { computed, ref } from "vue";

import type { Material, OperationMaterialResponse } from "@/api/bcare-types-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { roundTo5Decimals } from "@/utils/number";

/**
 * Material Usage Matrix Cell Data
 * Represents a single cell in the material usage matrix
 */
export interface MaterialUsageCell {
  /** Base quantity per unit (always stored) */
  baseQuantity: number;
  /** Current display quantity (base or multiplied) */
  usedQuantity: number;
  /** Original quota quantity from operation */
  quotedQuantity: number;
  /** Product quantity for this operation */
  productQuantity: number;
  /** Whether this cell has been modified by user */
  isDirty: boolean;
  /** Whether this material was manually added (not from quota) */
  isCustom: boolean;
  /** Existing material usage ID for edit mode */
  existingId?: number;
}

/**
 * Material Usage Matrix Structure
 * [materialId][operationId] = MaterialUsageCell
 * Updated to support both number (standard operations) and string (custom operations) IDs
 */
export interface MaterialUsageMatrix {
  [materialId: number]: {
    [operationId: number | string]: MaterialUsageCell;
  };
}

/**
 * Product information for operation mapping
 */
export interface ProductInfo {
  productId: number;
  quantity: number;
  attachmentId: number;
}

/**
 * Material Usage Matrix Store
 * Manages state for Material Usage V2 system with per-column multiplier support
 */
export const useMaterialUsageMatrixStore = defineStore("materialUsageMatrix", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  /** Currently selected operation IDs - supports both standard (number) and custom (string) operations */
  const selectedOperations = ref<(number | string)[]>([]);

  /** Main matrix data structure */
  const usageMatrix = ref<MaterialUsageMatrix>({});

  /** Mapping of operation ID to product information */
  const operationProductMap = ref<Record<number | string, ProductInfo>>({});

  /** Per-operation toggle state for multiplier */
  const operationToggleState = ref<Record<number | string, boolean>>({});

  /** Operations that were removed (for API deletion in edit mode) */
  const removedOperations = ref<Set<number | string>>(new Set());

  /** Whether we're in edit mode */
  const isEditMode = ref<boolean>(false);

  /** Current attachment ID */
  const attachmentId = ref<number | null>(null);

  /** Material metadata cache */
  const materialMetadata = ref<
    Record<number, { id: number; name: string; code?: string; unit?: string }>
  >({});

  /** Track which materials are custom (manually added by user) */
  const customMaterials = ref<Set<number>>(new Set());

  /** Check if there are any dirty (unsaved) changes */
  const hasChanges = computed(() => {
    return Object.values(usageMatrix.value).some((materialOps) =>
      Object.values(materialOps).some((cell) => (cell as MaterialUsageCell).isDirty),
    );
  });

  /** Get all materials that have usage data */
  const materialsWithUsage = computed(() => {
    return Object.keys(usageMatrix.value).map((id) => Number(id));
  });

  /** Check if specific operation is toggled for multiplier */
  const isOperationToggled = computed(() => (operationId: number | string): boolean => {
    return operationToggleState.value[operationId] || false;
  });

  /** Get product quantity for specific operation */
  const getProductQuantity = computed(() => (operationId: number | string): number => {
    const productInfo = operationProductMap.value[operationId];
    return productInfo?.quantity || 1;
  });

  /**
   * Initialize store for new usage session
   * @param editMode - Whether in edit mode
   * @param currentAttachmentId - Current attachment ID
   */
  const initialize = (editMode: boolean = false, currentAttachmentId: number | null = null) => {
    isEditMode.value = editMode;
    attachmentId.value = currentAttachmentId;

    selectedOperations.value = [];
    usageMatrix.value = {};
    operationProductMap.value = {};
    operationToggleState.value = {};
    removedOperations.value.clear();
    customMaterials.value.clear();
  };

  /**
   * Update selected operations and rebuild matrix
   * @param operations - Array of operation IDs
   * @param replace - Whether to replace (true) or merge (false, default)
   */
  const updateOperations = (operations: (number | string)[], replace: boolean = false) => {
    if (replace) {
      const currentOps = new Set(selectedOperations.value);
      const newOps = new Set(operations);

      currentOps.forEach((opId) => {
        if (!newOps.has(opId)) {
          removedOperations.value.add(opId);
          Object.values(usageMatrix.value).forEach((materialOps) => {
            delete materialOps[opId];
          });
          delete operationToggleState.value[opId];
          delete operationProductMap.value[opId];
        }
      });

      selectedOperations.value = operations;
    } else {
      // MERGE mode: Add new operations while preserving existing ones
      const currentOps = new Set(selectedOperations.value);
      const newOps = new Set(operations);

      newOps.forEach((opId) => {
        if (!currentOps.has(opId)) {
          currentOps.add(opId);
        }
      });

      selectedOperations.value = Array.from(currentOps);
    }
  };

  /**
   * Set product information for an operation
   * @param operationId - Operation ID
   * @param productInfo - Product information
   */
  const setOperationProduct = (operationId: number | string, productInfo: ProductInfo) => {
    const oldQuantity = operationProductMap.value[operationId]?.quantity;
    const newQuantity = productInfo.quantity;

    operationProductMap.value[operationId] = productInfo;

    if (oldQuantity !== newQuantity) {
      Object.values(usageMatrix.value).forEach((materialOps) => {
        const cell = materialOps[operationId];
        if (cell) {
          cell.productQuantity = newQuantity;

          const isToggled = operationToggleState.value[operationId] || false;
          if (isToggled) {
            cell.usedQuantity = roundTo5Decimals(cell.baseQuantity * newQuantity);
          }
        }
      });
    }
  };

  /**
   * Cache material metadata for edit mode
   * @param materials - Available materials
   */
  const cacheMaterialMetadata = (materials: Material[]) => {
    materials.forEach((material) => {
      materialMetadata.value[material.id] = {
        id: material.id,
        name: material.name,
        code: material.code,
        unit: material.unit,
      };
    });
  };

  /**
   * Build matrix from materials and quotas for create mode
   * @param materials - Available materials
   * @param quotas - Material quotas from operations
   */
  const buildMatrix = (materials: Material[], quotas: OperationMaterialResponse[]) => {
    const newMatrix: MaterialUsageMatrix = {};

    materials.forEach((material) => {
      materialMetadata.value[material.id] = {
        id: material.id,
        name: material.name,
        code: material.code,
        unit: material.unit,
      };
    });

    const quotaMap = new Map<string, OperationMaterialResponse>();
    quotas.forEach((quota) => {
      const key = `${quota.operation_id}_${quota.material_id}`;
      quotaMap.set(key, quota);
    });

    const materialsWithQuotas = new Set<number>();
    quotas.forEach((quota) => {
      if (selectedOperations.value.includes(quota.operation_id)) {
        materialsWithQuotas.add(quota.material_id);
      }
    });

    materialsWithQuotas.forEach((materialId) => {
      newMatrix[materialId] = {};

      selectedOperations.value.forEach((operationId) => {
        const quotaKey = `${operationId}_${materialId}`;
        const quota = quotaMap.get(quotaKey);
        const productInfo = operationProductMap.value[operationId];

        newMatrix[materialId][operationId] = {
          baseQuantity: quota?.quantity || 0,
          usedQuantity: quota?.quantity || 0,
          quotedQuantity: quota?.quantity || 0,
          productQuantity: productInfo?.quantity || 1,
          isDirty: false,
          isCustom: customMaterials.value.has(materialId),
          existingId: undefined,
        };
      });
    });

    Object.entries(usageMatrix.value).forEach(([materialId, operations]) => {
      Object.entries(operations).forEach(([operationIdStr, oldCell]) => {
        const matId = Number(materialId);
        // Preserve original operation ID type to support both number and string IDs
        const opId = isNaN(Number(operationIdStr)) ? operationIdStr : Number(operationIdStr);
        const cell = oldCell as MaterialUsageCell;

        if (newMatrix[matId]?.[opId]) {
          if (cell.isDirty) {
            newMatrix[matId][opId].baseQuantity = cell.baseQuantity;
            newMatrix[matId][opId].usedQuantity = cell.usedQuantity;
            newMatrix[matId][opId].isDirty = cell.isDirty;
            newMatrix[matId][opId].existingId = cell.existingId;
          }
        } else if (customMaterials.value.has(matId) && selectedOperations.value.includes(opId)) {
          if (!newMatrix[matId]) {
            newMatrix[matId] = {};
          }
          newMatrix[matId][opId] = {
            ...cell,
            isCustom: true,
          };
        }
      });
    });

    usageMatrix.value = newMatrix;
  };

  // ===== MATERIAL OPERATIONS =====

  /**
   * Get materials that have data in the matrix
   * @returns Array of materials with their basic info
   */
  const getMaterialsInMatrix = (): Array<{
    id: number;
    name: string;
    code?: string;
    unit?: string;
  }> => {
    const materialIds = Object.keys(usageMatrix.value).map((id) => Number(id));

    return materialIds.map((id) => {
      const cached = materialMetadata.value[id];
      if (cached) {
        return cached;
      }

      return {
        id,
        name: `Material ${id}`,
        code: undefined,
        unit: undefined,
      };
    });
  };

  // ===== CELL OPERATIONS =====

  /**
   * Update a specific cell value
   * @param materialId - Material ID
   * @param operationId - Operation ID
   * @param newValue - New quantity value
   */
  const updateCell = (materialId: number, operationId: number | string, newValue: number) => {
    let cell = usageMatrix.value[materialId]?.[operationId];

    if (!cell && isEditMode.value) {
      if (!usageMatrix.value[materialId]) {
        usageMatrix.value[materialId] = {};
      }

      const productInfo = operationProductMap.value[operationId];
      usageMatrix.value[materialId][operationId] = {
        baseQuantity: 0,
        usedQuantity: 0,
        quotedQuantity: 0,
        productQuantity: productInfo?.quantity || 1,
        isDirty: false,
        isCustom: customMaterials.value.has(materialId),
        existingId: undefined,
      };

      cell = usageMatrix.value[materialId][operationId];
    }

    if (!cell) {
      return;
    }
    const isToggled = operationToggleState.value[operationId] || false;
    const productQuantity = cell.productQuantity;

    if (isToggled) {
      cell.baseQuantity =
        productQuantity > 0 ? roundTo5Decimals(newValue / productQuantity) : newValue;
      cell.usedQuantity = newValue;
    } else {
      cell.baseQuantity = newValue;
      cell.usedQuantity = newValue;
    }

    cell.isDirty = true;
  };

  /**
   * Get cell value for display
   * @param materialId - Material ID
   * @param operationId - Operation ID
   * @returns Current display value
   */
  const getCellValue = (materialId: number, operationId: number | string): number => {
    let cell = usageMatrix.value[materialId]?.[operationId];

    if (!cell && isEditMode.value && usageMatrix.value[materialId]) {
      const productInfo = operationProductMap.value[operationId];
      usageMatrix.value[materialId][operationId] = {
        baseQuantity: 0,
        usedQuantity: 0,
        quotedQuantity: 0,
        productQuantity: productInfo?.quantity || 1,
        isDirty: false,
        isCustom: customMaterials.value.has(materialId),
        existingId: undefined,
      };

      cell = usageMatrix.value[materialId][operationId];
    }

    return cell?.usedQuantity || 0;
  };

  /**
   * Check if cell is dirty
   * @param materialId - Material ID
   * @param operationId - Operation ID
   * @returns Whether cell has unsaved changes
   */
  const isCellDirty = (materialId: number, operationId: number | string): boolean => {
    getCellValue(materialId, operationId);
    return usageMatrix.value[materialId]?.[operationId]?.isDirty || false;
  };

  /**
   * Toggle multiplier for specific operation column
   * @param operationId - Operation ID to toggle
   */
  const toggleOperationMultiplier = (operationId: number | string) => {
    const currentState = operationToggleState.value[operationId] || false;
    operationToggleState.value[operationId] = !currentState;

    const newToggleState = !currentState;
    const productQuantity = operationProductMap.value[operationId]?.quantity || 1;

    Object.values(usageMatrix.value).forEach((materialOps) => {
      const cell = materialOps[operationId];
      if (cell) {
        if (newToggleState) {
          cell.usedQuantity = roundTo5Decimals(cell.baseQuantity * productQuantity);
        } else {
          cell.usedQuantity = cell.baseQuantity;
        }
      }
    });
  };

  // ===== MATERIAL OPERATIONS =====

  /**
   * Add custom material to matrix
   * @param materialId - Material ID to add
   */
  const addCustomMaterial = (materialId: number) => {
    customMaterials.value.add(materialId);

    if (!usageMatrix.value[materialId]) {
      usageMatrix.value[materialId] = {};
    }

    selectedOperations.value.forEach((operationId) => {
      if (!usageMatrix.value[materialId][operationId]) {
        const productInfo = operationProductMap.value[operationId];

        usageMatrix.value[materialId][operationId] = {
          baseQuantity: 0,
          usedQuantity: 0,
          quotedQuantity: 0,
          productQuantity: productInfo?.quantity || 1,
          isDirty: false,
          isCustom: true,
          existingId: undefined,
        };
      }
    });
  };

  /**
   * Remove material from matrix (works for both custom and quota)
   * @param materialId - Material ID to remove
   */
  const removeCustomMaterial = (materialId: number) => {
    customMaterials.value.delete(materialId);
    delete usageMatrix.value[materialId];
  };

  /**
   * Calculate total usage for a material across all operations
   * @param materialId - Material ID
   * @returns Total usage quantity
   */
  const calculateTotal = (materialId: number): number => {
    let total = 0;
    const materialOps = usageMatrix.value[materialId];

    if (materialOps) {
      Object.values(materialOps).forEach((cell) => {
        total += cell.usedQuantity;
      });
    }

    return total;
  };

  /**
   * Get all entries for API submission
   * @returns Array of material usage entries to submit
   */
  const getAllEntries = () => {
    const entries: Array<{
      material_id: number;
      operation_id?: number; // For standard operations
      operation_key?: string; // For custom operations
      attachment_id: number;
      kind: "operation_quota" | "manual_addition";
      quoted_quantity: number;
      used_quantity: number;
      existingId?: number;
    }> = [];

    Object.entries(usageMatrix.value).forEach(([materialId, operations]) => {
      Object.entries(operations).forEach(([operationIdStr, cell]) => {
        const cellData = cell as MaterialUsageCell;

        // In edit mode: submit dirty entries or non-zero values
        // In create mode: only submit non-zero values
        const shouldSubmit = isEditMode.value
          ? cellData.isDirty || cellData.usedQuantity > 0
          : cellData.usedQuantity > 0;

        if (shouldSubmit) {
          // Handle both number and string operation IDs
          const opId = isNaN(Number(operationIdStr)) ? operationIdStr : Number(operationIdStr);

          // Calculate multiplied values for API submission
          const isToggled = operationToggleState.value[opId] || false;
          const productQuantity = operationProductMap.value[opId]?.quantity || 1;
          
          // Keep original used_quantity logic, only modify quoted_quantity
          const finalUsedQuantity = cellData.usedQuantity;
            
          const finalQuotedQuantity = isToggled
            ? roundTo5Decimals(cellData.quotedQuantity * productQuantity) 
            : cellData.quotedQuantity;

          const entryPayload: any = {
            material_id: Number(materialId),
            attachment_id: attachmentId.value!,
            kind: cellData.isCustom ? "manual_addition" : "operation_quota",
            quoted_quantity: finalQuotedQuantity,
            used_quantity: finalUsedQuantity,
            existingId: cellData.existingId,
          };

          // Assign operation identifier based on type
          if (typeof opId === "number") {
            entryPayload.operation_id = opId;
          } else {
            entryPayload.operation_key = opId;
          }

          entries.push(entryPayload);
        }
      });
    });

    return entries;
  };

  /**
   * Get entries filtered by attachment ID
   * Only returns materials for operations that belong to the specified attachment
   */
  const getEntriesForAttachment = (targetAttachmentId: number) => {
    const entries: Array<{
      material_id: number;
      operation_id?: number; // For standard operations
      operation_key?: string; // For custom operations
      attachment_id: number;
      kind: "operation_quota" | "manual_addition";
      quoted_quantity: number;
      used_quantity: number;
      existingId?: number;
    }> = [];

    Object.entries(usageMatrix.value).forEach(([materialId, operations]) => {
      Object.entries(operations).forEach(([operationIdStr, cell]) => {
        const cellData = cell as MaterialUsageCell;

        // Handle both number and string operation IDs
        const opId = isNaN(Number(operationIdStr)) ? operationIdStr : Number(operationIdStr);

        // Only include operations that belong to this attachment
        const operationAttachmentId = operationProductMap.value[opId]?.attachmentId;

        // In edit mode: submit dirty entries or non-zero values
        // In create mode: only submit non-zero values
        const shouldSubmit = isEditMode.value
          ? cellData.isDirty || cellData.usedQuantity > 0
          : cellData.usedQuantity > 0;

        if (operationAttachmentId === targetAttachmentId && shouldSubmit) {
          // Calculate multiplied values for API submission
          const isToggled = operationToggleState.value[opId] || false;
          const productQuantity = operationProductMap.value[opId]?.quantity || 1;
          
          // Keep original used_quantity logic, only modify quoted_quantity
          const finalUsedQuantity = cellData.usedQuantity;
            
          const finalQuotedQuantity = isToggled
            ? roundTo5Decimals(cellData.quotedQuantity * productQuantity) 
            : cellData.quotedQuantity;

          // Create payload with correct operation identifier
          const entryPayload: any = {
            material_id: Number(materialId),
            attachment_id: targetAttachmentId,
            kind: cellData.isCustom ? "manual_addition" : "operation_quota",
            quoted_quantity: finalQuotedQuantity,
            used_quantity: finalUsedQuantity,
            existingId: cellData.existingId,
          };

          // Assign operation identifier based on type
          if (typeof opId === "number") {
            entryPayload.operation_id = opId;
          } else {
            entryPayload.operation_key = opId;
          }

          entries.push(entryPayload);
        }
      });
    });

    return entries;
  };

  /**
   * Get unique operation-attachment combinations to handle potential ID collisions
   * @returns Map of operation ID to attachment ID for conflict resolution
   */
  const getOperationAttachmentMapping = (): Map<number | string, number> => {
    const mapping = new Map<number | string, number>();

    Object.values(operationProductMap.value).forEach((productInfo, index) => {
      const operationId = Object.keys(operationProductMap.value)[index];
      mapping.set(operationId, productInfo.attachmentId);
    });

    return mapping;
  };

  /**
   * Validate operation uniqueness across attachments
   * @returns Array of potential conflicts
   */
  const validateOperationUniqueness = (): Array<{
    operationId: number | string;
    attachments: number[];
    conflict: boolean;
  }> => {
    const operationToAttachments = new Map<number | string, Set<number>>();

    Object.entries(operationProductMap.value).forEach(([opId, productInfo]) => {
      if (!operationToAttachments.has(opId)) {
        operationToAttachments.set(opId, new Set());
      }
      operationToAttachments.get(opId)!.add(productInfo.attachmentId);
    });

    const conflicts: Array<{
      operationId: number | string;
      attachments: number[];
      conflict: boolean;
    }> = [];

    operationToAttachments.forEach((attachments, operationId) => {
      conflicts.push({
        operationId,
        attachments: Array.from(attachments),
        conflict: attachments.size > 1,
      });
    });

    return conflicts;
  };

  /**
   * Clear dirty flags after successful save (for UI indicators)
   */
  const clearDirtyFlags = () => {
    Object.values(usageMatrix.value).forEach((operations) => {
      Object.values(operations).forEach((cell) => {
        // Clear dirty flags for UI indicators
        (cell as MaterialUsageCell).isDirty = false;
      });
    });
  };

  /**
   * Reset store to initial state
   */
  const resetStore = () => {
    selectedOperations.value = [];
    usageMatrix.value = {};
    operationProductMap.value = {};
    operationToggleState.value = {};
    removedOperations.value.clear();
    customMaterials.value.clear();
    isEditMode.value = false;
    attachmentId.value = null;
  };

  return {
    // State
    selectedOperations,
    usageMatrix,
    operationProductMap,
    operationToggleState,
    removedOperations,
    customMaterials,
    isEditMode,
    attachmentId,
    isLoading,
    error,

    // Getters
    hasChanges,
    materialsWithUsage,
    isOperationToggled,
    getProductQuantity,
    getMaterialsInMatrix,

    // Actions
    initialize,
    updateOperations,
    setOperationProduct,
    cacheMaterialMetadata,
    buildMatrix,
    updateCell,
    getCellValue,
    isCellDirty,
    toggleOperationMultiplier,
    addCustomMaterial,
    removeCustomMaterial,
    calculateTotal,
    getAllEntries,
    getEntriesForAttachment,
    getOperationAttachmentMapping,
    validateOperationUniqueness,
    clearDirtyFlags,
    resetStore,
  };
});
