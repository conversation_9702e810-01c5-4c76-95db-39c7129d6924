import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  OperationMaterialAddRequest,
  OperationMaterialDeleteRequest,
  OperationMaterialGetRequest,
  OperationMaterialListRequest,
  OperationMaterialResponse,
  OperationMaterialUpdateRequest,
  BulkSetRequest,
} from "@/api/bcare-types-v2";
import {
  operation_materialAdd,
  operation_materialBulkSet,
  operation_materialDelete,
  operation_materialGet,
  operation_materialList,
  operation_materialUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  materialQuotas: OperationMaterialResponse[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

export const useMaterialQuotaStore = defineStore("materialQuota", () => {
  // State
  const materialQuotas = shallowRef<OperationMaterialResponse[]>([]);
  const currentMaterialQuota = shallowRef<OperationMaterialResponse | null>(null);
  const filteredMaterialQuotas = shallowRef<OperationMaterialResponse[]>([]);
  const isLoading = shallowRef(false);
  const error = shallowRef<string | null>(null);

  // Cache management
  const cachedData = useStorage<CacheData>("material-quota-cache", {
    materialQuotas: [],
    expireTime: 0,
  });

  // Getters
  const getMaterialQuotaCount = computed(() => materialQuotas.value.length);
  const getMaterialQuotaById = (id: number) =>
    materialQuotas.value.find((mq) => mq.id === id);
  const getMaterialQuotasByOperationId = (operationId: number) =>
    materialQuotas.value.filter((mq) => mq.operation_id === operationId);
  const getMaterialQuotasByMaterialId = (materialId: number) =>
    materialQuotas.value.filter((mq) => mq.material_id === materialId);
  const hasCachedData = computed(() =>
    cachedData.value.expireTime > Date.now() && cachedData.value.materialQuotas.length > 0
  );

  // Async action wrapper
  const { performAsyncAction } = useAsyncAction();

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      materialQuotas.value = cachedData.value.materialQuotas;
    }
  }

  function updateCache() {
    cachedData.value = {
      materialQuotas: materialQuotas.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllMaterialQuotas() {
    return performAsyncAction(async () => {
      const response = await operation_materialList({ page: 1, page_size: 1000 });
      if (response.data?.operation_materials) {
        materialQuotas.value = response.data.operation_materials;
        updateCache();
      }
      return response.data;
    });
  }

  function addMaterialQuota(req: OperationMaterialAddRequest) {
    return performAsyncAction(async () => {
      const response = await operation_materialAdd(req);
      if (response.data) {
        // Convert OperationMaterial to OperationMaterialResponse format
        const newMaterialQuota: OperationMaterialResponse = {
          ...response.data,
          deleted_at: "",
        };
        materialQuotas.value.push(newMaterialQuota);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteMaterialQuota(req: OperationMaterialDeleteRequest) {
    return performAsyncAction(async () => {
      await operation_materialDelete(req);
      materialQuotas.value = materialQuotas.value.filter((mq) => mq.id !== req.id);
      updateCache();
    });
  }

  function getMaterialQuota(req: OperationMaterialGetRequest) {
    return performAsyncAction(async () => {
      const response = await operation_materialGet(req);
      if (response.data) {
        currentMaterialQuota.value = {
          ...response.data,
          deleted_at: "",
        };
      }
      return response.data;
    });
  }

  function listMaterialQuotas(req: OperationMaterialListRequest) {
    return performAsyncAction(async () => {
      const response = await operation_materialList(req);
      if (response.data?.operation_materials) {
        filteredMaterialQuotas.value = response.data.operation_materials;
      }
      return response.data;
    });
  }

  function updateMaterialQuota(req: OperationMaterialUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await operation_materialUpdate(req);
      if (response.data) {
        const index = materialQuotas.value.findIndex((mq) => mq.id === req.id);
        if (index !== -1) {
          materialQuotas.value[index] = {
            ...response.data,
            deleted_at: "",
          };
          updateCache();
        }
      }
      return response.data;
    });
  }

  function bulkSetMaterialQuotas(req: BulkSetRequest) {
    return performAsyncAction(async () => {
      await operation_materialBulkSet(req);
      // No need to refresh here, parent component will handle refresh via operation/get
    });
  }

  function fetchMaterialQuotasByOperation(operationId: number) {
    return performAsyncAction(async () => {
      const response = await operation_materialList({
        filter: { operation_id: operationId },
        page: 1,
        page_size: 1000,
      });
      if (response.data?.operation_materials) {
        // Update the cache with new data for this operation
        const otherMaterialQuotas = materialQuotas.value.filter(
          (mq) => mq.operation_id !== operationId
        );
        materialQuotas.value = [...otherMaterialQuotas, ...response.data.operation_materials];
        updateCache();
      }
      return response.data;
    });
  }

  function fetchMaterialQuotasByOperations(operationIds: number[]) {
    return performAsyncAction(async () => {
      // Fetch materials for all operations in parallel
      const promises = operationIds.map(operationId =>
        operation_materialList({
          filter: { operation_id: operationId },
          page: 1,
          page_size: 1000,
        })
      );

      const responses = await Promise.all(promises);
      const allMaterials: OperationMaterialResponse[] = [];

      responses.forEach(response => {
        if (response.data?.operation_materials) {
          allMaterials.push(...response.data.operation_materials);
        }
      });

      if (allMaterials.length > 0) {
        // Remove existing materials for these operations from store
        const otherMaterialQuotas = materialQuotas.value.filter(
          (mq) => !operationIds.includes(mq.operation_id)
        );
        materialQuotas.value = [...otherMaterialQuotas, ...allMaterials];
        updateCache();
      }

      return allMaterials;
    });
  }

  function updateFullMaterialQuotaList() {
    return performAsyncAction(async () => {
      const response = await operation_materialList({ page: 1, page_size: 1000 });
      if (response.data?.operation_materials) {
        materialQuotas.value = response.data.operation_materials;
        updateCache();
      }
      return response.data;
    });
  }

  function updateMaterialQuotasFromExternal(operationId: number, materials: OperationMaterialResponse[]) {
    // Remove existing materials for this operation from store
    const otherMaterialQuotas = materialQuotas.value.filter(
      (mq) => mq.operation_id !== operationId
    );
    // Add the new materials from external source
    materialQuotas.value = [...otherMaterialQuotas, ...materials];
    // Update cache
    updateCache();
  }

  function removeMaterialQuotas(operationId: number) {
    // Remove all material quotas for the specified operation
    materialQuotas.value = materialQuotas.value.filter(
      (mq) => mq.operation_id !== operationId
    );
    // Update cache after removal
    updateCache();
  }

  function removeMaterialQuotasForOperations(operationIds: number[]) {
    // Remove all material quotas for the specified operations
    materialQuotas.value = materialQuotas.value.filter(
      (mq) => !operationIds.includes(mq.operation_id)
    );
    // Update cache after removal
    updateCache();
  }

  return {
    // State
    materialQuotas,
    currentMaterialQuota,
    filteredMaterialQuotas,
    isLoading,
    error,
    // Getters
    getMaterialQuotaCount,
    getMaterialQuotaById,
    getMaterialQuotasByOperationId,
    getMaterialQuotasByMaterialId,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllMaterialQuotas,
    addMaterialQuota,
    deleteMaterialQuota,
    getMaterialQuota,
    listMaterialQuotas,
    updateMaterialQuota,
    bulkSetMaterialQuotas,
    fetchMaterialQuotasByOperation,
    fetchMaterialQuotasByOperations,
    updateFullMaterialQuotaList,
    updateMaterialQuotasFromExternal,
    removeMaterialQuotas,
    removeMaterialQuotasForOperations,
  };
});
