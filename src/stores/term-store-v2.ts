// src/store/term.ts
import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import { Term } from "@/api/bcare-types-v2";
import { taxonomyTermList } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export type BundleType =
  | "nghe_nghiep"
  | "loai_dieu_tri"
  | "nguon"
  | "trang_thai_dieu_tri"
  | "noi_dung_dieu_tri_ke_hoach"
  | "toa_thuoc"
  | "danh_muc_sp"
  | "don_vi"
  | "nhom_sp"
  | "thuoc_ke_toa"
  | "department"
  | "danh_muc_thu"
  | "danh_muc_chi";

interface TermData {
  [bundle: string]: Record<string, Term>;
}

interface CacheData {
  data: TermData;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useTermStore = defineStore("term_v2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const termData = shallowRef<TermData>({});

  const cachedData = useStorage<CacheData>("termStoreCache", { data: {}, expireTime: 0 });

  function initializeFromCache() {
    if (cachedData.value.expireTime > Date.now()) {
      termData.value = cachedData.value.data;
    }
  }

  function updateCache() {
    cachedData.value = {
      data: termData.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  function fetchAllTerms() {
    return performAsyncAction(async () => {
      const bundles: BundleType[] = [
        "nghe_nghiep",
        "loai_dieu_tri",
        "nguon",
        "trang_thai_dieu_tri",
        "noi_dung_dieu_tri_ke_hoach",
        "toa_thuoc",
        "danh_muc_sp",
        "don_vi",
        "nhom_sp",
        "thuoc_ke_toa",
        "department",
        "danh_muc_thu",
        "danh_muc_chi",
      ];

      const newTermData: TermData = {};

      await Promise.all(
        bundles.map(async (bundle) => {
          const response = await taxonomyTermList({ bundle, id: 0 });
          if (response.code === 0) {
            newTermData[bundle] = (response.data?.terms || []).reduce(
              (result, term) => {
                if (term.id) result[term.id] = term;
                return result;
              },
              {} as Record<string, Term>,
            );
          }
        }),
      );

      termData.value = newTermData;
      updateCache();
      return newTermData;
    });
  }

  function refreshBundle(bundle: BundleType) {
    return performAsyncAction(async () => {
      const response = await taxonomyTermList({ bundle, id: 0 });
      if (response.code === 0) {
        const newBundleData = (response.data?.terms || []).reduce(
          (result, term) => {
            if (term.id) result[term.id] = term;
            return result;
          },
          {} as Record<string, Term>,
        );

        termData.value = { ...termData.value, [bundle]: newBundleData };
        updateCache();
        return newBundleData;
      }
      throw new Error(`Failed to refresh bundle ${bundle}`);
    });
  }

  // Getters
  const getTermById = computed(
    () =>
      (bundle: BundleType, id: number): Term | undefined =>
        termData.value[bundle]?.[id],
  );

  const getTermNameById = computed(
    () =>
      (bundle: BundleType, id: number): string =>
        termData.value[bundle]?.[id]?.name || "Unknown",
  );

  return {
    termData,
    isLoading,
    error,
    initializeFromCache,
    fetchAllTerms,
    refreshBundle,
    getTermById,
    getTermNameById,
  };
});
