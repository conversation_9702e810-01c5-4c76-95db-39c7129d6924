import { defineStore } from "pinia";
import { computed, ref, shallowRef } from "vue";

import type {
  DealAddRequest,
  DealDeleteRequest,
  DealGetRequest,
  DealListRequest,
  DealResponse,
  DealUpdateRequest,
  DealUserAddRequest,
  DealUserDeleteRequest,
  DealUserRating,
  DealUserRatingAddRequest,
  DealUserRatingUpdateRequest,
  DealUserUpdateRequest,
  StageGetRequest,
} from "@/api/bcare-types-v2";
import {
  deal_userAdd,
  deal_userAddRating,
  deal_userDelete,
  deal_userDeleteRating,
  deal_userUpdate,
  deal_userUpdateRating,
  dealAdd,
  dealDelete,
  dealGet,
  dealList,
  dealUpdate,
  stageGet,
} from "@/api/bcare-v2";
import { useToastStore } from "@/stores/toast-store";

export const useDealStore = defineStore("deal", () => {
  const toastStore = useToastStore();

  // State
  const deals = shallowRef<DealResponse[]>([]);
  const currentDeal = ref<DealResponse | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const getDealCount = computed(() => deals.value.length);
  const getDealById = computed(() => (id: number) => deals.value.find((deal) => deal.id === id));

  // Helper functions
  const initValue = () => {
    isLoading.value = true;
    error.value = null;
  };

  async function performAsyncAction<T>(action: () => Promise<T>): Promise<T | null> {
    initValue();
    try {
      return await action();
    } catch (err) {
      error.value = (err as Error).message;
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  // Helper to update ratings in the state
  const updateDealUserRatingInState = (dealUserId: number, updatedRating: DealUserRating) => {
    const dealIndex = deals.value.findIndex((d) =>
      d.deal_assignment?.some((da) => da.id === dealUserId),
    );
    if (dealIndex === -1) return;

    const dealUserIndex = deals.value[dealIndex].deal_assignment?.findIndex(
      (da) => da.id === dealUserId,
    );
    if (dealUserIndex === -1 || !deals.value[dealIndex].deal_assignment) return;

    const userRatings = deals.value[dealIndex].deal_assignment[dealUserIndex].ratings || [];
    const ratingIndex = userRatings.findIndex((r) => r.id === updatedRating.id);

    if (ratingIndex !== -1) {
      // Update existing rating
      userRatings[ratingIndex] = updatedRating;
    } else {
      // Add new rating
      userRatings.push(updatedRating);
    }
    // Ensure the ratings array is assigned back if it was initially null/undefined
    deals.value[dealIndex].deal_assignment[dealUserIndex].ratings = userRatings;
    // Trigger reactivity by creating a new array for the deal assignment
    deals.value[dealIndex].deal_assignment = [...deals.value[dealIndex].deal_assignment];
    // Trigger reactivity for the deals array
    deals.value = [...deals.value];
  };

  // Actions
  function fetchDealList(request: DealListRequest) {
    return performAsyncAction(async () => {
      request.include_relation = true;
      const response = await dealList(request);
      if (response.code === 0 && response.data) {
        deals.value = response.data.deals;
      }
      return response.data?.deals;
    });
  }

  async function fetchDealsByStage(stageId: number) {
    const fetchDealsRecursive = async (stageId: number) => {
      const request: DealListRequest = {
        filter: { stage_id: stageId },
        page: 1,
        page_size: 100,
        include_relation: true,
      };

      const fetchedDeals = await fetchDealList(request);
      if (fetchedDeals) {
        deals.value.push(...fetchedDeals);
      }

      const stageRequest: StageGetRequest = { id: stageId };
      const stageResponse = await stageGet(stageRequest);
      if (stageResponse.code === 0 && stageResponse.data?.children) {
        for (const childStage of stageResponse.data.children) {
          await fetchDealsRecursive(childStage.id);
        }
      }
    };

    deals.value = [];
    await fetchDealsRecursive(stageId);
  }

  function fetchDeal(request: DealGetRequest) {
    return performAsyncAction(async () => {
      const response = await dealGet(request);
      if (response.code === 0 && response.data) {
        currentDeal.value = response.data;
      }
      return response.data;
    });
  }

  function addDeal(request: DealAddRequest) {
    return performAsyncAction(async () => {
      const response = await dealAdd(request);
      if (response.code === 0 && response.data) {
        deals.value.push(response.data);
        /*toastStore.success({
          title: "Thành công",
          message: `Thêm khách hàng ${response.data.person?.full_name} vào chờ phục vụ`,
        });*/
      }
      return response.data;
    });
  }

  function updateDeal(request: DealUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await dealUpdate(request);
      if (response.code === 0 && response.data) {
        const index = deals.value.findIndex((deal) => deal.id === request.id);
        if (index !== -1) {
          deals.value[index] = response.data;
        }
        toastStore.success({
          title: "Thành công",
          message: ``,
        });
      }
      return response.data;
    });
  }

  function deleteDeal(request: DealDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await dealDelete(request);
      if (response.code === 0) {
        deals.value = deals.value.filter((deal) => deal.id !== request.id);
        toastStore.success({
          title: "Xóa deal thành công",
          message: "",
        });
      }
      return response.data;
    });
  }

  function deleteDealUser(request: DealUserDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await deal_userDelete(request);
      return response.data;
    });
  }

  function addDealUser(request: DealUserAddRequest) {
    return performAsyncAction(async () => {
      const response = await deal_userAdd(request);
      return response.data;
    });
  }

  function updateDealUser(request: DealUserUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await deal_userUpdate(request);
      // Optionally update state if needed, but rating updates are handled separately now
      return response.data;
    });
  }

  // Add Deal User Rating Action
  function addDealUserRating(request: DealUserRatingAddRequest) {
    return performAsyncAction(async () => {
      const response = await deal_userAddRating(request);
      if (response.code === 0 && response.data) {
        // Update the specific deal user's ratings in the state
        if (request.deal_user_id) {
          updateDealUserRatingInState(request.deal_user_id, response.data);
        }
      }
      return response.data;
    });
  }

  // Update Deal User Rating Action
  function updateDealUserRating(request: DealUserRatingUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await deal_userUpdateRating(request);
      if (response.code === 0 && response.data) {
        // Update the specific deal user's ratings in the state
        updateDealUserRatingInState(response.data.deal_user_id, response.data);
      }
      return response.data;
    });
  }

  async function getLatestDeal(personId: number): Promise<DealResponse | null> {
    const response = await dealList({
      filter: { person_id: personId },
      page: 1,
      page_size: 1,
      include_relation: false,
      order_by: "created_at DESC",
    });

    if (response.code === 0 && response.data?.deals && response.data.deals.length > 0) {
      return response.data.deals[0];
    }
    return null;
  }

  return {
    // State
    deals,
    currentDeal,
    isLoading,
    error,
    // Getters
    getDealCount,
    getDealById,
    // Actions
    fetchDealList,
    fetchDealsByStage,
    fetchDeal,
    addDeal,
    updateDeal,
    deleteDeal,
    deleteDealUser,
    addDealUser,
    updateDealUser,
    addDealUserRating,
    updateDealUserRating,
    getLatestDeal,
  };
});
