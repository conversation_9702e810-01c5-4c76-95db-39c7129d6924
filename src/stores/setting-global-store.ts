import { defineStore } from "pinia";
import { reactive, watch } from "vue";
import { useLocalStorage } from "@vueuse/core";

import { UniversalSetting } from "@/api/extend-types";

export const ALL_SETTINGS: Record<string, UniversalSetting[]> = {
  dashboard: [
    {
      type: "group",
      label: "Bộ lọc",
      field_name: "display",
      value: null,
      children: [
        {
          type: "toggle_button",
          label: "Ẩn bác sĩ",
          field_name: "hide_doctor",
          value: false,
        },
        {
          type: "toggle_button",
          label: "Ẩn bộ lọc",
          field_name: "hide_filter",
          value: false,
        },
      ],
    },
    {
      type: "select",
      label: "Chế độ hiển thị",
      field_name: "display_mode",
      options: [
        { label: "<PERSON>h sách", value: "list" },
        { label: "Lưới", value: "grid" },
      ],
      value: "list",
    },
  ],
  person: [
    {
      type: "group",
      label: "<PERSON><PERSON><PERSON> thị cột",
      field_name: "display",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "<PERSON><PERSON><PERSON> thị cột",
          field_name: "display_columns",
          options: [
            { label: "STT", value: true, field_name: "id" },
            { label: "Thông tin KH", value: true, field_name: "full_name" },
            { label: "Email", value: false, field_name: "email" },
            { label: "Ngày sinh", value: false, field_name: "date_of_birth" },
            { label: "Nghề nghiệp", value: false, field_name: "job_id" },
            {
              label: "Mô tả",
              value: false,
              field_name: "person_field.description",
            },
            { label: "Địa chỉ", value: false, field_name: "address_number" },
            { label: "Nguồn khách hàng", value: true, field_name: "source_id" },
            {
              label: "Loại điều trị",
              value: true,
              field_name: "person_field.treatment_id",
            },
            {
              label: "Trạng thái điều trị",
              value: true,
              field_name: "person_field.treatment_status_id",
            },
            {
              label: "Sale",
              value: true,
              field_name: "sale.name",
            },
            {
              label: "Stage",
              value: true,
              field_name: "stage_name",
            },
            {
              label: "Ngày tạo",
              value: true,
              field_name: "created_at",
            },
            {
              label: "Thông tin form",
              value: false,
              field_name: "person_field.form_source",
            },
            {
              label: "Nguồn & URL",
              value: false,
              field_name: "person_field.source_channel",
            },
          ],
        },
      ],
    },
  ],
  task: [
    {
      type: "group",
      label: "Hiển thị cột",
      field_name: "display",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "Hiển thị cột",
          field_name: "display_columns",
          options: [
            { label: "Tên công việc", value: true, field_name: "title" },
            { label: "Khách hàng", value: true, field_name: "person_id" },
            { label: "Ngày bắt đầu", value: true, field_name: "start_date" },
            { label: "Ngày kết thúc", value: true, field_name: "due_date" },
            { label: "Người thực hiện", value: true, field_name: "assignments" },
            { label: "Trạng thái", value: true, field_name: "state" },
            { label: "Ngày hoàn thành", value: false, field_name: "completed_at" },
            { label: "Thao tác", value: true, field_name: "actions" },
          ],
        },
      ],
    },
  ],
  deal: [
    {
      type: "group",
      label: "Hiển thị cột",
      field_name: "display",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "Hiển thị cột",
          field_name: "display_columns",
          options: [
            { label: "Ngày tạo", value: false, field_name: "created_at" },
            { label: "Ngày tới đầu tiên", value: true, field_name: "first_arrival_date" },
            { label: "Tên Deal", value: false, field_name: "name" },
            { label: "Khách hàng", value: true, field_name: "person_id" },
            { label: "Stage", value: true, field_name: "stage_id" },
            { label: "Tag", value: false, field_name: "tags" },
            { label: "Người liên quan", value: true, field_name: "related_users" },
            { label: "Loại lịch hẹn", value: true, field_name: "latest_appointment_type" },
            { label: "Thời gian lịch hẹn", value: true, field_name: "latest_appointment_time" },
            { label: "Tổng thanh toán", value: true, field_name: "total_person_payment" },
          ],
        },
      ],
    },
    {
      type: "group",
      label: "Hiển thị role trong danh sách người liên quan",
      field_name: "user_role_visibility",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "Hiển thị role",
          field_name: "visible_user_roles",
          options: [
            { label: "Bác sĩ điều trị", value: false, field_name: "treatment_doctor" },
            { label: "Bác sĩ tư vấn", value: false, field_name: "consultant_doctor" },
            { label: "Sale Online", value: false, field_name: "sale" },
            { label: "Phụ tá", value: true, field_name: "doctor_assistant" },
          ],
        },
      ],
    },
    {
      type: "group",
      label: "Hiển thị preset filters",
      field_name: "preset_filters_visibility",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "Hiển thị preset filters",
          field_name: "visible_preset_filters",
          options: [
            { label: "Tất cả", value: true, field_name: "all" },
            { label: "Deal đã tạo", value: false, field_name: "my_deals" },
            { label: "Deal won", value: false, field_name: "won_today" },
            { label: "Deal lost", value: false, field_name: "lost_today" },
            { label: "Deal active", value: false, field_name: "active_deals" },
            { label: "Đến phòng khám", value: true, field_name: "stage_den_phong_kham" },
            { label: "Đã TV", value: true, field_name: "stage_da_tv" },
            { label: "Chưa cọc", value: true, field_name: "stage_chua_coc" },
            { label: "Đã cọc", value: true, field_name: "stage_da_coc" },
            { label: "Gặp BS", value: true, field_name: "stage_gap_bs" },
            { label: "Tổng quát", value: true, field_name: "stage_tong_quat" },
          ],
        },
      ],
    },
  ],
};

export const useSettingGlobalStore = defineStore("settingGlobalStore", () => {
  // Create state object that will hold useLocalStorage refs
  const state = reactive<Record<string, UniversalSetting[]>>({});
  
  // Use useLocalStorage for each setting key - this handles sync automatically
  Object.keys(ALL_SETTINGS).forEach((key) => {
    const localStorageRef = useLocalStorage(key, ALL_SETTINGS[key], {
      serializer: {
        read: (value: string) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.warn(`Failed to parse localStorage for key ${key}, using defaults`);
            return structuredClone ? structuredClone(ALL_SETTINGS[key]) : JSON.parse(JSON.stringify(ALL_SETTINGS[key]));
          }
        },
        write: (value: UniversalSetting[]) => JSON.stringify(value)
      }
    });
    
    // Assign initial value
    state[key] = localStorageRef.value;
    
    // Single watcher: sync state changes to localStorage
    // useLocalStorage handles localStorage -> state automatically
    watch(() => state[key], (newValue) => {
      localStorageRef.value = newValue;
    }, { deep: true });
  });

  const getSettingByKey = (settingKey: string) => {
    return state[settingKey];
  };

  return { state, getSettingByKey };
});
