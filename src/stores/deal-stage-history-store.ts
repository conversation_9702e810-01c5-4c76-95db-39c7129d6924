import dayjs from "dayjs";
import { defineStore } from "pinia";
import { reactive, onUnmounted } from "vue";
import type { UserShort } from "@/api/bcare-types-v2"; // Import UserShort

import {
  DEAL_STAGE_HISTORY_COUNT_QUERY,
  DEAL_STAGE_HISTORY_QUERY,
} from "@/constants/sql/deal-stage-history-queries";
import { useRawQueryV2, ParamConfig, SortOrder } from "@/hooks/useRawQuery-v2"; // Import V2
import { useToastStore } from "@/stores/toast-store";
// Import types for DealStageHistory if available, otherwise use 'any' for now
// import { DealStageHistory } from "@/api/bcare-types-v2";

// Define the structure of the filter state
interface DealStageHistoryFilters {
  changedAtRange: {
    start: Date | string | null;
    end: Date | string | null;
  };
  personSearch: string | null;
  beforeStages: number[] | null;
  afterStages: number[] | null;
  tags: number[] | null;
  relatedUsers: UserShort[] | null;
}

export const useDealStageHistoryStore = defineStore("dealStageHistory", () => {
  const toastStore = useToastStore();

  const sqlQuery = DEAL_STAGE_HISTORY_QUERY;
  const countQuery = DEAL_STAGE_HISTORY_COUNT_QUERY;

  // --- Parameter Configuration ---
  const paramConfig: Record<string, ParamConfig> = {
    changedAtFrom: { type: "date", placeholder: "$1", default: null },
    changedAtTo: { type: "date", placeholder: "$2", default: null },
    personSearch: { type: "string", placeholder: "$3", default: null },
    beforeStages: { type: "array", placeholder: "$4", arrayElementType: "int", default: null },
    afterStages: { type: "array", placeholder: "$5", arrayElementType: "int", default: null },
    tags: { type: "array", placeholder: "$6", arrayElementType: "int", default: null },
    relatedUsers: { type: "array", placeholder: "$7", arrayElementType: "int", default: null },
    limit: { type: "int", placeholder: "$8", isLimit: true, default: 50 },
    offset: { type: "int", placeholder: "$9", isOffset: true, default: 0 },
    // This parameter holds the ORDER BY clause generated by useRawQueryV2
    orderClause1: {
      type: "string",
      template: true,
      isOrderClause: true /* Mark as order clause */,
      default: "dsh.changed_at DESC",
    },
  };

  // --- Default Sorting ---
  const defaultSort: SortOrder[] = [{ field: "dsh.changed_at", direction: "DESC" }];

  // --- Instantiate useRawQueryV2 ---
  const {
    data: histories,
    loading,
    error,
    rowCount: totalHistories,
    executionTime,
    params,
    execute,
    refresh,
    resetParams: resetRawQueryParams,
    setParams,
    pagination,
    sorting,
    countLoading,
    executeCount,
  } = useRawQueryV2(sqlQuery, paramConfig, {
    pageSize: 50,
    timeout: 60, // Increase timeout slightly if queries are complex
    countQuery: countQuery,
    defaultSort: defaultSort,
    onSuccess: (data) => {
      // console.log("Deal stage history query executed successfully:", data);
    },
    onError: (err) => {
      console.error("Error executing deal stage history query:", err);
      toastStore.error({ message: "Lấy lịch sử chuyển giai đoạn thất bại." });
    },
  });

  // --- Filter Management ---
  const filters = reactive<DealStageHistoryFilters>({
    changedAtRange: { start: null, end: null },
    personSearch: null,
    beforeStages: null,
    afterStages: null,
    tags: null,
    relatedUsers: [],
  });

  // Helper to find the key for the offset parameter
  const offsetParamKey = Object.entries(paramConfig).find(([_, config]) => config.isOffset)?.[0];

  function _applyFiltersToParams() {
    // Date Range
    params.changedAtFrom = filters.changedAtRange?.start || null;
    params.changedAtTo = filters.changedAtRange?.end
      ? dayjs(filters.changedAtRange.end).add(1, "day").toDate()
      : filters.changedAtRange?.start
        ? dayjs(filters.changedAtRange.start).add(1, "day").toDate()
        : null;

    params.personSearch = filters.personSearch || null;
    params.beforeStages =
      filters.beforeStages && filters.beforeStages.length > 0 ? filters.beforeStages : null;
    params.afterStages =
      filters.afterStages && filters.afterStages.length > 0 ? filters.afterStages : null;
    params.tags = filters.tags && filters.tags.length > 0 ? filters.tags : null;

    // Handle empty array case for relatedUsers
    params.relatedUsers =
      filters.relatedUsers && filters.relatedUsers.length > 0
        ? filters.relatedUsers.map((user) => user.id)
        : null;

    // Explicitly set offset to 0 when applying filters
    if (offsetParamKey && pagination.page !== 1) {
      // Only reset if not already on page 1
      params[offsetParamKey] = 0;
      pagination.page = 1;
    } else if (offsetParamKey) {
      // Ensure param is updated even if page is 1
      params[offsetParamKey] = 0;
    }
  }

  function _getDefaultFilterState(): DealStageHistoryFilters {
    const today = dayjs().startOf("day").toDate(); // Start of today
    return {
      changedAtRange: { start: today, end: today }, // Both start and end are today
      personSearch: null,
      beforeStages: null,
      afterStages: null,
      tags: null,
      relatedUsers: [], // Default to empty array
    };
  }

  async function applyFilters() {
    _applyFiltersToParams();
    // Now executeCount is correctly in scope
    await executeCount();
    return await execute();
  }

  async function resetFilters() {
    Object.assign(filters, _getDefaultFilterState());
    // Reset raw query params completely, don't execute yet
    await resetRawQueryParams(false);
    // Re-apply the (now default) filters to the params object
    _applyFiltersToParams();
    // Now execute count and main query
    await executeCount(); // Also call count here after reset
    return await execute();
  }

  // No need for updateFilters if applyFilters handles everything

  async function clearFilter(filterKey: keyof DealStageHistoryFilters) {
    const defaultState = _getDefaultFilterState();

    switch (filterKey) {
      case "changedAtRange":
        filters.changedAtRange = defaultState.changedAtRange;
        break;
      case "personSearch":
        filters.personSearch = defaultState.personSearch;
        break;
      case "beforeStages":
        filters.beforeStages = defaultState.beforeStages;
        break;
      case "afterStages":
        filters.afterStages = defaultState.afterStages;
        break;
      case "relatedUsers":
        // Resetting to empty array or null? Let's match _getDefaultFilterState
        filters.relatedUsers = defaultState.relatedUsers;
        break;
      case "tags":
        filters.tags = defaultState.tags;
        break;
    }
    // After clearing, apply the filters to update the query and fetch data
    await applyFilters();
  }

  // --- Initial Data Load ---
  async function loadInitialData() {
    // Reset filters to default (including today's date)
    Object.assign(filters, _getDefaultFilterState());
    // Reset raw query params (pagination, sort, etc.) but don't execute yet
    await resetRawQueryParams(false);
    // --- ADD THIS --- Apply the default filters to the params object
    _applyFiltersToParams();
    // Execute count and main query with initial filters applied
    await executeCount(); // Call count AFTER filters are applied
    return await execute();
  }

  // --- Store Cleanup ---
  function resetStoreState() {
    histories.value = [];
    totalHistories.value = 0;
    Object.assign(filters, _getDefaultFilterState());
    sorting.setOrders([...defaultSort]); // Reset sorting
    // Reset raw query params to their defaults (this also resets internal pagination page to 1)
    resetRawQueryParams(false);
  }

  onUnmounted(() => {
    resetStoreState();
  });

  function $reset() {
    resetStoreState();
  }

  function $dispose() {
    // Usually similar to $reset or for aborting requests
    $reset();
  }

  // --- Exposed API ---
  return {
    // Data & State
    histories,
    loading,
    error,
    totalHistories,
    executionTime,
    countLoading,

    // Pagination & Sorting
    pagination,
    sorting,

    // Filter state and methods
    filters,
    applyFilters,
    resetFilters,
    clearFilter,

    // Core execution methods
    execute,
    refresh,
    loadInitialData,

    // Store lifecycle
    $reset,
    $dispose,
  };
});
