import { defineStore } from "pinia";
import { useConfirm } from "primevue/useconfirm";
import { computed, ref } from "vue";

import { config } from "@/config";
import Realtime from "@/realtime";
import { WsMessage } from "@/realtime/websocket";

interface EventCallback {
  event: string;
  callback: (msg: WsMessage) => void;
}

export const useWsStore = defineStore("websocket", () => {
  const conn = ref<Realtime | null>(null);
  const events = ref<EventCallback[]>([]);
  const isReady = ref(false);

  function connect(onMessageCallback?: (msg: WsMessage) => void, onData?: (data: string) => void) {
    if (!conn.value) {
      conn.value = new Realtime(config.wsUrl, onMessageCallback, onData);
      conn.value.getWs().addEvent("ready", () => {
        isReady.value = true;
        handleReady();
      });
      // conn.value.getWs().addEvent("close", handleDisconnect);
    }
  }

  function disconnect() {
    conn.value?.getWs().disconnect();
    conn.value = null;
    events.value = [];
    isReady.value = false;
  }

  function addEvent(event: string, callback: (msg: WsMessage) => void) {
    events.value.push({ event, callback });
    if (isReady.value && conn.value) {
      conn.value.on(event, callback);
    }
  }

  function removeEvent(event: string, callback: (msg: WsMessage) => void) {
    const index = events.value.findIndex((e) => e.event === event && e.callback === callback);
    if (index !== -1) {
      events.value.splice(index, 1);
      conn.value?.off(event, callback);
    }
  }

  function handleReady() {
    if (!conn.value) return;
    events.value.forEach(({ event, callback }) => {
      conn.value!.on(event, callback);
    });
  }

  const realtime = computed(() => {
    if (!conn.value) {
      throw new Error("WebSocket connection not initialized");
    }
    // Return a proxy that queues operations until ready
    return new Proxy(conn.value, {
      get(target, prop) {
        if (prop === 'on' || prop === 'off' || prop === 'join' || prop === 'mustJoin' || prop === 'leave') {
          return async (...args: any[]) => {
            // Wait for connection to be ready before executing
            if (!isReady.value) {
              await waitForReady();
            }
            return (target as any)[prop](...args);
          };
        }
        return (target as any)[prop];
      }
    });
  });

  // Helper function to wait for WebSocket to be ready
  function waitForReady(): Promise<void> {
    return new Promise((resolve) => {
      if (isReady.value) {
        resolve();
        return;
      }
      const checkReady = () => {
        if (isReady.value) {
          resolve();
        } else {
          setTimeout(checkReady, 50);
        }
      };
      checkReady();
    });
  }

  return {
    realtime,
    connect,
    disconnect,
    addEvent,
    removeEvent,
    isReady,
  };
});

export interface WsStore extends ReturnType<typeof useWsStore> {}
