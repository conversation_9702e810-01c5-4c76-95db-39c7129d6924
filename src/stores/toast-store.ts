import { defineStore } from "pinia";
import { ref } from "vue";

export type ToastType = "success" | "warning" | "error" | "info";

export interface SimpleToast {
  id: number;
  type: ToastType;
  title: string;
  message: string;
}

export type ToastPayload = {
  type?: ToastType;
  title?: string;
  message: string;
};

// const defaultTimeout = 2000;

const createToast = (
  type: ToastType,
  title: string,
  message: string,
): SimpleToast => ({
  id: Math.random() * 1000,
  type,
  title,
  message,
});

export const useToastStore = defineStore("toast", () => {
  const toasts = ref<SimpleToast[]>([]);

  function add(payload: ToastPayload) {
    const { type: originType, title: originTitle, message } = payload;
    const title = originTitle === undefined ? "Info" : originTitle;
    const type = originType === undefined ? "info" : originType;

    const toast = createToast(type, title, message);

    toasts.value.push(toast);
  }

  function next() {
    if (toasts.value.length > 0) {
      const [firstToast] = toasts.value.splice(0, 1);
      return firstToast;
    }
    return null;
  }

  function success(payload: ToastPayload) {
    payload.type = "success";
    add(payload);
  }

  function warning(payload: ToastPayload) {
    payload.title = "Warning";
    payload.type = "warning";
    add(payload);
  }

  function error(payload: ToastPayload) {
    payload.type = "error";
    add(payload);
  }

  function info(payload: ToastPayload) {
    payload.type = "info";
    add(payload);
  }

  return {
    toasts,
    add,
    next,
    success,
    warning,
    error,
    info,
  };
});
