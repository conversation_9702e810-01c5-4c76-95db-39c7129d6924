// src/store/operation.ts
import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  OperationAddRequest,
  OperationDeleteRequest,
  OperationGetRequest,
  OperationListRequest,
  OperationResponse,
  OperationUpdateRequest,
  ProductOperation,
  BulkSetProductOperationRequest,
} from "@/api/bcare-types-v2";
import {
  operationAll,
  operationAdd,
  operationDelete,
  operationGet,
  operationList,
  operationUpdate,
  product_operationBulkSet,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface OperationData {
  [id: number]: OperationResponse;
}

interface CacheData {
  data: OperationData;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useOperationStore = defineStore("operation_v2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const operationData = shallowRef<OperationData>({});
  // Additional state from v2
  const currentOperation = shallowRef<OperationResponse | null>(null);
  const filteredOperations = shallowRef<OperationResponse[]>([]);

  const cachedData = useStorage<CacheData>("operationStoreCache", { data: {}, expireTime: 0 });

  function initializeFromCache() {
    if (cachedData.value.expireTime > Date.now()) {
      operationData.value = cachedData.value.data;
    }
  }

  function updateCache() {
    cachedData.value = {
      data: operationData.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  function fetchAllOperations() {
    return performAsyncAction(async () => {
      const response = await operationAll();
      if (response.code === 0 && response.data) {
        const newOperationData: OperationData = {};
        response.data.operations.forEach((operation) => {
          if (operation.id) {
            newOperationData[operation.id] = operation;
          }
        });

        operationData.value = newOperationData;
        updateCache();
        return newOperationData;
      }
      throw new Error("Failed to fetch operations");
    });
  }

  // Getters
  const getOperationById = computed(
    () =>
      (id: number): OperationResponse | undefined =>
        operationData.value[id],
  );

  const getOperationNameById = computed(
    () =>
      (id: number): string =>
        operationData.value[id]?.name || "Unknown",
  );

  const getProductOperationsForOperation = computed(
    () =>
      (operationId: number): ProductOperation[] => {
        const operation = operationData.value[operationId];
        return operation ? operation.product_operation : [];
      },
  );

  // Additional getters from v2
  const getOperationCount = computed(() => Object.keys(operationData.value).length);

  const hasCachedData = computed(() => {
    return (
      cachedData.value.expireTime > Date.now() && Object.keys(cachedData.value.data).length > 0
    );
  });

  async function refreshOperations() {
    // Xóa cache
    cachedData.value = { data: {}, expireTime: 0 };
    operationData.value = {};

    // Tải lại dữ liệu
    return await fetchAllOperations();
  }

  // Additional actions from v2
  function addOperation(req: OperationAddRequest) {
    return performAsyncAction(async () => {
      const response = await operationAdd(req);
      if (response.data && response.data.id) {
        operationData.value[response.data.id] = response.data;
        updateCache();
      }
      return response.data;
    });
  }

  function deleteOperation(req: OperationDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await operationDelete(req);
      // operationDelete returns null, so we just remove from local data
      if (req.id && operationData.value[req.id]) {
        delete operationData.value[req.id];
        updateCache();
      }
      return response;
    });
  }

  function getOperation(req: OperationGetRequest) {
    return performAsyncAction(async () => {
      const response = await operationGet(req);
      currentOperation.value = response.data ?? null;
      return response.data;
    });
  }

  function listOperations(req: OperationListRequest) {
    return performAsyncAction(async () => {
      const response = await operationList(req);
      if (response.data?.operations && Array.isArray(response.data.operations)) {
        filteredOperations.value = response.data.operations;
      } else {
        filteredOperations.value = [];
      }
      return response.data;
    });
  }

  function updateOperation(req: OperationUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await operationUpdate(req);
      if (response.data && response.data.id) {
        operationData.value[response.data.id] = response.data;
        updateCache();
      }
      return response.data;
    });
  }

  function updateFullOperationList() {
    return performAsyncAction(async () => {
      const response = await operationList({ page: 1, page_size: 1000 });
      if (response.data?.operations && Array.isArray(response.data.operations)) {
        const newOperationData: OperationData = {};
        response.data.operations.forEach((operation) => {
          if (operation.id) {
            newOperationData[operation.id] = operation;
          }
        });
        operationData.value = newOperationData;
        updateCache();
      }
      return response.data;
    });
  }

  function bulkSetProductOperation(req: BulkSetProductOperationRequest) {
    return performAsyncAction(async () => {
      const response = await product_operationBulkSet(req);
      if (response && req.operationId) {
        const updatedOperation = await operationGet({ id: req.operationId });
        if (updatedOperation.data && updatedOperation.data.id) {
          operationData.value[updatedOperation.data.id] = updatedOperation.data;
          updateCache();
        }
      }
      return response;
    });
  }

  return {
    // State
    operationData,
    currentOperation,
    filteredOperations,
    isLoading,
    error,
    // Getters
    getOperationById,
    getOperationNameById,
    getProductOperationsForOperation,
    getOperationCount,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllOperations,
    refreshOperations,
    addOperation,
    deleteOperation,
    getOperation,
    listOperations,
    updateOperation,
    updateFullOperationList,
    bulkSetProductOperation,
  };
});
