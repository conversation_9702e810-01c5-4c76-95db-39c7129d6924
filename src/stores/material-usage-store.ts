import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  MaterialUsage,
  MaterialUsageAddRequest,
  MaterialUsageBulkAddRequest,
  MaterialUsageDeleteRequest,
  MaterialUsageGetRequest,
  MaterialUsageListRequest,
  MaterialUsageResponse,
  MaterialUsageUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  material_usageAdd,
  material_usageBulkAdd,
  material_usageDelete,
  material_usageGet,
  material_usageList,
  material_usageUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useMaterialUsageStore = defineStore("materialUsage", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const materialUsages = shallowRef<MaterialUsageResponse[]>([]);
  const currentMaterialUsage = shallowRef<MaterialUsageResponse | null>(null);
  const filteredMaterialUsages = shallowRef<MaterialUsageResponse[]>([]);

  // Getters
  const getMaterialUsageCount = computed(() => materialUsages.value.length);
  const getMaterialUsageById = computed(
    () => (id: number) => materialUsages.value.find((materialUsage) => materialUsage.id === id),
  );

  // Actions
  function fetchAllMaterialUsages() {
    return performAsyncAction(async () => {
      const response = await material_usageList({ page: 1, page_size: 1000 });
      if (response.data?.material_usages) {
        materialUsages.value = response.data.material_usages;
      }
      return response.data;
    });
  }

  function addMaterialUsage(req: MaterialUsageAddRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageAdd(req);
      if (response.data) {
        materialUsages.value.push(response.data);
      }
      return response.data;
    });
  }

  function bulkAddMaterialUsage(req: MaterialUsageBulkAddRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageBulkAdd(req);
      return response.data;
    });
  }

  function deleteMaterialUsage(req: MaterialUsageDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageDelete(req);
      if (response.data) {
        materialUsages.value = materialUsages.value.filter(
          (materialUsage) => materialUsage.id !== req.id,
        );
      }
      return response.data;
    });
  }

  function getMaterialUsage(req: MaterialUsageGetRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageGet(req);
      currentMaterialUsage.value = response.data ?? null;
      return response.data;
    });
  }

  function listMaterialUsages(req: MaterialUsageListRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageList(req);
      if (response.data?.material_usages) {
        filteredMaterialUsages.value = response.data.material_usages;
      }
      return response.data;
    });
  }

  function updateMaterialUsage(req: MaterialUsageUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await material_usageUpdate(req);
      if (response.data) {
        const index = materialUsages.value.findIndex(
          (materialUsage) => materialUsage.id === req.id,
        );
        if (index !== -1) {
          materialUsages.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  // Helper functions
  function getMaterialUsagesByAttachmentId(attachmentId: number) {
    return materialUsages.value.filter((usage) => usage.attachment_id === attachmentId);
  }

  function getMaterialUsagesByMaterialId(materialId: number) {
    return materialUsages.value.filter((usage) => usage.material_id === materialId);
  }

  function getMaterialUsagesByOperationId(operationId: number) {
    return materialUsages.value.filter((usage) => usage.operation_id === operationId);
  }

  function getMaterialUsagesByKind(kind: "operation_quota" | "manual_addition") {
    return materialUsages.value.filter((usage) => usage.kind === kind);
  }

  return {
    // State
    materialUsages,
    currentMaterialUsage,
    filteredMaterialUsages,
    isLoading,
    error,
    // Getters
    getMaterialUsageCount,
    getMaterialUsageById,
    // Actions
    fetchAllMaterialUsages,
    addMaterialUsage,
    bulkAddMaterialUsage,
    deleteMaterialUsage,
    getMaterialUsage,
    listMaterialUsages,
    updateMaterialUsage,
    // Helper functions
    getMaterialUsagesByAttachmentId,
    getMaterialUsagesByMaterialId,
    getMaterialUsagesByOperationId,
    getMaterialUsagesByKind,
  };
});
