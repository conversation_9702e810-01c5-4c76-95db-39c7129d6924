import { defineStore } from "pinia";
import { computed, onUnmounted,ref } from "vue";

import {
  NotificationDeleteRequest,
  NotificationListRequest,
  NotificationMarkAllAsReadRequest,
  NotificationMarkAsReadRequest,
  Notification,
  NotificationAddRequest,
  NotificationUnreadCountRequest} from "@/api/bcare-types-v2";
import {
  notificationAdd,
  notificationDelete,
  notificationList,
  notificationMarkAllAsRead,
  notificationMarkAsRead,
  notificationUnread,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useAuthStore } from "@/stores/auth-store";

export const useNotificationStore = defineStore("notification", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();
  const authStore = useAuthStore();

  // State
  const notifications = ref<Notification[]>([]);
  const unreadCount = ref<number>(0);
  const currentPage = ref<number>(1);
  const totalPages = ref<number>(1);
  const totalNotifications = ref<number>(0);
  const currentPageSize = ref<number>(20);

  // Polling state
  const pollingInterval = ref<number | null>(null);
  const POLLING_INTERVAL = 30000; // 30 seconds
  const lastFetchTimestamp = ref<string | null>(null);

  // Getters
  const getNotificationCount = computed(() => notifications.value.length);
  const getUnreadNotifications = computed(() =>
    notifications.value.filter((n) => !n.is_read)
  );
  const getReadNotifications = computed(() =>
    notifications.value.filter((n) => n.is_read)
  );
  const getNotificationById = computed(
    () => (id: number) => notifications.value.find((n) => n.id === id)
  );

  // Get notifications by entity
  const getNotificationsByEntity = computed(
    () => (entityType: string, entityId: number) =>
      notifications.value.filter(
        (n) => n.entity_type === entityType && n.entity_id === entityId
      )
  );

  // Get notifications by type
  const getNotificationsByType = computed(
    () => (type: string) => notifications.value.filter((n) => n.type === type)
  );

  // Actions
  function fetchNotifications(req: NotificationListRequest, options?: { append?: boolean }) {
    return performAsyncAction(async () => {
      const response = await notificationList(req);
      if (response.data?.data) {
        const { append = false } = options || {};

        if (append && req.page && req.page > 1) {
          // Append data for "load more" functionality
          notifications.value.push(...response.data.data);
        } else {
          // Replace data for page navigation or initial load
          notifications.value = response.data.data;
        }

        totalNotifications.value = response.data.total;
        totalPages.value = response.data.total_page;
        currentPage.value = req.page || 1;
        currentPageSize.value = req.limit || 20;
        lastFetchTimestamp.value = new Date().toISOString();
      }
      return response.data;
    });
  }

  // Load more notifications (append to existing list)
  function loadMoreNotifications(req: NotificationListRequest) {
    return fetchNotifications(req, { append: true });
  }

  // Navigate to specific page (replace existing list)
  function navigateToPage(req: NotificationListRequest) {
    return fetchNotifications(req, { append: false });
  }

  function addNotification(req: NotificationAddRequest) {
    return performAsyncAction(async () => {
      const response = await notificationAdd(req);
      if (response.data) {
        // Add to beginning of list (newest first)
        notifications.value.unshift(response.data);
        if (!response.data.is_read) {
          unreadCount.value++;
        }
      }
      return response.data;
    });
  }

  function deleteNotification(req: NotificationDeleteRequest) {
    return performAsyncAction(async () => {
      await notificationDelete(req);
      const notification = notifications.value.find(
        (n) => n.id === req.notification_id
      );
      if (notification && !notification.is_read) {
        unreadCount.value--;
      }
      notifications.value = notifications.value.filter(
        (n) => n.id !== req.notification_id
      );
    });
  }

  function markAsRead(req: NotificationMarkAsReadRequest) {
    return performAsyncAction(async () => {
      await notificationMarkAsRead(req);
      // Update local state
      req.notification_ids.forEach((id) => {
        const notification = notifications.value.find((n) => n.id === id);
        if (notification && !notification.is_read) {
          notification.is_read = true;
          notification.read_at = new Date().toISOString();
          unreadCount.value--;
        }
      });
    });
  }

  function markAllAsRead(req: NotificationMarkAllAsReadRequest) {
    return performAsyncAction(async () => {
      await notificationMarkAllAsRead(req);
      // Update local state
      const now = new Date().toISOString();
      notifications.value.forEach((notification) => {
        if (!notification.is_read) {
          notification.is_read = true;
          notification.read_at = now;
        }
      });
      unreadCount.value = 0;
    });
  }

  function fetchUnreadCount(req: NotificationUnreadCountRequest) {
    return performAsyncAction(async () => {
      const response = await notificationUnread(req);
      if (response.data) {
        unreadCount.value = response.data.unread_count;
      }
      return response.data;
    });
  }

  // WebSocket handler for new notifications
  function handleWebSocketNotification(notification: Notification) {
    // Add to beginning of list
    notifications.value.unshift(notification);
    if (!notification.is_read) {
      unreadCount.value++;
    }
  }

  // Clear all notifications from local state
  function clearNotifications() {
    notifications.value = [];
    unreadCount.value = 0;
    currentPage.value = 1;
    totalPages.value = 1;
    totalNotifications.value = 0;
    currentPageSize.value = 20;
  }

  // Sync notifications with server (fetch latest and merge with WebSocket updates)
  async function syncNotifications() {
    const currentUserId = authStore.currentUser?.id;
    if (!currentUserId) return;

    try {
      // Fetch latest notifications
      const response = await notificationList({
        user_id: currentUserId,
        page: 1,
        limit: 20,
        sort_by: "created_at",
        order: "desc",
      });

      if (response.data?.data) {
        // Merge with existing notifications, avoiding duplicates
        const existingIds = new Set(notifications.value.map(n => n.id));
        const newNotifications = response.data.data.filter(n => !existingIds.has(n.id));

        if (newNotifications.length > 0) {
          // Add new notifications to the beginning
          notifications.value.unshift(...newNotifications);
          // Sort by created_at to maintain order
          notifications.value.sort((a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        }

        // Update counts
        totalNotifications.value = response.data.total;
        totalPages.value = response.data.total_page;
        lastFetchTimestamp.value = new Date().toISOString();
      }

      // Also fetch unread count to ensure accuracy
      const unreadResponse = await notificationUnread({
        user_id: currentUserId,
      });
      if (unreadResponse.data) {
        unreadCount.value = unreadResponse.data.unread_count;
      }
    } catch (err) {
      console.error("Failed to sync notifications:", err);
    }
  }

  // Start polling for notifications
  function startPolling() {
    // Clear any existing interval
    stopPolling();

    // Start new polling interval
    pollingInterval.value = window.setInterval(() => {
      syncNotifications();
    }, POLLING_INTERVAL);

    // Do an immediate sync
    syncNotifications();
  }

  // Stop polling for notifications
  function stopPolling() {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
  }

  // Clean up on unmount
  onUnmounted(() => {
    stopPolling();
  });

  return {
    // State
    notifications,
    unreadCount,
    currentPage,
    totalPages,
    totalNotifications,
    currentPageSize,
    isLoading,
    error,
    // Getters
    getNotificationCount,
    getUnreadNotifications,
    getReadNotifications,
    getNotificationById,
    getNotificationsByEntity,
    getNotificationsByType,
    // Actions
    fetchNotifications,
    loadMoreNotifications,
    navigateToPage,
    addNotification,
    deleteNotification,
    markAsRead,
    markAllAsRead,
    fetchUnreadCount,
    handleWebSocketNotification,
    clearNotifications,
    syncNotifications,
    startPolling,
    stopPolling,
  };
});
