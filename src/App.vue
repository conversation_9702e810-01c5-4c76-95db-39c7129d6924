<script setup lang="ts">
import { defineAsyncComponent, onMounted, onUnmounted } from "vue";

import type { Notification } from "@/api/bcare-types-v2";
import SimpleToast from "@/base-components/Toast/SimpleToast.vue";

import { useNotificationStore } from "./stores/notification-store";
import { useWsStore } from "./stores/ws-store";

const LazyConfirmDialog = defineAsyncComponent(() => import("primevue/confirmdialog"));
const LazyCallCenter = defineAsyncComponent(
  () => import("@/base-components/SipPhone/CallCenter.vue"),
);
const LazyModalCustomer = defineAsyncComponent(() => import("@/pages/customer/ModalCustomer.vue"));

const wsStore = useWsStore();
const notificationStore = useNotificationStore();

onMounted(async () => {
    wsStore.connect();
    try {
      await wsStore.realtime.on("notification", (msg) => {
        if (msg.data && typeof msg.data === 'object') {
          notificationStore.handleWebSocketNotification(msg.data as Notification);
        }
      });
    } catch (error) {
      console.error("Failed to register notification handler:", error);
    }

    // Start polling for notifications to catch any missed WebSocket messages
    notificationStore.startPolling();
  }
);
onUnmounted(() => {
  wsStore.disconnect();
  notificationStore.stopPolling();
});
</script>

<template>
  <Suspense>
    <template #default>
      <div>
        <LazyConfirmDialog group="global" />
        <LazyCallCenter />
      </div>
    </template>

    <template #fallback>
      <div class="fixed bottom-4 right-4 w-80 rounded-lg bg-white p-4 shadow-lg">
        <i class="pi pi-spin pi-spinner mr-2" />
        Loading ...
      </div>
    </template>
  </Suspense>

  <SimpleToast />
  <RouterView />
  <LazyModalCustomer />
</template>
