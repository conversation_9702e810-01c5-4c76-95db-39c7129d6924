import { computed, ref } from "vue";

import type {
  MaterialUsageReport,
  MaterialUsageReportRequest,
  MaterialUsageDetailRow,
} from "@/api/bcare-types-v2";
import {
  material_usageReport,
  material_usageDetailReport,
  material_usageExportReport,
  material_usageExportDetailReport
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { downloadBlob } from "@/utils/file";
import { getDateStringFromPayload } from "@/utils/time-helper";

export type ReportType = "summary" | "detail";

interface MaterialUsageReportQueryOptions {
  reportType?: ReportType;
}

export function useMaterialUsageReportQuery(options: MaterialUsageReportQueryOptions = {}) {
  const { reportType = "summary" } = options;

  // State management
  const summaryItems = ref<MaterialUsageReport[]>([]);
  const detailItems = ref<MaterialUsageDetailRow[]>([]);
  const total = ref(0);
  const totalPages = ref(0);
  const currentRequest = ref<MaterialUsageReportRequest>({});

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Export state management
  const isExporting = ref(false);

  // Computed properties
  const items = computed(() => {
    return reportType === "summary" ? summaryItems.value : detailItems.value;
  });

  // Fetch summary report
  const fetchSummaryReport = async (
    request: MaterialUsageReportRequest,
    getCount: boolean = true
  ) => {
    return performAsyncAction(async () => {
      try {
        currentRequest.value = request;
        const response = await material_usageReport(request);

        if (response.code === 0 && response.data) {
          summaryItems.value = response.data.material_usage_report || [];
          if (getCount) {
            total.value = response.data.total || 0;
            totalPages.value = response.data.total_page || 0;
          }
        } else {
          summaryItems.value = [];
          total.value = 0;
          totalPages.value = 0;
        }

        return response;
      } catch (e) {
        summaryItems.value = [];
        total.value = 0;
        totalPages.value = 0;
        throw e;
      }
    });
  };

  // Fetch detail report
  const fetchDetailReport = async (
    request: MaterialUsageReportRequest,
    getCount: boolean = true
  ) => {
    return performAsyncAction(async () => {
      try {
        currentRequest.value = request;
        const response = await material_usageDetailReport(request);

        if (response.code === 0 && response.data) {
          detailItems.value = response.data.items || [];
          if (getCount) {
            total.value = response.data.total || 0;
            totalPages.value = response.data.total_page || 0;
          }
        } else {
          detailItems.value = [];
          total.value = 0;
          totalPages.value = 0;
        }

        return response;
      } catch (e) {
        detailItems.value = [];
        total.value = 0;
        totalPages.value = 0;
        throw e;
      }
    });
  };

  // Generic fetch function that routes to appropriate endpoint
  const fetchReport = async (
    request: MaterialUsageReportRequest,
    getCount: boolean = true,
    type: ReportType = reportType
  ) => {
    if (type === "summary") {
      return fetchSummaryReport(request, getCount);
    } else {
      return fetchDetailReport(request, getCount);
    }
  };

  // Export functionality with loading state management
  const exportReport = async (
    request: MaterialUsageReportRequest,
    type: ReportType = reportType
  ) => {
    if (isExporting.value) return; // Prevent multiple exports

    isExporting.value = true;

    try {
      const exportRequest = {
        ...request,
        page_size: undefined, // Remove pagination for export
        page: undefined,
      };

      // Select the appropriate API function based on report type
      const api = type === 'summary' ? material_usageExportReport : material_usageExportDetailReport;

      // Call the API and get the blob
      const blob = await api(exportRequest);

      // Generate filename with date from request payload
      const dateStr = getDateStringFromPayload(request);
      const typeStr = type === 'summary' ? 'vat-tu-tong-hop' : 'vat-tu-chi-tiet';
      const filename = `${typeStr}${dateStr ? `_${dateStr}` : ''}.xlsx`;

      // Trigger download
      downloadBlob(blob, filename);
    } catch (error) {
      console.error("Export failed:", error);
      // Re-throw error so components can handle it if needed
      throw error;
    } finally {
      isExporting.value = false;
    }
  };

  // Helper to calculate efficiency and difference for summary items
  const enhancedSummaryItems = computed(() => {
    return summaryItems.value.map(item => ({
      ...item,
      difference: item.total_used_quantity - item.total_quoted_quantity,
      efficiency: item.total_quoted_quantity > 0 
        ? Math.round((item.total_used_quantity / item.total_quoted_quantity) * 100)
        : 0,
    }));
  });

  // Helper to calculate efficiency and difference for detail items
  const enhancedDetailItems = computed(() => {
    return detailItems.value.map(row => ({
      ...row,
      operation_details: row.operation_details.map(operation => ({
        ...operation,
        used_materials: operation.used_materials.map(material => ({
          ...material,
          efficiency: material.quoted_quantity > 0
            ? Math.round((material.used_quantity / material.quoted_quantity) * 100)
            : 0,
        })),
      })),
    }));
  });

  return {
    // Data
    summaryItems: enhancedSummaryItems,
    detailItems: enhancedDetailItems,
    items,
    total,
    totalPages,
    currentRequest: computed(() => currentRequest.value),

    // Actions
    fetchReport,
    fetchSummaryReport,
    fetchDetailReport,
    exportReport,

    // State
    isLoading,
    isExporting,
    error,
  };
}
