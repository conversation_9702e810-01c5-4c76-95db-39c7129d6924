import { computed, onMounted, ref, toRaw, watch } from "vue";
import { useLocalStorage } from "@vueuse/core";

import { useConfigurationsStore } from "@/stores/configuration-store";

export function useComponentSetting(initialSettingName?: string, options?: {
  useLocal?: boolean; // Use localStorage instead of backend
  defaultValue?: any; // Default value for implicit settings
}) {
  const store = useConfigurationsStore();
  const category = "component";
  const useLocal = options?.useLocal ?? false;

  // State management
  const syncStatus = ref(false);
  const errorMessage = ref<string | null>(null);
  const isInitialized = ref(false);
  const currentSetting = ref<{ [key: string]: any } | null>(null);
  const settingName = ref<string | undefined>(initialSettingName);

  // Local storage for implicit settings
  const localStorageKey = computed(() => 
    settingName.value ? `bcare-setting-${settingName.value}` : null
  );
  
  const localStorage = localStorageKey.value && useLocal
    ? useLocalStorage(localStorageKey.value, options?.defaultValue ?? {})
    : ref(null);

  // Thêm method refresh
  const refreshCurrentSetting = () => {
    if (settingName.value) {
      if (useLocal && localStorage.value !== null) {
        currentSetting.value = localStorage.value;
      } else {
        currentSetting.value = getSetting(settingName.value);
      }
    }
  };

  // Initialize store and load initial setting if settingName is provided
  onMounted(async () => {
    if (!isInitialized.value) {
      if (!useLocal) {
        await store.init({ category: "", name: "" });
      }
      isInitialized.value = true;
      refreshCurrentSetting();
    }
  });

  // Watch for localStorage changes if using local storage
  if (useLocal && localStorage.value !== null) {
    watch(localStorage, () => {
      refreshCurrentSetting();
    }, { deep: true });
  }

  const syncSetting = async (name: string, value: any) => {
    try {
      syncStatus.value = false;
      errorMessage.value = null;

      if (useLocal) {
        // Save to localStorage
        const key = `bcare-setting-${name}`;
        useLocalStorage(key, value).value = value;
        syncStatus.value = true;
        
        // Update current setting if it's the same name
        if (settingName.value === name) {
          currentSetting.value = value;
        }
      } else {
        // Save to backend
        const requestValue = JSON.stringify(value);
        const success = await store.syncSetting(category, name, requestValue);
        syncStatus.value = success;

        if (!success) {
          throw new Error("Failed to sync setting");
        }

        // Sau khi sync thành công, refresh lại giá trị
        if (settingName.value && name === settingName.value) {
          refreshCurrentSetting();
        }
      }
    } catch (error) {
      errorMessage.value = (error as Error).message || "Unknown error";
      console.error("Error syncing setting:", errorMessage.value);
    }
  };

  const getSetting = (name: string): { [key: string]: any } | null => {
    if (useLocal) {
      const key = `bcare-setting-${name}`;
      const stored = useLocalStorage(key, options?.defaultValue ?? null);
      return stored.value;
    } else {
      const setting = store.getSettingByKey(category, name);
      return setting ? setting.value : null;
    }
  };

  const updateSettingName = (newSettingName: string) => {
    settingName.value = newSettingName;
    refreshCurrentSetting();
  };

  const currentSettingValue = computed(() => {
    return toRaw(currentSetting.value);
  });

  // Helper to update current setting directly (for implicit settings)
  const updateCurrentSetting = (value: any) => {
    if (settingName.value) {
      syncSetting(settingName.value, value);
    }
  };

  return {
    syncSetting,
    getSetting,
    syncStatus,
    errorMessage,
    currentSetting,
    currentSettingValue,
    settingName,
    updateSettingName,
    refreshCurrentSetting,
    updateCurrentSetting, // New helper method
  };
}