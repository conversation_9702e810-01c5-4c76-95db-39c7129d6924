import { debounce } from "lodash";
import { computed, readonly, ref } from "vue";

export interface FilterListConfig {
  field: string;
  valueTransform?: (value: any) => any;
  isFilter?: boolean;
}

export function useFilterList(
  applyFiltersCallback: (filters: any) => void,
  filterConfigs: Record<string, FilterListConfig>,
  defaultFilters: Record<string, any> = {},
) {
  const filters = ref<Record<string, any>>({});
  const currentFilterPayload = ref<Record<string, any>>({
    ...defaultFilters,
    filter: {},
  });

  // Initialize filters
  Object.keys(filterConfigs).forEach((key) => {
    filters.value[key] = { value: null };
  });

  const applyFilters = debounce(() => {
    const appliedFilters: Record<string, any> = {
      ...defaultFilters,
      filter: {},
    };

    Object.entries(filters.value).forEach(([key, { value }]) => {
      if (value == null || value === "") return;

      const config = filterConfigs[key];
      const transformedValue = config.valueTransform ? config.valueTransform(value) : value;

      if (config.isFilter) {
        // Special handling for dateRange - split into from/to in filter object
        if (key === "dateRange" && transformedValue && typeof transformedValue === "object") {
          // Extract from and to from the transformed dateRange object
          if (transformedValue.from) {
            appliedFilters.filter.from = transformedValue.from;
          }
          if (transformedValue.to) {
            appliedFilters.filter.to = transformedValue.to;
          }
        } else {
          appliedFilters.filter[config.field] = transformedValue;
        }
      } else {
        appliedFilters[config.field] = transformedValue;
      }
    });

    currentFilterPayload.value = appliedFilters;
    applyFiltersCallback(appliedFilters);
  }, 500);

  const updateFilters = (newFilters: Record<string, any>) => {
    Object.entries(newFilters).forEach(([key, value]) => {
      if (filters.value[key]) {
        filters.value[key] = value;
      }
    });
    applyFilters();
  };

  const computedFilters = computed({
    get: () => filters.value,
    set: updateFilters,
  });

  // Initial load
  applyFilters();

  return {
    filters: computedFilters,
    currentFilterPayload: readonly(currentFilterPayload),
  };
}
