import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  MaterialUsageAddRequest,
  MaterialUsageBulkAddRequest,
  MaterialUsageDeleteRequest,
  MaterialUsageGetRequest,
  MaterialUsageListRequest,
  MaterialUsageResponse,
  MaterialUsageUpdateRequest,
  OperationMaterialResponse,
} from "@/api/bcare-types-v2";
import { useMaterialUsageStore } from "@/stores/material-usage-store";
import { useMaterialUsageMatrixStore } from "@/stores/material-usage-matrix-store";
import useMaterial from "@/hooks/useMaterial";
import useMaterialQuota from "@/hooks/useMaterialQuota";
import useAttachment from "@/hooks/useAttachment";

interface UseMaterialUsageOptions {
  autoLoad?: boolean;
  attachmentId?: number;
  materialId?: number;
  operationId?: number;
}

export default function useMaterialUsage(options: UseMaterialUsageOptions = { autoLoad: true }) {
  const materialUsageStore = useMaterialUsageStore();
  const { isLoading, error, materialUsages } = storeToRefs(materialUsageStore);

  // Local state for filtered material usages, initialized as empty
  const filteredMaterialUsages = shallowRef<MaterialUsageResponse[]>([]);

  // Computed property for material usage count
  const materialUsageCount = computed(() => materialUsageStore.getMaterialUsageCount);

  // Load all material usages
  const loadMaterialUsages = async () => {
    await materialUsageStore.fetchAllMaterialUsages();
    filteredMaterialUsages.value = [];
  };

  if (options.autoLoad) {
    onMounted(() => {
      loadMaterialUsages();
    });
  }

  // Add a new material usage
  const addMaterialUsage = async (req: MaterialUsageAddRequest) => {
    return await materialUsageStore.addMaterialUsage(req);
  };

  // Bulk add material usages
  const bulkAddMaterialUsage = async (req: MaterialUsageBulkAddRequest) => {
    return await materialUsageStore.bulkAddMaterialUsage(req);
  };

  // Delete a material usage
  const deleteMaterialUsage = async (req: MaterialUsageDeleteRequest) => {
    return await materialUsageStore.deleteMaterialUsage(req);
  };

  // Get a specific material usage
  const getMaterialUsage = async (req: MaterialUsageGetRequest) => {
    return await materialUsageStore.getMaterialUsage(req);
  };

  // List material usages with filtering
  const listMaterialUsages = async (req: MaterialUsageListRequest) => {
    const response = await materialUsageStore.listMaterialUsages(req);
    if (response?.material_usages) {
      filteredMaterialUsages.value = response.material_usages;
    }
    return response;
  };

  // Update a material usage
  const updateMaterialUsage = async (req: MaterialUsageUpdateRequest) => {
    return await materialUsageStore.updateMaterialUsage(req);
  };

  // Get a material usage by ID
  const getMaterialUsageById = (id: number) => {
    return materialUsageStore.getMaterialUsageById(id);
  };

  // Get material usages by attachment ID
  const getMaterialUsagesByAttachmentId = (attachmentId: number) => {
    return materialUsageStore.getMaterialUsagesByAttachmentId(attachmentId);
  };

  // Get material usages by material ID
  const getMaterialUsagesByMaterialId = (materialId: number) => {
    return materialUsageStore.getMaterialUsagesByMaterialId(materialId);
  };

  // Get material usages by operation ID
  const getMaterialUsagesByOperationId = (operationId: number) => {
    return materialUsageStore.getMaterialUsagesByOperationId(operationId);
  };

  // Get material usages by kind
  const getMaterialUsagesByKind = (kind: "operation_quota" | "manual_addition") => {
    return materialUsageStore.getMaterialUsagesByKind(kind);
  };

  // Search material usages
  const searchMaterialUsages = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    filteredMaterialUsages.value = materialUsageStore.materialUsages.filter(
      (usage) =>
        usage.note?.toLowerCase().includes(lowercaseQuery) ||
        usage.kind.toLowerCase().includes(lowercaseQuery),
    );
  };

  // Sort material usages
  const sortMaterialUsages = (key: keyof MaterialUsageResponse, order: "asc" | "desc" = "asc") => {
    filteredMaterialUsages.value = [...filteredMaterialUsages.value].sort((a, b) => {
      const aValue = a[key];
      const bValue = b[key];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return order === "asc" ? -1 : 1;
      if (bValue == null) return order === "asc" ? 1 : -1;

      // Handle numeric comparison
      if (typeof aValue === "number" && typeof bValue === "number") {
        return order === "asc" ? aValue - bValue : bValue - aValue;
      }

      // Fallback to string comparison for other types
      const aStr = String(aValue);
      const bStr = String(bValue);
      const comparison = aStr.localeCompare(bStr);
      return order === "asc" ? comparison : -comparison;
    });
  };

  // Paginate material usages
  const paginateMaterialUsages = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredMaterialUsages.value.slice(startIndex, startIndex + pageSize);
  };

  // Filter material usages by specific criteria
  const filterMaterialUsages = (filters: {
    attachmentId?: number;
    materialId?: number;
    operationId?: number;
    kind?: "operation_quota" | "manual_addition";
    userId?: number;
  }) => {
    filteredMaterialUsages.value = materialUsageStore.materialUsages.filter((usage) => {
      if (filters.attachmentId && usage.attachment_id !== filters.attachmentId) return false;
      if (filters.materialId && usage.material_id !== filters.materialId) return false;
      if (filters.operationId && usage.operation_id !== filters.operationId) return false;
      if (filters.kind && usage.kind !== filters.kind) return false;
      if (filters.userId && usage.user_id !== filters.userId) return false;
      return true;
    });
  };

  // ===== MATERIAL USAGE V2 INTEGRATION =====

  /**
   * Initialize Material Usage V2 system - FIXED for multi-attachment support
   * @param editMode - Whether in edit mode
   * @param currentAttachmentId - Current attachment ID (optional, for single attachment context)
   */
  const initializeMaterialUsageV2 = async (editMode: boolean = false, currentAttachmentId: number | null = null) => {
    try {
      const matrixStore = useMaterialUsageMatrixStore();
      const { materials, loadMaterials } = useMaterial({ autoLoad: false });

      matrixStore.initialize(editMode, currentAttachmentId);
    } catch (error) {
      console.error('❌ Failed to initialize Material Usage V2 system:', error);
      throw error;
    }
  };

  /**
   * Handle operations changed event from AttachmentTree for V2 system
   * Updated to support both standard (number) and custom (string) operations
   * @param data - Operations changed event data
   */
  const handleOperationsChangedV2 = async (data: {
    attachmentId: number;
    operations: { id: number | string; name: string }[];
    changedAttachmentId?: number;  // Which attachment triggered the change
    changedOperations?: { id: number | string; name: string }[];  // Operations that actually changed
  }) => {
    try {
      const matrixStore = useMaterialUsageMatrixStore();
      const { materials, loadMaterials } = useMaterial({ autoLoad: false });
      const { loadMaterialQuotasForOperations } = useMaterialQuota({ autoLoad: false });

      // 🔧 FIX: Use MERGE logic instead of REPLACE to preserve operations from other attachments
      const allOperationIds = data.operations.map(op => op.id);

      // Set current attachment ID if not set or if this is the primary attachment
      if (!matrixStore.attachmentId || matrixStore.attachmentId === data.attachmentId) {
        matrixStore.attachmentId = data.attachmentId;
      }

      // ✅ FIX: Smart update - merge new operations but remove operations that are no longer in ANY attachment
      // First, get all operations that should exist (from all attachments)
      const shouldExistOperations = new Set(allOperationIds);

      // Check if any existing operations should be removed (not in any attachment anymore)
      const currentOperations = new Set(matrixStore.selectedOperations);
      const operationsToRemove: (number | string)[] = [];

      currentOperations.forEach(opId => {
        if (!shouldExistOperations.has(opId)) {
          operationsToRemove.push(opId);
        }
      });

      // Remove operations that are no longer in any attachment
      if (operationsToRemove.length > 0) {
        operationsToRemove.forEach(opId => {
          matrixStore.removedOperations.add(opId);
          Object.values(matrixStore.usageMatrix).forEach(materialOps => {
            delete materialOps[opId];
          });
          delete matrixStore.operationToggleState[opId];
          delete matrixStore.operationProductMap[opId];
        });
      }

      // Update with all operations that should exist
      matrixStore.selectedOperations = allOperationIds;

      // ✅ VALIDATION: Check for operation conflicts and log warnings
      const conflicts = matrixStore.validateOperationUniqueness();
      const hasConflicts = conflicts.some(c => c.conflict);

      if (hasConflicts) {
        console.warn('⚠️ Operation ID conflicts detected:');
        conflicts.filter(c => c.conflict).forEach(conflict => {
          console.warn(`  Operation ${conflict.operationId} appears in attachments: ${conflict.attachments.join(', ')}`);
        });
        console.warn('  This may cause unexpected behavior with product quantity multipliers.');
      }

      // 2. Update product quantity mapping for ALL operations from ALL attachments
      // ✅ FIX: Handle potential operation ID collisions by tracking attachment per operation
      const operationAttachmentMap = new Map<number | string, number>();

      // Build operation-to-attachment mapping from all operations
      data.operations.forEach(operation => {
        const attachmentId = data.changedAttachmentId || data.attachmentId;
        operationAttachmentMap.set(operation.id, attachmentId);
      });

      // Update product info for operations from the changed attachment
      const changedAttachmentId = data.changedAttachmentId || data.attachmentId;
      const changedOperations = data.changedOperations || data.operations;

      const productQuantity = await extractProductQuantityV2(changedAttachmentId);

      // ✅ FIX: Update product info for changed operations with correct attachment mapping
      changedOperations.forEach(operation => {
        const productInfo = {
          productId: 0,
          quantity: productQuantity,
          attachmentId: changedAttachmentId
        };
        matrixStore.setOperationProduct(operation.id, productInfo);
      });

      // ✅ ENHANCEMENT: Ensure all existing operations have product info
      // This handles cases where operations were added from other attachments previously
      matrixStore.selectedOperations.forEach(async (opId) => {
        if (!matrixStore.operationProductMap[opId]) {
          // Operation doesn't have product info, try to extract it
          const attachmentId = operationAttachmentMap.get(opId) || data.attachmentId;
          const quantity = await extractProductQuantityV2(attachmentId);

          const productInfo = {
            productId: 0,
            quantity: quantity,
            attachmentId: attachmentId
          };
          matrixStore.setOperationProduct(opId, productInfo);
        }
      });

      if (matrixStore.isEditMode) {
        // ✅ FIXED: Load ALL existing usage data for the attachment, regardless of operation type
        // This ensures we don't miss any existing data for custom operations
        await loadExistingUsageDataV2ForAttachment(data.attachmentId);

        if (materials.value.length === 0) {
          await loadMaterials();
        }

        matrixStore.cacheMaterialMetadata(materials.value);
      } else {
        // 3. Filter for ONLY standard operations to fetch quotas (from ALL operations, not just changed ones)
        const allStandardOperationIds = allOperationIds
          .filter(id => typeof id === 'number') as number[];

        if (allStandardOperationIds.length > 0) {
          await loadMaterialQuotasForOperations(allStandardOperationIds);

          const { materialQuotas } = useMaterialQuota({ autoLoad: false });
          const quotas: OperationMaterialResponse[] = materialQuotas.value.filter(quota =>
            allStandardOperationIds.includes(quota.operation_id)
          );

          if (materials.value.length === 0) {
            await loadMaterials();
          }

          matrixStore.buildMatrix(materials.value, quotas);
        }
      }

    } catch (error) {
      console.error('❌ Error handling operations changed V2:', error);
      throw error;
    }
  };


  /**
   * 🔧 FIXED: Submit material usage for specific attachment (handles both CREATE and EDIT modes)
   */
  const submitMaterialUsageV2ForAttachment = async (attachmentId: number): Promise<boolean> => {
    try {
      const matrixStore = useMaterialUsageMatrixStore();
      const { bulkAddMaterialUsage, addMaterialUsage, updateMaterialUsage } = useMaterialUsage({ autoLoad: false });

      // ✅ FILTER: Get only entries for this specific attachment
      const attachmentEntries = matrixStore.getEntriesForAttachment(attachmentId);

      if (attachmentEntries.length === 0) {
        return true;
      }
      
      // ✅ FIXED: Handle edit mode vs create mode per entry
      if (matrixStore.isEditMode) {
        // EDIT MODE: Handle CREATE/UPDATE operations individually
        const operations = [];
        
        for (const entry of attachmentEntries) {
          if (entry.existingId) {
            // UPDATE existing usage
            operations.push(
              updateMaterialUsage({
                id: entry.existingId,
                used_quantity: entry.used_quantity,
                modified: ["used_quantity"]
              }).then(() => ({ type: 'UPDATE', success: true, entry }))
                .catch(error => ({ type: 'UPDATE', success: false, entry, error }))
            );
          } else {
            // CREATE new usage - handle both operation_id and operation_key
            const createPayload: any = {
              attachment_id: entry.attachment_id,
              material_id: entry.material_id,
              kind: entry.kind,
              quoted_quantity: entry.quoted_quantity,
              used_quantity: entry.used_quantity
            };

            // ✅ CRITICAL: Add operation identifier based on type
            if (entry.operation_id !== undefined) {
              createPayload.operation_id = entry.operation_id;
            } else if (entry.operation_key !== undefined) {
              createPayload.operation_key = entry.operation_key;
            }

            operations.push(
              addMaterialUsage(createPayload).then(() => ({ type: 'CREATE', success: true, entry }))
                .catch(error => ({ type: 'CREATE', success: false, entry, error }))
            );
          }
        }
        
        // Execute all operations
        const results = await Promise.all(operations);
        
        // Check for failures
        const failures = results.filter(r => !r.success);
        if (failures.length > 0) {
          console.error('❌ Some operations failed:', failures);
          throw new Error(`Failed ${failures.length}/${results.length} operations for attachment ${attachmentId}`);
        }
        
      } else {
        // CREATE MODE: Use bulk add for new entries - handle both operation_id and operation_key
        await bulkAddMaterialUsage({
          item: attachmentEntries.map(entry => {
            const bulkPayload: any = {
              attachment_id: entry.attachment_id,
              material_id: entry.material_id,
              kind: entry.kind,
              quoted_quantity: entry.quoted_quantity,
              used_quantity: entry.used_quantity
            };

            // ✅ CRITICAL: Add operation identifier based on type
            if (entry.operation_id !== undefined) {
              bulkPayload.operation_id = entry.operation_id;
            } else if (entry.operation_key !== undefined) {
              bulkPayload.operation_key = entry.operation_key;
            }

            return bulkPayload;
          })
        });
      }
      
      await loadMaterialUsages();
      
      return true;

    } catch (error) {
      console.error(`❌ Error submitting materials for attachment ${attachmentId}:`, error);
      return false;
    }
  };



  /**
   * Load existing usage data for attachment in edit mode
   * ✅ FIXED: Load ALL existing usage data regardless of operation type (standard or custom)
   * @param attachmentId - Attachment ID
   */
  const loadExistingUsageDataV2ForAttachment = async (attachmentId: number) => {
    
    try {

      const response = await listMaterialUsages({
        filter: { attachment_id: attachmentId },
        page: 1,
        page_size: 1000
      });

      const allUsagesForAttachment = response?.material_usages || [];
      // ✅ FIXED: Include both standard operations (operation_id) and custom operations (operation_key)
      const existingUsages = allUsagesForAttachment.filter(usage =>
        usage.operation_id != null || usage.operation_key != null
      );

      if (existingUsages.length > 0) {
        const matrixStore = useMaterialUsageMatrixStore();

        // Merge existing usage data into matrix
        existingUsages.forEach(usage => {
          const materialId = usage.material_id;
          // ✅ CRITICAL: Handle both operation_id (number) and operation_key (string)
          const operationId = usage.operation_id || usage.operation_key;

          if (!operationId) {
            return;
          }

          // Ensure matrix structure exists
          if (!matrixStore.usageMatrix[materialId]) {
            matrixStore.usageMatrix[materialId] = {};
          }

          if (!matrixStore.usageMatrix[materialId][operationId]) {
            const productInfo = matrixStore.operationProductMap[operationId];
            matrixStore.usageMatrix[materialId][operationId] = {
              baseQuantity: usage.used_quantity || 0,
              usedQuantity: usage.used_quantity || 0,
              quotedQuantity: usage.quoted_quantity || 0,
              productQuantity: productInfo?.quantity || 1,
              isDirty: false,
              isCustom: usage.kind === 'manual_addition',
              existingId: usage.id
            };
          } else {
            const cell = matrixStore.usageMatrix[materialId][operationId];
            cell.baseQuantity = usage.used_quantity || 0;
            cell.usedQuantity = usage.used_quantity || 0;
            cell.quotedQuantity = usage.quoted_quantity || 0;
            cell.isCustom = usage.kind === 'manual_addition';
            cell.existingId = usage.id;
            cell.isDirty = false;
          }
        });
      }

    } catch (error) {
      console.error('❌ Error loading existing usage data V2 for attachment:', attachmentId, error);
    }
  };
  

  /**
   * Extract product quantity from attachment for V2 system
   * @param attachmentId - Attachment ID
   * @returns Product quantity
   */
  const extractProductQuantityV2 = async (attachmentId: number): Promise<number> => {
    try {
      const { getAttachment } = useAttachment({ useStore: false });
      const attachment = await getAttachment({ id: attachmentId });

      if (attachment) {
        let targetAttachment = attachment;
        
        if (attachment.parent_id) {
          const parentAttachment = await getAttachment({ id: attachment.parent_id });
          if (parentAttachment) {
            targetAttachment = parentAttachment;
          }
        }
        
        return targetAttachment.quantity || 1;
      }

      return 1;
    } catch (error) {
      console.error('❌ Error extracting product quantity V2:', error);
      return 1;
    }
  };

  return {
    isLoading,
    error,
    materialUsages, // Exposed material usages list from the store
    filteredMaterialUsages,
    materialUsageCount,
    loadMaterialUsages,
    addMaterialUsage,
    bulkAddMaterialUsage,
    deleteMaterialUsage,
    getMaterialUsage,
    listMaterialUsages,
    updateMaterialUsage,
    getMaterialUsageById,
    getMaterialUsagesByAttachmentId,
    getMaterialUsagesByMaterialId,
    getMaterialUsagesByOperationId,
    getMaterialUsagesByKind,
    searchMaterialUsages,
    sortMaterialUsages,
    paginateMaterialUsages,
    filterMaterialUsages,

    // Material Usage V2 Integration
    initializeMaterialUsageV2,
    handleOperationsChangedV2,
    submitMaterialUsageV2ForAttachment,
    extractProductQuantityV2,
    loadExistingUsageDataV2ForAttachment,
  };
}
