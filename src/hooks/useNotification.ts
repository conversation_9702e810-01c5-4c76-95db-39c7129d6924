import { storeToRefs } from "pinia";

import type {
  Notification,
  NotificationAddRequest,
} from "@/api/bcare-types-v2";
import {
  notificationAdd,
  notificationDelete,
  notificationList,
  notificationMarkAllAsRead,
  notificationMarkAsRead,
  notificationUnread,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useNotificationNavigation } from "@/composables/useNotificationNavigation";
import { useAuthStore } from "@/stores/auth-store";
import { useNotificationStore } from "@/stores/notification-store";

interface UseNotificationOptions {
  autoLoad?: boolean;
  autoLoadUnreadCount?: boolean;
  useStore?: boolean;
  pageSize?: number;
  paginationMode?: 'loadMore' | 'paginator';
}

export default function useNotification(
  options: UseNotificationOptions = {
    autoLoad: false,
    autoLoadUnreadCount: true,
    useStore: true,
    pageSize: 20,
    paginationMode: 'loadMore',
  }
) {
  const {
    autoLoad = false,
    autoLoadUnreadCount = true,
    useStore = true,
    pageSize = 20,
    paginationMode = 'loadMore',
  } = options;

  const authStore = useAuthStore();
  const currentUserId = computed(() => authStore.currentUser?.id);
  const { navigateToNotification } = useNotificationNavigation();

  if (useStore) {
    const notificationStore = useNotificationStore();
    const {
      isLoading,
      error,
      notifications,
      unreadCount,
      currentPage,
      totalPages,
      totalNotifications,
      currentPageSize,
      getUnreadNotifications,
      getReadNotifications,
    } = storeToRefs(notificationStore);

    // Initialize page size if not set
    if (!currentPageSize.value) {
      currentPageSize.value = pageSize;
    }

    // Pagination state
    const hasMorePages = computed(() => currentPage.value < totalPages.value);

    // Category helpers
    const getCategoryStyle = (type: string) => {
      if (type.includes("assignment")) return "blue";
      if (type.includes("success") || type.includes("complete")) return "green";
      if (type.includes("alert") || type.includes("overdue")) return "red";
      if (type.includes("reminder")) return "orange";
      if (type.includes("update")) return "yellow";
      if (type.includes("info")) return "gray";
      if (type.includes("warning")) return "amber";
      return "gray";
    };

    const getNotificationIcon = (type: string) => {
      if (type.includes("assignment")) return "UserPlus";
      if (type.includes("success") || type.includes("complete")) return "CheckCircle";
      if (type.includes("alert") || type.includes("overdue")) return "AlertCircle";
      if (type.includes("reminder")) return "Clock";
      if (type.includes("update")) return "RefreshCw";
      if (type.includes("warning")) return "AlertTriangle";
      return "Bell";
    };

    // Load notifications
    const loadNotifications = async (page: number = 1, onlyUnread: boolean = false) => {
      if (!currentUserId.value) return null;

      return await notificationStore.fetchNotifications({
        user_id: currentUserId.value,
        page,
        limit: currentPageSize.value || pageSize,
        is_read: onlyUnread ? false : undefined,
        sort_by: "created_at",
        order: "desc",
      });
    };

    // Load more notifications (pagination)
    const loadMore = async () => {
      if (!hasMorePages.value || isLoading.value) return null;
      return await notificationStore.loadMoreNotifications({
        user_id: currentUserId.value!,
        page: currentPage.value + 1,
        limit: pageSize,
        sort_by: "created_at",
        order: "desc",
      });
    };

    // Navigate to specific page (for paginator)
    const goToPage = async (page: number, onlyUnread: boolean = false) => {
      if (!currentUserId.value || page < 1) return null;

      return await notificationStore.navigateToPage({
        user_id: currentUserId.value,
        page,
        limit: currentPageSize.value || pageSize,
        is_read: onlyUnread ? false : undefined,
        sort_by: "created_at",
        order: "desc",
      });
    };

    // Update page size and reload
    const updatePageSize = async (newPageSize: number) => {
      if (!currentUserId.value || newPageSize < 1) return null;

      return await notificationStore.navigateToPage({
        user_id: currentUserId.value,
        page: 1, // Reset to first page when changing page size
        limit: newPageSize,
        sort_by: "created_at",
        order: "desc",
      });
    };

    // Load unread count
    const loadUnreadCount = async () => {
      if (!currentUserId.value) return null;

      return await notificationStore.fetchUnreadCount({
        user_id: currentUserId.value,
      });
    };

    // Mark single notification as read
    const markAsRead = async (notificationId: number) => {
      if (!currentUserId.value) return;

      await notificationStore.markAsRead({
        user_id: currentUserId.value,
        notification_ids: [notificationId],
      });
    };

    // Mark multiple notifications as read
    const markMultipleAsRead = async (notificationIds: number[]) => {
      if (!currentUserId.value || notificationIds.length === 0) return;

      await notificationStore.markAsRead({
        user_id: currentUserId.value,
        notification_ids: notificationIds,
      });
    };

    // Mark all notifications as read
    const markAllAsRead = async () => {
      if (!currentUserId.value) return;

      await notificationStore.markAllAsRead({
        user_id: currentUserId.value,
      });
    };

    // Delete notification
    const deleteNotification = async (notificationId: number) => {
      if (!currentUserId.value) return;

      await notificationStore.deleteNotification({
        user_id: currentUserId.value,
        notification_id: notificationId,
      });
    };

    // Handle notification click
    const handleNotificationClick = async (notification: Notification) => {
      // Mark as read if not already
      if (!notification.is_read) {
        await markAsRead(notification.id);
      }

      // Navigate to notification
      await navigateToNotification(notification);
    };

    // Auto-load on mount if enabled
    if (autoLoad || autoLoadUnreadCount) {
      onMounted(async () => {
        if (currentUserId.value) {
          if (autoLoad) {
            await loadNotifications();
          }
          if (autoLoadUnreadCount) {
            await loadUnreadCount();
          }
        }
      });
    }

    // Watch for user changes
    watch(currentUserId, async (newUserId, oldUserId) => {
      if (newUserId && newUserId !== oldUserId) {
        notificationStore.clearNotifications();
        if (autoLoad) {
          await loadNotifications();
        }
        if (autoLoadUnreadCount) {
          await loadUnreadCount();
        }
      }
    });

    return {
      // State
      isLoading,
      error,
      notifications,
      unreadCount,
      currentPage,
      totalPages,
      totalNotifications,
      hasMorePages,
      paginationMode,
      pageSize: currentPageSize,
      // Computed
      getUnreadNotifications,
      getReadNotifications,
      // Actions
      loadNotifications,
      loadMore,
      goToPage,
      updatePageSize,
      loadUnreadCount,
      markAsRead,
      markMultipleAsRead,
      markAllAsRead,
      deleteNotification,
      handleNotificationClick,
      syncNotifications: notificationStore.syncNotifications,
      startPolling: notificationStore.startPolling,
      stopPolling: notificationStore.stopPolling,
      // Helpers
      getCategoryStyle,
      getNotificationIcon,
      // Navigation
      navigateToNotification,
    };
  }

  // Local state management when useStore = false
  const notifications = ref<Notification[]>([]);
  const unreadCount = ref<number>(0);
  const currentPage = ref<number>(1);
  const totalPages = ref<number>(1);
  const totalNotifications = ref<number>(0);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Computed
  const hasMorePages = computed(() => currentPage.value < totalPages.value);
  const getUnreadNotifications = computed(() =>
    notifications.value.filter((n) => !n.is_read)
  );
  const getReadNotifications = computed(() =>
    notifications.value.filter((n) => n.is_read)
  );

  // Category helpers
  const getCategoryStyle = (type: string) => {
    if (type.includes("assignment")) return "blue";
    if (type.includes("success") || type.includes("complete")) return "green";
    if (type.includes("alert") || type.includes("overdue")) return "red";
    if (type.includes("reminder")) return "orange";
    if (type.includes("update")) return "yellow";
    if (type.includes("info")) return "gray";
    if (type.includes("warning")) return "amber";
    return "gray";
  };

  const getNotificationIcon = (type: string) => {
    if (type.includes("assignment")) return "UserPlus";
    if (type.includes("success") || type.includes("complete")) return "CheckCircle";
    if (type.includes("alert") || type.includes("overdue")) return "AlertCircle";
    if (type.includes("reminder")) return "Clock";
    if (type.includes("update")) return "RefreshCw";
    if (type.includes("warning")) return "AlertTriangle";
    return "Bell";
  };

  // Load notifications
  const loadNotifications = (page: number = 1, onlyUnread: boolean = false) =>
    performAsyncAction(async () => {
      if (!currentUserId.value) return null;

      const response = await notificationList({
        user_id: currentUserId.value,
        page,
        limit: pageSize,
        is_read: onlyUnread ? false : undefined,
        sort_by: "created_at",
        order: "desc",
      });

      if (response.data?.data) {
        if (page === 1) {
          notifications.value = response.data.data;
        } else {
          notifications.value.push(...response.data.data);
        }
        totalNotifications.value = response.data.total;
        totalPages.value = response.data.total_page;
        currentPage.value = page;
      }
      return response.data;
    });

  // Load more notifications (pagination)
  const loadMore = async () => {
    if (!hasMorePages.value || isLoading.value) return null;
    return await loadNotifications(currentPage.value + 1);
  };

  // Navigate to specific page (for paginator)
  const goToPage = (page: number, onlyUnread: boolean = false) =>
    performAsyncAction(async () => {
      if (!currentUserId.value || page < 1) return null;

      const response = await notificationList({
        user_id: currentUserId.value,
        page,
        limit: pageSize,
        is_read: onlyUnread ? false : undefined,
        sort_by: "created_at",
        order: "desc",
      });

      if (response.data?.data) {
        // Replace data for page navigation
        notifications.value = response.data.data;
        totalNotifications.value = response.data.total;
        totalPages.value = response.data.total_page;
        currentPage.value = page;
      }
      return response.data;
    });

  // Update page size and reload
  const updatePageSize = (newPageSize: number) =>
    performAsyncAction(async () => {
      if (!currentUserId.value || newPageSize < 1) return null;

      const response = await notificationList({
        user_id: currentUserId.value,
        page: 1, // Reset to first page when changing page size
        limit: newPageSize,
        sort_by: "created_at",
        order: "desc",
      });

      if (response.data?.data) {
        notifications.value = response.data.data;
        totalNotifications.value = response.data.total;
        totalPages.value = response.data.total_page;
        currentPage.value = 1;
      }
      return response.data;
    });

  // Load unread count
  const loadUnreadCount = () =>
    performAsyncAction(async () => {
      if (!currentUserId.value) return null;

      const response = await notificationUnread({
        user_id: currentUserId.value,
      });
      if (response.data) {
        unreadCount.value = response.data.unread_count;
      }
      return response.data;
    });

  // Add notification
  const addNotification = (req: NotificationAddRequest) =>
    performAsyncAction(async () => {
      const response = await notificationAdd(req);
      if (response.data) {
        notifications.value.unshift(response.data);
        if (!response.data.is_read) {
          unreadCount.value++;
        }
      }
      return response.data;
    });

  // Mark single notification as read
  const markAsRead = (notificationId: number) =>
    performAsyncAction(async () => {
      if (!currentUserId.value) return;

      await notificationMarkAsRead({
        user_id: currentUserId.value,
        notification_ids: [notificationId],
      });

      const notification = notifications.value.find((n) => n.id === notificationId);
      if (notification && !notification.is_read) {
        notification.is_read = true;
        notification.read_at = new Date().toISOString();
        unreadCount.value--;
      }
    });

  // Mark multiple notifications as read
  const markMultipleAsRead = (notificationIds: number[]) =>
    performAsyncAction(async () => {
      if (!currentUserId.value || notificationIds.length === 0) return;

      await notificationMarkAsRead({
        user_id: currentUserId.value,
        notification_ids: notificationIds,
      });

      const now = new Date().toISOString();
      notificationIds.forEach((id) => {
        const notification = notifications.value.find((n) => n.id === id);
        if (notification && !notification.is_read) {
          notification.is_read = true;
          notification.read_at = now;
          unreadCount.value--;
        }
      });
    });

  // Mark all notifications as read
  const markAllAsRead = () =>
    performAsyncAction(async () => {
      if (!currentUserId.value) return;

      await notificationMarkAllAsRead({
        user_id: currentUserId.value,
      });

      const now = new Date().toISOString();
      notifications.value.forEach((notification) => {
        if (!notification.is_read) {
          notification.is_read = true;
          notification.read_at = now;
        }
      });
      unreadCount.value = 0;
    });

  // Delete notification
  const deleteNotification = (notificationId: number) =>
    performAsyncAction(async () => {
      if (!currentUserId.value) return;

      await notificationDelete({
        user_id: currentUserId.value,
        notification_id: notificationId,
      });

      const notification = notifications.value.find((n) => n.id === notificationId);
      if (notification && !notification.is_read) {
        unreadCount.value--;
      }
      notifications.value = notifications.value.filter((n) => n.id !== notificationId);
    });

  // Handle notification click
  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if not already
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }
  };

  // Handle WebSocket notification
  const handleWebSocketNotification = (notification: Notification) => {
    notifications.value.unshift(notification);
    if (!notification.is_read) {
      unreadCount.value++;
    }
  };

  // Auto-load on mount if enabled
  if (autoLoad || autoLoadUnreadCount) {
    onMounted(async () => {
      if (currentUserId.value) {
        if (autoLoad) {
          await loadNotifications();
        }
        if (autoLoadUnreadCount) {
          await loadUnreadCount();
        }
      }
    });
  }

  // Watch for user changes
  watch(currentUserId, async (newUserId, oldUserId) => {
    if (newUserId && newUserId !== oldUserId) {
      notifications.value = [];
      unreadCount.value = 0;
      currentPage.value = 1;
      totalPages.value = 1;
      totalNotifications.value = 0;

      if (autoLoad) {
        await loadNotifications();
      }
      if (autoLoadUnreadCount) {
        await loadUnreadCount();
      }
    }
  });

  return {
    // State
    isLoading,
    error,
    notifications,
    unreadCount,
    currentPage,
    totalPages,
    totalNotifications,
    hasMorePages,
    paginationMode,
    pageSize,
    // Computed
    getUnreadNotifications,
    getReadNotifications,
    // Actions
    loadNotifications,
    loadMore,
    goToPage,
    updatePageSize,
    loadUnreadCount,
    addNotification,
    markAsRead,
    markMultipleAsRead,
    markAllAsRead,
    deleteNotification,
    handleNotificationClick,
    handleWebSocketNotification,
    // Helpers
    getCategoryStyle,
    getNotificationIcon,
  };
}
