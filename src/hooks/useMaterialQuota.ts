import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  OperationMaterialAddRequest,
  OperationMaterialDeleteRequest,
  OperationMaterialGetRequest,
  OperationMaterialListRequest,
  OperationMaterialResponse,
  OperationMaterialUpdateRequest,
  BulkSetRequest,
  OperationMaterialSetItem,
} from "@/api/bcare-types-v2";
import { useMaterialQuotaStore } from "@/stores/material-quota-store";

interface UseMaterialQuotaOptions {
  autoLoad?: boolean;
  operationId?: number;
}

export default function useMaterialQuota(
  options: UseMaterialQuotaOptions = { autoLoad: true }
) {
  const materialQuotaStore = useMaterialQuotaStore();
  const {
    isLoading,
    error,
    materialQuotas,
    currentMaterialQuota,
    filteredMaterialQuotas
  } = storeToRefs(materialQuotaStore);

  // Local state for filtered material quotas, initialized as empty
  const localFilteredMaterialQuotas = shallowRef<OperationMaterialResponse[]>([]);

  // Computed property for material quota count
  const materialQuotaCount = computed(() => materialQuotaStore.getMaterialQuotaCount);

  // Get material quotas for a specific operation
  const getMaterialQuotasForOperation = (operationId: number) => {
    return materialQuotaStore.getMaterialQuotasByOperationId(operationId);
  };

  // Get material quotas for a specific material
  const getMaterialQuotasForMaterial = (materialId: number) => {
    return materialQuotaStore.getMaterialQuotasByMaterialId(materialId);
  };

  // Load all material quotas
  const loadMaterialQuotas = async () => {
    if (materialQuotaStore.hasCachedData) {
      materialQuotaStore.initializeFromCache();
    } else {
      await materialQuotaStore.fetchAllMaterialQuotas();
    }
    // Keep localFilteredMaterialQuotas empty after loading
    localFilteredMaterialQuotas.value = [];
  };

  // Load material quotas for a specific operation
  const loadMaterialQuotasForOperation = async (operationId: number) => {
    await materialQuotaStore.fetchMaterialQuotasByOperation(operationId);
  };

  // Load material quotas for multiple operations
  const loadMaterialQuotasForOperations = async (operationIds: number[]) => {
    await materialQuotaStore.fetchMaterialQuotasByOperations(operationIds);
  };

  // Remove material quotas for a specific operation
  const removeMaterialQuotas = (operationId: number) => {
    materialQuotaStore.removeMaterialQuotas(operationId);
  };

  // Remove material quotas for multiple operations
  const removeMaterialQuotasForOperations = (operationIds: number[]) => {
    materialQuotaStore.removeMaterialQuotasForOperations(operationIds);
  };

  // Auto-load material quotas if autoLoad is true
  if (options.autoLoad) {
    onMounted(async () => {
      if (options.operationId) {
        await loadMaterialQuotasForOperation(options.operationId);
      } else {
        await loadMaterialQuotas();
      }
    });
  }

  // Add a new material quota
  const addMaterialQuota = async (req: OperationMaterialAddRequest) => {
    return await materialQuotaStore.addMaterialQuota(req);
  };

  // Delete a material quota
  const deleteMaterialQuota = async (req: OperationMaterialDeleteRequest) => {
    await materialQuotaStore.deleteMaterialQuota(req);
  };

  // Get a specific material quota
  const getMaterialQuota = async (req: OperationMaterialGetRequest) => {
    return await materialQuotaStore.getMaterialQuota(req);
  };

  // List material quotas with filtering
  const listMaterialQuotas = async (req: OperationMaterialListRequest) => {
    const response = await materialQuotaStore.listMaterialQuotas(req);
    if (response?.operation_materials) {
      localFilteredMaterialQuotas.value = response.operation_materials;
    }
    return response;
  };

  // Update a material quota
  const updateMaterialQuota = async (req: OperationMaterialUpdateRequest) => {
    return await materialQuotaStore.updateMaterialQuota(req);
  };

  // Bulk set material quotas for an operation
  const bulkSetMaterialQuotas = async (req: BulkSetRequest) => {
    await materialQuotaStore.bulkSetMaterialQuotas(req);
  };

  // Get a material quota by ID
  const getMaterialQuotaById = (id: number) => {
    return materialQuotaStore.getMaterialQuotaById(id);
  };

  // Search material quotas
  const searchMaterialQuotas = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    localFilteredMaterialQuotas.value = materialQuotaStore.materialQuotas.filter(
      (materialQuota) => {
        // You can extend this search to include material name, operation name, etc.
        // For now, searching by ID and quantity
        return (
          materialQuota.id.toString().includes(lowercaseQuery) ||
          materialQuota.quantity.toString().includes(lowercaseQuery)
        );
      }
    );
  };

  // Sort material quotas
  const sortMaterialQuotas = (
    key: keyof OperationMaterialResponse,
    order: "asc" | "desc" = "asc"
  ) => {
    localFilteredMaterialQuotas.value = [...localFilteredMaterialQuotas.value].sort((a, b) => {
      const aValue = a[key];
      const bValue = b[key];

      // Handle null/undefined values - put them at the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      // Handle string comparison (case-insensitive for text fields)
      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
        return order === "asc" ? comparison : -comparison;
      }

      // Handle numeric comparison
      if (typeof aValue === "number" && typeof bValue === "number") {
        return order === "asc" ? aValue - bValue : bValue - aValue;
      }

      // Fallback to string comparison for other types
      const aStr = String(aValue);
      const bStr = String(bValue);
      const comparison = aStr.localeCompare(bStr);
      return order === "asc" ? comparison : -comparison;
    });
  };

  // Paginate material quotas
  const paginateMaterialQuotas = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return localFilteredMaterialQuotas.value.slice(startIndex, startIndex + pageSize);
  };

  // Helper function to create bulk set items from material quotas
  const createBulkSetItems = (materialQuotas: OperationMaterialResponse[]): OperationMaterialSetItem[] => {
    return materialQuotas.map((mq) => ({
      material_id: mq.material_id,
      quantity: mq.quantity,
    }));
  };

  return {
    isLoading,
    error,
    materialQuotas, // Exposed material quotas list from the store
    currentMaterialQuota,
    filteredMaterialQuotas,
    localFilteredMaterialQuotas,
    materialQuotaCount,
    loadMaterialQuotas,
    loadMaterialQuotasForOperation,
    loadMaterialQuotasForOperations,
    removeMaterialQuotas,
    removeMaterialQuotasForOperations,
    addMaterialQuota,
    deleteMaterialQuota,
    getMaterialQuota,
    listMaterialQuotas,
    updateMaterialQuota,
    bulkSetMaterialQuotas,
    getMaterialQuotaById,
    getMaterialQuotasForOperation,
    getMaterialQuotasForMaterial,
    searchMaterialQuotas,
    sortMaterialQuotas,
    paginateMaterialQuotas,
    createBulkSetItems,
  };
}
