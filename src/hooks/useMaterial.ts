import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  Material,
  MaterialAddRequest,
  MaterialDeleteRequest,
  MaterialGetRequest,
  MaterialListRequest,
  MaterialUpdateRequest,
} from "@/api/bcare-types-v2";
import { useMaterialStore } from "@/stores/material-store-v2";

interface UseMaterialOptions {
  autoLoad?: boolean;
}

export default function useMaterial(options: UseMaterialOptions = { autoLoad: true }) {
  const materialStore = useMaterialStore();
  const { isLoading, error, materials } = storeToRefs(materialStore);

  // Local state for filtered materials, initialized as empty
  const filteredMaterials = shallowRef<Material[]>([]);

  // Computed property for material count
  const materialCount = computed(() => materialStore.getMaterialCount);

  // Load all materials
  const loadMaterials = async () => {
    if (materialStore.hasCachedData) {
      materialStore.initializeFromCache();
    } else {
      await materialStore.fetchAllMaterials();
    }
    // Keep filteredMaterials empty after loading
    filteredMaterials.value = [];
  };

  // Auto-load materials if autoLoad is true
  if (options.autoLoad) {
    onMounted(() => {
      loadMaterials();
    });
  }

  // Add a new material
  const addMaterial = async (req: MaterialAddRequest) => {
    return await materialStore.addMaterial(req);
  };

  // Delete a material
  const deleteMaterial = async (req: MaterialDeleteRequest) => {
    await materialStore.deleteMaterial(req);
  };

  // Get a specific material
  const getMaterial = async (req: MaterialGetRequest) => {
    return await materialStore.getMaterial(req);
  };

  // List materials with filtering
  const listMaterials = async (req: MaterialListRequest) => {
    const response = await materialStore.listMaterials(req);
    if (response?.materials) {
      filteredMaterials.value = response.materials;
    }
    return response;
  };

  // Update a material
  const updateMaterial = async (req: MaterialUpdateRequest) => {
    return await materialStore.updateMaterial(req);
  };

  // Get a material by ID
  const getMaterialById = (id: number) => {
    return materialStore.getMaterialById(id);
  };

  // Get multiple materials by their IDs
  const getMaterialsByIds = (ids: number[]) => {
    return ids.map((id) => materialStore.getMaterialById(id)).filter(Boolean) as Material[];
  };

  // Search materials
  const searchMaterials = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    filteredMaterials.value = materialStore.materials.filter(
      (material) =>
        material.name.toLowerCase().includes(lowercaseQuery) ||
        (material.code && material.code.toLowerCase().includes(lowercaseQuery)) ||
        material.unit.toLowerCase().includes(lowercaseQuery),
    );
  };

  // Sort materials
  const sortMaterials = (key: keyof Material, order: "asc" | "desc" = "asc") => {
    filteredMaterials.value = [...filteredMaterials.value].sort((a, b) => {
      const aValue = a[key];
      const bValue = b[key];

      // Handle null/undefined values - put them at the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      // Handle string comparison (case-insensitive for text fields)
      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
        return order === "asc" ? comparison : -comparison;
      }

      // Handle numeric comparison
      if (typeof aValue === "number" && typeof bValue === "number") {
        return order === "asc" ? aValue - bValue : bValue - aValue;
      }

      // Fallback to string comparison for other types
      const aStr = String(aValue);
      const bStr = String(bValue);
      const comparison = aStr.localeCompare(bStr);
      return order === "asc" ? comparison : -comparison;
    });
  };

  // Paginate materials
  const paginateMaterials = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredMaterials.value.slice(startIndex, startIndex + pageSize);
  };

  const getMaterialNameById = (id: number): string => {
    const material = materialStore.getMaterialById(id);
    return material ? material.name : "Unknown";
  };

  return {
    isLoading,
    error,
    materials, // Exposed materials list from the store
    filteredMaterials,
    materialCount,
    loadMaterials,
    addMaterial,
    deleteMaterial,
    getMaterial,
    listMaterials,
    updateMaterial,
    getMaterialById,
    getMaterialsByIds,
    searchMaterials,
    sortMaterials,
    paginateMaterials,
    getMaterialNameById,
  };
}
