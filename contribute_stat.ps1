# Define multiple authors
$authors = @("*")
$since = "30 days ago"

# Define paths to ignore (supports wildcards)
$ignorePaths = @(
    "*.lock",
    "*.lockb",
    "package-lock.json",
    "bun.lockb",
    "dist/*",
    "node_modules/*",
    ".git/*",
    "*.min.js",
    "*.min.css"
    "**/bcare-types-v2.ts"
    "**/bcare-v2.ts"
)

# Initialize counters
$totalAdded = 0
$totalRemoved = 0

# Function to check if a file path should be ignored
function Should-IgnorePath {
    param($filePath)

    foreach ($pattern in $ignorePaths) {
        if ($filePath -like $pattern) {
            return $true
        }
    }
    return $false
}

# Loop through each author to get their git stats
foreach ($author in $authors) {
    # Added --all flag to check all branches
    $output = git log --all --author="$author" --since="$since" --numstat --pretty="%H"

    $output | ForEach-Object {
        if ($_ -match "^(\d+)\s+(\d+)\s+(.+)$") {
            $added = [int]$Matches[1]
            $removed = [int]$Matches[2]
            $filePath = $Matches[3]

            # Only count if file is not in ignore list
            if (-not (Should-IgnorePath $filePath)) {
                $totalAdded += $added
                $totalRemoved += $removed
            }
        }
    }
}

# Calculate total changes
$totalChanges = $totalAdded + $totalRemoved

# Display results
Write-Host "Total changes for authors: $($authors -join ', ')"
Write-Host "Time period: since $since"
Write-Host "Added: $totalAdded, Removed: $totalRemoved, Total changes: $totalChanges"
