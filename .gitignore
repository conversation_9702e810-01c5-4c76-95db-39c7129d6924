# Logs
logs
*~
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.eslintcache
.windsurfrules
.windsurf/*
windsurf/
.guides/*
guides/
.cursor/*
cursor/
TASKS.md
.roo/*
roo/
scripts/
.roomodes
.taskmasterconfig

# env
.env.codiumai
.env
.codiumai/local_index.json

.qodo

.qodo
.aider*

.qodo

# Added by <PERSON> Task Master
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.vscode
# OS specific
# Task files
tasks.json
tasks/ 
.roo/
.taskmaster/
