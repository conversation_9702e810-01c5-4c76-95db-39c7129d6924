#!/bin/bash

# Define multiple authors
authors=("vhoanglam" "vuhoanglam")
since="14 days ago"

# Initialize counters
totalAdded=0
totalRemoved=0

# Loop through each author to get their git stats
for author in "${authors[@]}"; do
    # Added --all flag to check all branches
    output=$(git log --all --author="$author" --since="$since" --numstat --pretty="%H")
    
    while IFS= read -r line; do
        if [[ $line =~ ^([0-9]+)[[:space:]]+([0-9]+)[[:space:]]+ ]]; then
            totalAdded=$((totalAdded + ${BASH_REMATCH[1]}))
            totalRemoved=$((totalRemoved + ${BASH_REMATCH[2]}))
        fi
    done <<< "$output"
done

# Calculate total changes
totalChanges=$((totalAdded + totalRemoved))

# Display results
echo "Total changes for authors: $(IFS=', '; echo "${authors[*]}")"
echo "Time period: since $since"
echo "Added: $totalAdded, Removed: $totalRemoved, Total changes: $totalChanges"