import { PrimeVueResolver } from "@primevue/auto-import-resolver";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { visualizer } from "rollup-plugin-visualizer";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv, PluginOption } from "vite";
import { VitePWA } from "vite-plugin-pwa";
import vueDevTools from "vite-plugin-vue-devtools";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// HTML Transform Plugin to inject environment variables
// Performance: Minimal impact - only processes index.html once during build
function htmlTransformPlugin(env: Record<string, string>) {
  return {
    name: 'html-transform',
    transformIndexHtml: {
      order: 'pre' as const, // Modern API: use 'order' instead of deprecated 'enforce'
      handler(html: string) {
        try {
          // Default values for fallback - using generic dump logo
          const favicon = env.VITE_APP_FAVICON || '/default-logo.svg';
          const appleIcon = env.VITE_APP_APPLE_TOUCH_ICON || '/default-logo.svg';
          const title = env.VITE_APP_TITLE || 'Dental Care System';
          const description = env.VITE_APP_META_DESCRIPTION || 'Professional Dental Management System';

          // Escape special characters to prevent XSS
          const escapeHtml = (str: string) => str.replace(/[&<>"']/g, (match) => {
            const escapeMap: Record<string, string> = {
              '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            };
            return escapeMap[match];
          });

          // More precise and safer regex patterns with error handling
          let result = html;



          // Replace favicon - more flexible pattern
          result = result.replace(
            /(<link[^>]*rel="icon"[^>]*href=")[^"]*(")/gi,
            `$1${favicon}$2`
          );

          // Also handle favicon without quotes or different order
          result = result.replace(
            /(href=")[^"]*("[^>]*rel="icon")/gi,
            `$1${favicon}$2`
          );

          // Replace apple-touch-icon
          result = result.replace(
            /(<link[^>]*rel="apple-touch-icon"[^>]*href=")[^"]*(")/gi,
            `$1${appleIcon}$2`
          );

          // Replace title
          result = result.replace(
            /(<title>)[^<]*(<\/title>)/gi,
            `$1${escapeHtml(title)}$2`
          );

          // Replace meta description
          result = result.replace(
            /(<meta[^>]*name="description"[^>]*content=")[^"]*(")/gi,
            `$1${escapeHtml(description)}$2`
          );



          return result;
        } catch (error) {
          console.warn('HTML Transform Plugin error:', error);
          return html; // Return original HTML if transformation fails
        }
      }
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');

  return {
    server: {
      host: "localhost",
      port: 7777,
      open: true,
      proxy: {},
    },
    build: {
      commonjsOptions: {
        include: ["tailwind.config.js", "node_modules/**"],
      },
    },
    optimizeDeps: {
      include: ["tailwind-config"],
    },
    plugins: [
      htmlTransformPlugin(env),
      vue(),
      vueDevTools({
        launchEditor: "webstorm",
      }),
      VitePWA({
        registerType: "autoUpdate",
        includeAssets: ["favicon.ico", "default-logo.svg", "updental-logo.svg", "robots.txt"],
        manifest: {
          name: env.VITE_APP_TITLE || "Dental Care System",
          short_name: env.VITE_APP_BRAND_NAME || "DentalCare",
          description: env.VITE_APP_DESC || "Professional Dental Management System",
          icons: [
            {
              src: env.VITE_APP_PWA_ICON || "default-logo.svg",
              sizes: "any",
              type: "image/svg+xml",
              purpose: "any",
            },
            {
              src: "pwa-192x192.png",
              sizes: "192x192",
              type: "image/png",
            },
            {
              src: "pwa-512x512.png",
              sizes: "512x512",
              type: "image/png",
            },
          ],
        },
        workbox: {
          globPatterns: ["**/*.{js,css,html,ico,png,svg}"],
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/up\.blazy\.vn\/api/,
              handler: "NetworkFirst",
              options: {
                cacheName: "api-cache",
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24, // 1 day
                },
              },
            },
          ],
        },
      }),
      visualizer({
        filename: "dist/stats.html",
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: "treemap",
      }) as PluginOption,
      Components({
        resolvers: [PrimeVueResolver()],
        dts: true,
        dirs: ["src/components", "src/volt"],
      }),
      AutoImport({
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.vue\.[tj]sx?\?vue/, /\.md$/],
        imports: ["vue", "vue-router", "@vueuse/core"],
        viteOptimizeDeps: true,
        dts: "./auto-imports.d.ts",
      }),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "tailwind-config": path.resolve(__dirname, "./tailwind.config.js"),
      },
    },
  };
});
