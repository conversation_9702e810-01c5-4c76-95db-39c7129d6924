---
description: 
globs: 
alwaysApply: false
---
# Performance Optimization

## Code Splitting

Use dynamic imports for route-level code splitting
Lazy load components that aren't immediately visible
Use Webpack/Vite chunks for vendor code

## Rendering Optimization

- Use v-once for static content
- Use v-memo for memoizing template parts
- Use shallowRef for large objects that don't need deep reactivity
  Avoid unnecessary watchers and computed properties

## Asset Optimization

Optimize images (WebP format, appropriate sizes)
Use SVGs for icons and simple graphics
Lazy load images and media
Use appropriate caching strategies

## Monitoring and Metrics

Track Core Web Vitals (LCP, FID, CLS)
Monitor bundle size
Use Vue DevTools for performance profiling
Set performance budgets

## Example Performance Patterns

<!-- Lazy loading components -->
<script setup>
import { defineAsyncComponent } from 'vue';

const HeavyComponent = defineAsyncComponent(() =>
  import('@/components/HeavyComponent.vue')
);
</script>

<!-- Optimizing rendering -->
<template>
  <div v-once>Static content that never changes</div>

  <div v-memo="[item.id]">
    Content that only changes when item.id changes
  </div>
</template>
