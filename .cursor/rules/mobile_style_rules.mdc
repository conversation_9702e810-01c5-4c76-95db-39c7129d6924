---
description: 
globs: 
alwaysApply: false
---
---
description: Mobile UI/UX Design and Styling Guidelines, prioritizing usability, performance, and consistency on smaller screens, derived from the main style rules.
globs: ["src/**/*.{vue,ts,css,scss}"]
alwaysApply: true
---

- **Core Mobile Styling Philosophy**

  - **Tailwind CSS First, Mobile First**: Utilize Tailwind CSS utility classes directly in Vue component templates, designing for mobile screens first and then scaling up using responsive prefixes (`md:`, `lg:`, etc.).
  - **PrimeVue for Components**: Leverage PrimeVue for complex UI components, ensuring they are configured and styled for optimal mobile interaction.
  - **PrimeVue Passthrough (PT) for Customization**: Use Passthrough (PT) to apply Tailwind CSS classes to PrimeVue components, tailoring them for mobile without overriding global CSS.
  - **No Scoped Styles**: Maintain this rule to ensure styling consistency and leverage Tailwind's strengths.
  - **Screen Real Estate Efficiency**: Be mindful of the limited screen space. Prioritize essential content and actions. Use compact layouts where appropriate, without sacrificing clarity or touchability.
  - **Consistency**: Maintain a consistent visual style and user interaction patterns across all mobile views.

- **PrimeVue on Mobile**

  - **Component Usage**: Choose PrimeVue components that offer good mobile experiences (e.g., `Drawer` from bottom, `SelectButton` for simple choices).
    - **Respect Default Component Padding**:
    - **Avoid Overriding Base Padding**: For standard PrimeVue components like `Button`, `InputText`, etc., avoid arbitrarily changing their default padding (`p-button-sm`, `p-button-md`, theme-level padding for inputs) unless absolutely necessary for a specific layout requirement not achievable via component props or standard Tailwind spacing utilities around the component.
    - **Rationale**: PrimeVue components are often designed with balanced padding for various sizes. Overriding them globally or too frequently can lead to inconsistencies and negate the benefits of the component library's theming.
    - **When to Customize**: Customization is acceptable using PT when ad
  - **Passthrough (PT) for Mobile Adaptation**:

    - Adapt component parts specifically for mobile (e.g., sizing, spacing, visibility of elements within a component) using PT.
    - Example: A `Drawer` might have `h-[95vh]` when `position="bottom"` for mobile, as previously discussed.

    ```typescript
    // ✅ DO: Example of PT adapting Drawer for mobile bottom position
    // const drawerPT = {
    //   root: ({ props }) => ({
    //     class: [
    //       'border-none shadow-xl',
    //       { 'h-[85vh]': props.position === 'bottom' } // Specific to mobile bottom drawer
    //     ]
    //   }),
    //   content: { class: 'p-4 overflow-y-auto' } // Ensure scrollability
    // };
    ```

- **Tailwind CSS for Mobile**

  - **Utility-First & Inline Usage**: Continue applying Tailwind classes directly for responsive and mobile-first styling.
  - **Readability**: Group related elements or use line breaks in templates if class lists become very long, especially when dealing with multiple responsive prefixes.
  - **Configuration (`tailwind.config.js`)**: Ensure theme extensions (colors, fonts, spacing) support clear mobile UIs.

- **Mobile Spacing System** (Leveraging existing Tailwind scale)

  - **Base Scale Adherence**: Continue using Tailwind's spacing scale (`p-1` to `p-16`, etc.).
  - **Touch Targets are Paramount**:
    - Interactive elements (buttons, list items, inputs) MUST have ample touch targets. Aim for a minimum size of `w-11 h-11` (44px by 44px, common guideline) or provide equivalent padding (e.g., `p-3`).
    ```vue
    <!-- ✅ DO: Adequate touch target for an icon button -->
    <Button
      icon="pi pi-plus"
      class="w-12 h-12 p-3 flex items-center justify-center"
    />
    <!-- ✅ DO: List item with good touch area -->
    <div class="p-3 flex items-center">...</div>
    ```
  - **Compact Layouts, Clear Separation**:
    - Use smaller gaps (`gap-2`, `gap-3`, `gap-4`) between elements to save space, but ensure distinct elements are clearly separated.
    - Use margins (`mb-2`, `mb-3`, `mb-4`) for vertical spacing between blocks of content.
  - **Form Spacing**:
    - Labels typically stacked above inputs.
    - Consistent vertical rhythm using `gap-4` or `mb-4` between form fields.

- **Mobile Visual Design**

  - **Clear Hierarchy**: Use typography, color, and spacing to establish an unambiguous visual flow, guiding the user's eye to primary content and actions.
  - **Color Palette & Contrast**:
    - Ensure primary, secondary, and state colors (success, warning, danger) have sufficient contrast (WCAG AA) against their backgrounds, especially considering varied mobile lighting conditions.
  - **Typography**:
    - Font Family: `font-public-sans` (FZ Poppins).
    - Font Sizes: Ensure readability. Body text typically `text-sm` (0.875rem) or `text-base` (1rem). Avoid very small text.
    - Font Weights: Use weights to create hierarchy, but avoid excessive bolding that can clutter small screens.

- **Mobile Interaction Design**

  - **Navigation**:
    - **Common Patterns**: Implement intuitive mobile navigation like bottom navigation bars for primary destinations, or hamburger menus (`Drawer` component) for secondary/less frequent actions.
    - **Reachability**: Place primary navigation and frequent actions within easy thumb reach.
    ```vue
    <!-- ✅ DO: Conceptual Bottom Navigation -->
    <!-- <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-md h-16 flex justify-around items-center"> -->
    <!--   <NavItemMobile v-for="item in bottomNavItems" :item="item" /> -->
    <!-- </nav> -->
    ```
  - **Calls-to-Action (CTAs)**:
    - **Prominent & Clear**: Buttons should be easily identifiable.
    - **Thumb-Friendly Placement**: Often full-width at the bottom of a screen/form or within a sticky footer.
    - **Sufficient Size**: Apply generous padding (e.g., `py-3 px-4`) or ensure min-height.
    ```vue
    <!-- ✅ DO: Full-width button in a form footer -->
    <div class="p-4 sticky bottom-0 bg-white border-t">
      <Button label="Submit Application" class="w-full py-3" />
    </div>
    ```
  - **States (`hover:`, `focus:`, `active:`, `disabled:`)**: Provide clear visual feedback for all interactive states. On mobile, `active:` state is particularly important for immediate feedback on tap.

- **Mobile Layout & Responsiveness (Mobile-First ALWAYS)**

  - **Single-Column Layouts**: Prefer single-column layouts for the main content flow on mobile to simplify reading and interaction. Use multi-column layouts (e.g., `md:grid-cols-2`) only when scaling up to larger screens.
  - **Fluidity**: Use percentage-based widths (`w-full`, `w-1/2`) and flex/grid utilities.
  - **Content Prioritization**:
    - Strategically hide or defer loading of non-essential content on mobile.
    - Use accordions or tabs to manage secondary information without cluttering the initial view.
    ```vue
    <!-- ✅ DO: Show critical info, hide secondary on mobile -->
    <div>Main Content Visible on All Screens</div>
    <div class="hidden md:block">Additional Details for Larger Screens</div>
    ```
  - **Viewport Units**: Use `vh` (viewport height) and `vw` (viewport width) units judiciously for elements that need to scale relative to the screen (e.g., bottom sheet height `h-[95vh]`).

- **Mobile Forms**

  - **Layout**:
    - Inputs should typically be full-width (`w-full`).
    - Labels stacked above their respective input fields.
  - **Input Types**: Use appropriate HTML5 input types (`email`, `tel`, `number`, `date`) to trigger optimized mobile keyboards.
  - **Validation**: Clear, concise, inline validation messages displayed close to the field in error.

- **Accessibility on Mobile (WCAG)**

  - **Semantic HTML**: Crucial for screen readers.
  - **Touch Target Size & Spacing**: Re-emphasize from spacing section.
  - **Keyboard Navigability**: Ensure all interactive elements are focusable and operable with a keyboard (important for users with motor impairments or those using external keyboards).
  - **Alternative Text**: For all images.

- **User Feedback on Mobile**

  - **Toasts/Notifications**: Position thoughtfully (e.g., top of screen) to avoid obscuring critical UI elements or navigation.
  - **Loading Indicators**: Use subtle indicators. For longer operations, consider skeleton screens for better perceived performance.

- **Mobile Performance Optimization**

  - **Asset Optimization**: Aggressively optimize images (use WebP, responsive images with `<picture>` or `srcset`).
  - **Lazy Loading**:
    - **Components**: Use `defineAsyncComponent` for non-critical components.
    - **Images**: Use `loading="lazy"`.
  - **Code Splitting**: Keep initial bundle sizes small.
  - **Minimize Animations**: Use simple, performant CSS transitions. Avoid heavy JavaScript animations.
  - **Prioritize Above-the-Fold Content**: Ensure essential content loads and renders quickly.
