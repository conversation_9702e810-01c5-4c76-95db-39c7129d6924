---
description: 
globs: 
alwaysApply: false
---
---
description: Guidelines for UI/UX design, styling, and theming using PrimeVue and Tailwind CSS, including general design principles.
globs: ["src/**/*.{vue,ts,css,scss}"]
alwaysApply: true
---

- **Core Styling Philosophy**

  - **Tailwind CSS First**: Utilize Tailwind CSS utility classes directly in Vue component templates for styling. Avoid creating custom CSS classes or using `<style scoped>` where a Tailwind utility can achieve the same result. This promotes consistency and reduces CSS bundle size.
  - **PrimeVue for Components**: Leverage PrimeVue for complex UI components (data tables, modals, dropdowns, etc.).
  - **PrimeVue Passthrough (PT) for Customization**: When PrimeVue components need stylistic adjustments beyond what their props offer, use the [Passthrough](mdc:https:/primevue.org/passthrough) (PT) feature to apply Tailwind CSS classes directly to the internal elements of PrimeVue components. This is the preferred method over overriding PrimeVue's global CSS.
  - **No Scoped Styles**: Do not use `<style scoped>` in Vue components. All styling should be achieved via Tailwind CSS utilities or PrimeVue PT. If a very specific, non-reusable, complex style is needed that cannot be achieved with Tailwind or PT, consider a global utility class in a main CSS file as a last resort, but this should be rare.
  - **Consistency**: Maintain a consistent visual style and user experience across the application. Develop and adhere to a design system. Use consistent terminology throughout the interface. Maintain consistent positioning of recurring elements.

- **PrimeVue (`primevue: 4.2.5`, `@primevue/themes: 4.2.2`)**

  - **Component Usage**: Prefer standard PrimeVue components for common UI patterns to reduce cognitive load.
  - **Theming**: Utilize `@primevue/themes` for base theming. Customizations should primarily be done via Passthrough. (Ask user for active theme details if specific theme overrides are needed in rules).
  - **Passthrough (PT) for Customization**:

    - **Targeted Styling**: Use PT to apply Tailwind classes to specific parts of a PrimeVue component.
    - **Avoid Global Overrides**: Do not write global CSS to override PrimeVue component styles. PT offers more control and is less prone to breaking with library updates.
    - **Readability**: Keep PT configurations clean and readable. If a PT object becomes very large for a component, consider breaking it down or abstracting parts if appropriate.

- **Tailwind CSS (`tailwindcss: 3.4.10`)**

  - **Utility-First**: Embrace the utility-first approach. Compose complex styles from small, single-purpose utility classes.
  - **Inline Usage**: Apply Tailwind classes directly in the `class` attribute of HTML elements within Vue templates.
    ```vue
    <!-- ✅ DO: Apply Tailwind classes directly -->
    <div class="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4">
      <img class="h-12 w-12 rounded-full" src="..." alt="User Avatar">
      <div>
        <p class="text-lg font-semibold text-gray-900">Erin Lindford</p>
        <p class="text-sm text-gray-600">Customer Support</p>
      </div>
    </div>
    ```
  - **Readability**: For very long lists of Tailwind classes, consider line breaks in the template for better readability, or group related elements if it simplifies class application.
  - **Configuration (`tailwind.config.js`)**:
    - Extend the default Tailwind theme for project-specific colors, fonts, spacing, etc.
    - Avoid adding one-off custom CSS if a Tailwind configuration or utility can achieve the same.
  - **Plugins**:
    - `@tailwindcss/forms`: Use for basic form styling resets and ensuring forms adapt to different screen sizes.
    - `@tailwindcss/typography`: Use for styling HTML content generated from Markdown or a WYSIWYG editor (e.g., `prose` classes).
    - `tailwindcss-primeui`: Use for PrimeVue component styling integration.

- **Spacing System**

  - **Consistent Scale**: Use Tailwind's spacing scale for all spacing needs:
    - `p-0`, `m-0`: 0px
    - `p-1`, `m-1`: 0.25rem (4px)
    - `p-2`, `m-2`: 0.5rem (8px)
    - `p-3`, `m-3`: 0.75rem (12px)
    - `p-4`, `m-4`: 1rem (16px)
    - `p-5`, `m-5`: 1.25rem (20px)
    - `p-6`, `m-6`: 1.5rem (24px)
    - `p-8`, `m-8`: 2rem (32px)
    - `p-10`, `m-10`: 2.5rem (40px)
    - `p-12`, `m-12`: 3rem (48px)
    - `p-16`, `m-16`: 4rem (64px)
  - **Container Spacing**:
    ```vue
    <!-- ✅ DO: Container padding responsive -->
    <div class="p-4 md:p-6 lg:p-8">
      <!-- Content -->
    </div>
    ```
  - **Element Spacing**:
    - **Component Padding**: `p-2` to `p-4` for UI components
    - **Section Margins**: `mb-6` to `mb-12` between major sections
    - **Component Margins**: `mb-4` to `mb-6` between components
    - **Gap in Layouts**: Use `gap-4` to `gap-6` for consistent spacing in flex/grid layouts
    ```vue
    <!-- ✅ DO: Use gap for consistent spacing -->
    <div class="flex flex-col gap-4 md:gap-6">
      <Component />
      <Component />
    </div>
    ```
  - **Form Spacing**:
    - **Input Padding**: `p-2` to `p-3` for input fields
    - **Field Gap**: `gap-4` to `gap-6` between form fields
    - **Label Gap**: `gap-1` to `gap-2` between label and input
    ```vue
    <!-- ✅ DO: Consistent form spacing -->
    <form class="flex flex-col gap-6">
      <div class="flex flex-col gap-2">
        <label>Field Label</label>
        <InputText class="p-2" />
      </div>
    </form>
    ```

- **Visual Design**

  - **Hierarchy**: Establish a clear visual hierarchy to guide user attention.
  - **Color Palette**: Use Tailwind's color utilities based on the project's theme configuration:
    - Primary: `text-primary`, `bg-primary`, etc. (mapped to cyan.800/900)
    - Secondary: `text-secondary`, `bg-secondary`, etc.
    - Success: `text-success`, `bg-success`, etc. (mapped to teal.600)
    - Warning: `text-warning`, `bg-warning`, etc. (mapped to orange.600)
    - Danger: `text-danger`, `bg-danger`, etc. (mapped to red.700)
    - Neutral: `text-slate-500`, `bg-slate-100`, etc.
  - **Typography**: Use Tailwind's typography utilities:
    - Font Family: `font-public-sans` (FZ Poppins)
    - Font Sizes: `text-xs` (0.75rem), `text-sm` (0.875rem), `text-base` (1rem), `text-lg` (1.125rem), `text-xl` (1.25rem), etc.
    - Font Weights: `font-normal` (400), `font-medium` (500), `font-semibold` (600), `font-bold` (700)
    - Line Heights: `leading-4` through `leading-10`
  - **Contrast**: Use Tailwind's color utilities to maintain sufficient contrast (WCAG 2.1 AA standard).
  - **Overall Style**: Maintain consistent styling across the application using the defined color palette and typography.

- **Interaction Design**

  - **Navigation**: Create intuitive navigation patterns using Tailwind's flex and grid utilities.
    ```vue
    <!-- ✅ DO: Responsive navigation -->
    <nav class="flex flex-col md:flex-row justify-between items-center p-4">
      <div class="flex items-center">
        <Logo class="h-8 w-auto" />
      </div>
      <div class="hidden md:flex space-x-4">
        <NavLink v-for="item in navItems" :key="item.id" :item="item" />
      </div>
      <Button icon="pi pi-bars" class="md:hidden" @click="toggleMobileMenu" />
    </nav>
    ```
  - **Familiarity**: Use familiar UI components (leveraging PrimeVue) to reduce cognitive load.
  - **Calls-to-Action (CTAs)**: Provide clear CTAs to guide user behavior.
    ```vue
    <!-- ✅ DO: Clear primary and secondary actions -->
    <div class="flex gap-3 justify-end">
      <Button label="Cancel" class="p-button-text" />
      <Button label="Save" class="p-button-primary" />
    </div>
    ```
  - **Responsiveness**: Implement responsive design using Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`).
  - **Animations**: Use Tailwind's transition utilities (`transition`, `duration-150`, etc.) for subtle animations.
  - **States**: Use Tailwind's state variants (`hover:`, `focus:`, `active:`, `disabled:`) for interactive elements.
    ```vue
    <!-- ✅ DO: Clear interactive states -->
    <button
      class="bg-blue-500 hover:bg-blue-700 focus:ring-2 focus:ring-blue-300 active:bg-blue-800 disabled:opacity-50 text-white px-4 py-2 rounded"
    >
      Click Me
    </button>
    ```

- **Layout & Responsiveness (Mobile-First Design)**

  - **Approach**: Design for mobile devices first, then scale up using Tailwind's responsive prefixes.
    ```vue
    <!-- ✅ DO: Mobile-first approach with responsive prefixes -->
    <div class="flex flex-col md:flex-row">
      <div class="w-full md:w-1/2 p-2">Content A</div>
      <div class="w-full md:w-1/2 p-2">Content B</div>
    </div>
    ```
  - **Fluid Layouts**: Use Tailwind's percentage-based width classes (`w-full`, `w-1/2`, etc.) and flex/grid utilities.
  - **Media Queries**: Use Tailwind's responsive prefixes instead of custom media queries:
    - `sm:` (640px and up)
    - `md:` (768px and up)
    - `lg:` (1024px and up)
    - `xl:` (1280px and up)
    - `2xl:` (1536px and up)
  - **Touch Targets**: Ensure interactive elements have sufficient size using appropriate padding and sizing classes:
    ```vue
    <!-- ✅ DO: Adequate touch target size -->
    <button class="p-3 md:p-2">
      <i class="pi pi-check text-lg"></i>
    </button>
    ```
  - **Content Prioritization**: Use responsive utilities to show/hide or reorder content based on screen size:
    ```vue
    <!-- ✅ DO: Prioritize content on mobile -->
    <div class="hidden md:block">Secondary content</div>
    <div class="order-first md:order-last">Primary content on mobile</div>
    ```

- **Images and Media**

  - **Responsive Images**: Use Tailwind's sizing and responsive utilities:
    ```vue
    <!-- ✅ DO: Responsive image -->
    <img
      class="w-full md:w-auto max-w-full h-auto"
      src="..."
      alt="Description"
    />
    ```
  - **Lazy Loading**: Implement lazy loading for images using the `loading="lazy"` attribute.
  - **Embedded Media**: Make embedded media responsive with Tailwind:
    ```vue
    <!-- ✅ DO: Responsive iframe -->
    <div class="relative w-full pt-[56.25%]">
      <iframe class="absolute top-0 left-0 w-full h-full" src="..." frameborder="0" allowfullscreen></iframe>
    </div>
    ```

- **Forms**

  - **Layout**: Use Tailwind's flex and grid utilities for responsive form layouts:
    ```vue
    <!-- ✅ DO: Responsive form layout -->
    <form class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
      <div class="flex flex-col gap-2">
        <label class="text-sm font-medium text-gray-700">First Name</label>
        <InputText class="w-full" />
      </div>
      <!-- More fields -->
    </form>
    ```
  - **Input Types**: Use appropriate HTML5 input types with PrimeVue components.
  - **Validation**: Implement inline validation with PrimeVue and style error messages consistently:
    ```vue
    <!-- ✅ DO: Consistent error styling -->
    <div class="flex flex-col gap-2">
      <label>Email</label>
      <InputText :class="{'p-invalid': hasError}" />
      <small v-if="hasError" class="text-red-500 text-xs">Please enter a valid email</small>
    </div>
    ```

- **Accessibility (WCAG)**

  - **Guidelines**: Follow WCAG guidelines for web accessibility.
  - **Semantic HTML**: Use semantic HTML to enhance screen reader compatibility.
  - **Alternative Text**: Provide `alt` text for images and non-text content.
  - **Keyboard Navigability**: Ensure keyboard navigability using Tailwind's `focus:` and `focus-visible:` utilities.
  - **Testing**: Test with various assistive technologies.

- **User Feedback Mechanisms**
  - **Loading Indicators**: Use PrimeVue's ProgressSpinner or ProgressBar with Tailwind styling:
    ```vue
    <!-- ✅ DO: Loading state -->
    <div v-if="loading" class="flex justify-center p-4">
      <ProgressSpinner :pt="{ spinner: { class: 'text-primary' } }" />
    </div>
    ```
  - **Error Handling**: Provide clear error messages styled consistently with Tailwind.

- **Performance Optimization**

  - **Asset Optimization**: Optimize images and assets to minimize load times.
  - **Lazy Loading**: Implement lazy loading for non-critical resources.
  - **Code Splitting**: Use Vue's dynamic imports for component lazy loading:
    ```js
    // ✅ DO: Lazy load components
    const HeavyComponent = defineAsyncComponent(
      () => import("@/components/HeavyComponent.vue")
    );
    ```
  - **Core Web Vitals**: Monitor and optimize LCP, FID, CLS.
  - **Mobile Performance**: Optimize for mobile networks by minimizing bundle size and using appropriate image sizes.

- **Documentation & Process**

  - **Style Guide**: Maintain this `style_rules.mdc` as a comprehensive style guide.
  - **Design Patterns**: Document common design patterns and component usage.
  - **User Flows**: Create user flow diagrams for complex interactions.
  - **Asset Organization**: Keep design assets organized and accessible to the team.
  - **Testing and Iteration**: Conduct testing across devices and iterate based on feedback.

- **Staying Updated**

  - Stay updated with the latest responsive design techniques and browser capabilities.
  - Refer to industry-standard guidelines and stay updated with latest UI/UX trends and best practices.
